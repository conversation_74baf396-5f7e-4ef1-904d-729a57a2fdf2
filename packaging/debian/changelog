mixxx (2.5.1-1~jammy) jammy; urgency=medium

  * Build of 2.5.1

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  Sun, 27 Apr 2025 09:58:58 +0000

mixxx (2.5.0-1~jammy) jammy; urgency=medium

  * Build of 2.5.0

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  <PERSON><PERSON>, 24 Dec 2024 02:09:16 +0000

mixxx (2.4.2-1~focal) focal; urgency=medium

  * Build of 2.4.2

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  <PERSON><PERSON>, 26 Nov 2024 23:29:32 +0000

mixxx (2.4.1-1~focal) focal; urgency=medium

  * Build of 2.4.1

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  Wed, 08 May 2024 21:39:18 +0000

mixxx (2.4.0-1~focal) focal; urgency=medium

  * Build of 2.4.0

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  <PERSON>hu, 15 Feb 2024 23:55:01 +0000

mixxx (2.3.6-1~bionic) bionic; urgency=medium

  * Build of 2.3.6

 -- <PERSON><PERSON>-<PERSON> <<EMAIL>>  <PERSON><PERSON>, 15 Aug 2023 06:18:14 +0000

mixxx (2.3.5-1~bionic) bionic; urgency=medium

  * Build of 2.3.5

 -- RJ Skerry-<PERSON> <<EMAIL>>  Wed, 10 May 2023 20:28:25 +0000

mixxx (2.3.4-1~bionic) bionic; urgency=medium

  * Build of 2.3.4

 -- RJ Skerry-Ryan <<EMAIL>>  Fri, 03 Mar 2023 15:00:16 +0000

mixxx (2.3.3-1~bionic) bionic; urgency=medium

  * Build of 2.3.3

 -- RJ Skerry-Ryan <<EMAIL>>  Tue, 21 Jun 2022 21:24:47 +0000

mixxx (2.3.2-1~bionic) bionic; urgency=medium

  * Build of 2.3.2

 -- RJ Skerry-Ryan <<EMAIL>>  Sun, 30 Jan 2022 20:37:54 +0000

mixxx (2.3.1-1~bionic) bionic; urgency=medium

  * Build of 2.3.1

 -- RJ Skerry-Ryan <<EMAIL>>  Wed, 29 Sep 2021 19:08:14 +0000

mixxx (2.3.0-1~bionic) bionic; urgency=medium

  * Build of 2.3.0

 -- RJ Skerry-Ryan <<EMAIL>>  Mon, 28 Jun 2021 20:45:05 +0000

mixxx (2.2.4-0ubuntu3) bionic; urgency=medium

  * Bugfix release

 -- Daniel Schürmann <<EMAIL>>  Fri, 15 May 2020 23:22:20 +0200

mixxx (2.2.3-0ubuntu1) bionic; urgency=medium

  * Bugfix release

 -- Be <<EMAIL>>  Wed, 27 Nov 2019 01:02:42 +0200

mixxx (2.2.2-0ubuntu1) bionic; urgency=medium

  * Bugfix release

 -- Uwe Klotz <<EMAIL>>  Thu, 10 Aug 2019 08:50:58 +0200

mixxx (2.2.1-0ubuntu1) bionic; urgency=medium

  * Bugfix release

 -- Daniel Schürmann <<EMAIL>>  Thu, 22 Apr 2019 23:05:28 +0100

mixxx (2.2.0-0ubuntu1) bionic; urgency=medium

  * Bugfix release

 -- Daniel Schürmann <<EMAIL>>  Fri, 11 Jan 2019 23:26:08 +0100

mixxx (2.1.8-0ubuntu1) bionic; urgency=medium

  * New upstream release

 -- Daniel Schürmann <<EMAIL>>  Sun, 07 Apr 2019 12:14:46 +0200

mixxx (2.1.7-0ubuntu1) bionic; urgency=medium

  * New upstream release

 -- Daniel Schürmann <<EMAIL>>  Mon, 14 Jan 2019 00:08:23 +0100

mixxx (2.1.6-0ubuntu1) bionic; urgency=medium

  * New upstream release

 -- Daniel Schürmann <<EMAIL>>  Sun, 23 Dec 2018 11:01:23 +0100

mixxx (2.1.4-0ubuntu1) bionic; urgency=medium

  * New upstream release.

 -- Be <<EMAIL>>  Mon, 30 Aug 2018 07:47:00 +0500

mixxx (2.1.3-0ubuntu1) bionic; urgency=medium

  * New upstream release.

 -- Be <<EMAIL>>  Mon, 20 Aug 2018 12:53:15 +0500

mixxx (2.1.2-0ubuntu1) artful; urgency=medium

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Fri, 10 Aug 2018 13:11:21 +0300

mixxx (2.1.1-0ubuntu1) artful; urgency=medium

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Thu, 17 May 2018 20:42:25 +0200

mixxx (2.1.0-0ubuntu1) artful; urgency=medium

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Sun, 15 Apr 2018 15:06:39 -0700

mixxx (2.0.0-0ubuntu1) precise; urgency=medium

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Mon, 28 Dec 2015 09:15:53 -0800

mixxx (1.12.0-beta1-0ubuntu1) precise; urgency=low

  * New upstream beta release.

 -- RJ Ryan <<EMAIL>>  Tue, 28 Apr 2015 20:51:42 -0400

mixxx (1.11.0-0ubuntu1) lucid; urgency=low

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Thu, 09 May 2013 01:19:27 -0400

mixxx (1.11.0~beta1-0ubuntu1) lucid; urgency=low

  * New upstream beta release

 -- RJ Ryan <<EMAIL>>  Thu, 21 Jun 2012 15:41:22 -0400

mixxx (1.10.0-0ubuntu1) lucid; urgency=low

  * New upstream release.

 -- RJ Ryan <<EMAIL>>  Sat, 24 Dec 2011 23:28:30 -0500

mixxx (1.10.0-beta1-0ubuntu1) lucid; urgency=low

  * New upstream beta release
  * Bump Qt requirement to 4.6.0
  * Drop Karmic support

 -- RJ Ryan <<EMAIL>>  Wed, 08 Jun 2011 17:28:00 -0500

mixxx (1.9.0-0ubuntu1) karmic; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Thu, 17 Feb 2011 01:17:59 -0500

mixxx (1.8.0-0ubuntu1) karmic; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Thu,  5 August 2010 23:06:22 -0800

mixxx (1.8.0~beta2-0ubuntu1) karmic; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Fri,  18 June 2010 21:06:45 -0800

mixxx (1.8.0~beta1-0ubuntu1) karmic; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Tue,  2 February 2010 21:06:45 -0800

mixxx (1.7.2-0ubuntu1) intrepid; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Tue,  29 December 2009 21:09:12 -0800

mixxx (1.7.1-0ubuntu1) intrepid; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Tue,  13 October 2009 11:21:40 -0800

mixxx (1.7.0-0ubuntu1) intrepid; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Thu,  5 August 2009 11:07:35 -0800

mixxx (1.7.0~beta2-0ubuntu1) intrepid; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Sun,  19 July 2009 12:13:37 -0800

mixxx (1.7.0~beta1-0ubuntu1) intrepid; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Mon,  13 April 2009 13:43:37 -0500

mixxx (1.6.2-0ubuntu1) hardy; urgency=low

  * New upstream release - removes libDJConsole support (new driver from Hercules make this obsolete)

 -- Garth Dahlstrom <<EMAIL>>  Tue,  13 November 2008 01:45:00 -0500

mixxx (1.6.1-0ubuntu1) hardy; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Tue,  25 September 2008 15:45:00 -0800

mixxx (1.6.0-0ubuntu1) hardy; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Tue,  5 August 2008 22:38:00 -0500

mixxx (1.6.0~beta4-0ubuntu1) hardy; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Sun,  13 July 2008 22:38:00 -0500

mixxx (1.6.0~beta3-0ubuntu1) hardy; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Sun,  18 May 2008 13:56:00 -0500

mixxx (1.6.0~beta2-0ubuntu1) hardy; urgency=low

  * New upstream release
    - Improved ATI compatibility (LP: #93814) (LP: #116484) (LP: #124213)
    - Various stability improvements (LP: #72549)
    - Rewritten sound core (LP: #72610)
    - Improved mp3 decoding safety (LP: #122476)
  * Removed mixxx.desktop from debian dir, already upstream (rules updated
    accordingly)
  * Dropped useless main.qbas patch (no longer use qt3)
  * Dropped direct rendering patch (handled better in upstream now)

 -- Albert Santoni <<EMAIL>>  Tue,  10 Feb 2008 15:32:00 -0500

mixxx (1.6.0~beta1-1ubuntu2) hardy; urgency=low

  * debian/mixxx.desktop
    - Updated desktop file to reflect icon name change
      (LP: #189598)

 -- Joseph Jackson IV <<EMAIL>>  Wed, 06 Feb 2008 11:02:37 -0500

mixxx (1.6.0~beta1-1ubuntu1) hardy; urgency=low

  * Merge from debian unstable, remaining changes:
    - Update desktop file.
    - Update maintainer field as per spec.

 -- Luke Yelavich <<EMAIL>>  Thu, 03 Jan 2008 23:09:53 +1100

mixxx (1.6.0~beta1-1) unstable; urgency=low

  * New upstream release
  * debian/patched:
      - dropped typos.patch (fixed upstream)
  * debian/control:
      - build depend on libqt4-dev and scons

 -- Free Ekanayaka <<EMAIL>>  Thu, 27 Dec 2007 09:53:41 +0000

mixxx (1.6.0beta1-0ubuntu1) gutsy; urgency=low

  * New upstream release
  * DFSG changes now upstream, tag no longer needed

 -- Albert Santoni <<EMAIL>>  Tue,  20 Nov 2007 23:02:00 -0500

mixxx (1.5.2svn~20070807dfsg-0ubuntu1) gutsy; urgency=low

  * New upstream release

 -- Albert Santoni <<EMAIL>>  Wed,  8 Aug 2007 20:10:00 -0500

mixxx (1.5.0svn~20070130dfsg-0ubuntu1) feisty; urgency=low

  * New upstream release
  * Trimmed non-free files
  * Mixxx now depends on libportaudio2 (PortAudio-v19 instead of -v18)

 -- Albert Santoni <<EMAIL>>  Sun,  28 Jan 2007 19:50:20 -0500

mixxx (1.4.2-1.1ubuntu1) edgy; urgency=low

  * Re-sync with Debian
  * Drop Ubuntu GL/GLU fixes
  * Fix .desktop file (Emmet Hikory)

 -- Barry deFreese <<EMAIL>>  Wed,  2 Aug 2006 12:46:20 -0400

mixxx (1.4.2-1.1) unstable; urgency=high

  * Non-maintainer upload.
  * Replace unsatisfiable build-dependency on xlibmesa-glu-dev by
    libglu1-mesa-dev; fixes FTBFS. (Closes: #374590)

 -- Steinar H. Gunderson <<EMAIL>>  Wed, 28 Jun 2006 00:23:04 +0200

mixxx (1.4.2-1) unstable; urgency=low

  * Initial Release (closes: #227017).
  * Modified src/mixxx.pro to be happier on debian
  * Added #ifdef __vbrheadersdk__ to src/soundsourcemp3.c
  * Removed non-free files in lib/gplot and lib/vbrheadersdk
  * Converted png to xpm to add to menu icon, added .desktop
  * Disable directRendering check to avoid crash when Visual=Simple
  * Prevent latency slider to try setting the latency to 0
  * Correct a typo on 'rendering' in mixxxview.cpp

 -- Paul Brossier <<EMAIL>>  Sat, 13 Aug 2005 00:14:24 +0100
