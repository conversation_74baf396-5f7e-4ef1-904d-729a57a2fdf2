Source: mixxx
Section: @CPACK_DEBIAN_PACKAGE_SECTION@
Priority: @CPACK_DEBIAN_PACKAGE_PRIORITY@
Maintainer: @CPACK_PACKAGE_CONTACT@
Build-Depends: debhelper (>= 11),
               @CPACK_DEBIAN_PACKAGE_BUILD_DEPENDS_EXTRA@
               pkg-config,
               docbook-to-man,
               markdown,
               libglu1-mesa-dev,
               qtkeychain-qt6-dev,
               qt6-declarative-private-dev,
               qt6-base-private-dev,
               qt6-qpa-plugins,
               qml6-module-qt5compat-graphicaleffects,
               qml6-module-qtqml-workerscript,
               qml6-module-qtquick-controls,
               qml6-module-qtquick-layouts,
               qml6-module-qtquick-nativestyle,
               qml6-module-qtquick-shapes,
               qml6-module-qtquick-templates,
               qml6-module-qtquick-window,
               qml6-module-qt-labs-qmlmodels,
               libqt6core5compat6-dev,
               libqt6opengl6-dev,
               libqt6sql6-sqlite,
               libqt6svg6-dev,
               cmake (>= 3.13),
               libjack-dev,
               portaudio19-dev,
               libid3tag0-dev,
               libmad0-dev,
               libogg-dev,
               libsndfile1-dev,
               libasound2-dev,
               libavformat-dev,
               libvorbis-dev,
               libfaad-dev,
               libportmidi-dev,
               libtag1-dev,
               libshout-idjc-dev,
               libssl-dev,
               libprotobuf-dev,
               protobuf-compiler,
               libusb-1.0-0-dev,
               libchromaprint-dev,
               librubberband-dev,
               libopusfile-dev,
               libsqlite3-dev,
               libsoundtouch-dev,
               libhidapi-dev,
               libupower-glib-dev,
               liblilv-dev,
               libmodplug-dev,
               libmp3lame-dev,
               libebur128-dev,
               libwavpack-dev,
               libudev-dev,
               libmsgsl-dev,
# for running mixxx-test
               libgtest-dev,
               libgmock-dev,
               libbenchmark-dev,
               xvfb
Rules-Requires-Root: no
Standards-Version: 4.1.4
Homepage: @CPACK_DEBIAN_PACKAGE_HOMEPAGE@
Vcs-Browser: https://github.com/mixxxdj/mixxx
Vcs-Git: https://github.com/mixxxdj/mixxx.git

Package: mixxx
Section: @CPACK_DEBIAN_PACKAGE_SECTION@
Architecture: linux-any
Depends: ${shlibs:Depends}, ${misc:Depends}, @CPACK_DEBIAN_PACKAGE_DEPENDS@
Recommends: @CPACK_DEBIAN_PACKAGE_RECOMMENDS@
Suggests: @CPACK_DEBIAN_PACKAGE_SUGGESTS@
Replaces: mixxx-data
Description: @CPACK_DEBIAN_PACKAGE_DESCRIPTION_MERGED@
