<!doctype refentry PUBLIC "-//OASIS//DTD DocBook V4.1//EN" [

<!-- Process this file with docbook-to-man to generate an nroff manual
     page: `docbook-to-man manpage.sgml > manpage.1'.  You may view
     the manual page with: `docbook-to-man manpage.sgml | nroff -man |
     less'.  A typical entry in a Makefile or Makefile.am is:

manpage.1: manpage.sgml
	docbook-to-man $< > $@


	The docbook-to-man binary is found in the docbook-to-man package.
	Please remember that if you create the nroff version in one of the
	debian/rules file targets (such as build), you will need to include
	docbook-to-man in your Build-Depends control field.

  -->

  <!-- Fill in your name for FIRSTNAME and SURNAME. -->
  <!ENTITY dhfirstname "<firstname>S.</firstname>">
  <!ENTITY dhsurname   "<surname>Brandt</surname>">
  <!-- Please adjust the date whenever revising the manpage. -->
  <!ENTITY dhdate      "<date>April 12, 2013</date>">
  <!-- SECTION should be 1-8, maybe w/ subsection other parameters are
       allowed: see man(7), man(1). -->
  <!ENTITY dhsection   "<manvolnum>1</manvolnum>">
  <!ENTITY dhemail     "<email><EMAIL></email>">
  <!ENTITY dhusername  "S.Brandt">
  <!ENTITY dhucpackage "<refentrytitle>MIXXX</refentrytitle>">
  <!ENTITY dhpackage   "mixxx">

  <!ENTITY debian      "<productname>Debian</productname>">
  <!ENTITY gnu         "<acronym>GNU</acronym>">
  <!ENTITY gpl         "&gnu; <acronym>GPL</acronym>">
]>

<refentry>
  <refentryinfo>
    <address>
      &dhemail;
    </address>
    <author>
      &dhfirstname;
      &dhsurname;
    </author>
    <copyright>
      <year>2013</year>
      <holder>&dhusername;</holder>
    </copyright>
    &dhdate;
  </refentryinfo>
  <refmeta>
    &dhucpackage;

    &dhsection;
  </refmeta>
  <refnamediv>
    <refname>&dhpackage;</refname>

    <refpurpose>A Digital Disc Jockey Interface</refpurpose>
  </refnamediv>
  <refsynopsisdiv>
    <cmdsynopsis>
      <command>&dhpackage;</command>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1>
    <title>DESCRIPTION</title>
    <para>
      <command>&dhpackage;</command> is free DJ software that gives you
      everything you need to perform live DJ mixes. Blend songs together with
      automatic BPM matching and remix on-the-fly with looping and hot cues.
      Whether you're a pro DJ or just getting started, Mixxx has you covered.
     </para>

     <para>
      &dhpackage; works with ALSA, JACK, OSS and supports many popular DJ
      controllers.
    </para>

    <para>
      This manual page documents briefly the <command>&dhpackage;</command>
      command line options. It does not describe all of the features
      of &dhpackage; or how to use it; Instead, it has a documentation in pdf
      format; see below.
    </para>
    <para>
      <emphasis role="bold">KEY FEATURES</emphasis>
    </para>

    <itemizedlist mark='opencircle'>
      <listitem>
        <para>Parallel or split scratchable waveform displays.</para>
      </listitem>
      <listitem>
        <para>MP3, OGG, WAVE, FLAC, and optional AAC (M4A) playback.</para>
      </listitem>
      <listitem>
        <para>WAVE, OGG, and optional MP3 recording.</para>
      </listitem>
      <listitem>
        <para>Fast, database-powered library.</para>
      </listitem>
      <listitem>
        <para>Crates and playlists for organizing your music.</para>
      </listitem>
      <listitem>
        <para>Reads iTunes, Traktor, and Rhythmbox libraries.</para>
      </listitem>
      <listitem>
        <para>Cross-platform - works on Windows, Mac OS X and Linux.</para>
      </listitem>
      <listitem>
        <para>Shoutcast and Icecast broadcasting.</para>
      </listitem>
      <listitem>
        <para>Support for many DJ MIDI & HID controllers out-of-the-box.</para>
      </listitem>
       <listitem>
        <para>Advanced MIDI scripting engine for maximum flexibility.</para>
      </listitem>
      <listitem>
        <para>Vinyl emulation with Serato,Traktor, and Mixvibes timecode support.</para>
      </listitem>
      <listitem>
        <para>Bulk BPM and beat detection.</para>
      </listitem>
      <listitem>
        <para>ReplayGain volume normalization.</para>
      </listitem>
      <listitem>
        <para>Automatic crossfading with Auto DJ.</para>
      </listitem>
      <listitem>
        <para>Skinnable interface with several skins bundled.</para>
      </listitem>
    </itemizedlist>

    <para>
      For a full list of features go to:
      <ulink url="http://mixxx.org/features/"></ulink>.
    </para>
    <para>
     &dhpackage; does not have AAC (M4A) playback support enabled by default due
     to licensing restrictions. To enable playback of unprotected AAC (M4A)
     files, you can build &dhpackage; from source. See also:
     <ulink url="http://www.mixxx.org/wiki/doku.php/compiling_on_linux#build_with_m4a_file_support"></ulink>
    </para>
  </refsect1>

  <refsect1>
    <title>OPTIONS</title>
    <para>
      Mixxx is designed to be as user-friendly as possible. As such, its command
      line options are only useful for development or debugging, as they make
      these tasks easier. <command>&dhpackage;</command> accepts the following
      case-sensitive options on the command-line:
    </para>
    <variablelist>
      <varlistentry>
        <term>
          <replaceable class="parameter">&lt;FILE&gt;</replaceable>
        </term>
        <listitem>
          <para>
            Load the specified music file(s) at start-up. Each file you
            specify will be loaded into the next virtual deck. Supported file
            types: mp3, ogg, aiff, aif, wav, flac
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--resourcePath</option>
          <replaceable class="parameter">&lt;PATH&gt;</replaceable>
        </term>
        <listitem>
          <para>
            Top-level directory where Mixxx should look for its resource
            files such as MIDI mappings, overriding the default installation
            location.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--settingsPath</option>
          <replaceable class="parameter">&lt;PATH&gt;</replaceable>
        </term>
        <listitem>
          <para>
            Top-level directory where Mixxx should look for settings.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--controllerDebug</option>
        </term>
        <listitem>
          <para>
            Causes Mixxx to display/log all of the MIDI/HID controller data
            it receives and script functions it loads.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--developer</option>
        </term>
        <listitem>
          <para>
            Enables developer-mode. Includes extra log info, stats on
            performance, and a Developer tools menu.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--locale</option>
          <replaceable class="parameter">&lt;LOCALE&gt;</replaceable>
        </term>
        <listitem>
          <para>
            Use a custom locale for loading translations (e.g 'fr').
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>--f</option>
          <option>--fullScreen</option>
        </term>
        <listitem>
          <para>
            Starts &dhpackage; in full-screen mode.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <option>-h</option>
          <option>--help</option>
        </term>
        <listitem>
          <para>
            Display a help message and exit.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>SEE ALSO</title>
    <para>
      <filename class='directory'>/usr/share/doc/mixxx/Mixxx-Manual.pdf</filename>
    </para>
    <para>
      The manual in pdf format should have come with your copy of &dhpackage;,
      however there is an online copy available:
      <ulink url="http://mixxx.org/wiki/doku.php/manual"></ulink>.
    </para>
  </refsect1>

  <refsect1>
    <title>FILES</title>
    <variablelist>
      <varlistentry>
        <term>
          <filename class='directory'>~/.mixxx/</filename>
        </term>
        <listitem>
          <para>
            The directory where &dhpackage; stores its user data.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>~/.mixxx/mixxx.cfg</filename>
        </term>
        <listitem>
          <para>
            The &dhpackage; configuration file.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>~/.mixxx/mixxxdb.sqlite</filename>
        </term>
        <listitem>
          <para>
            The &dhpackage; library file.
            <emphasis role="bold">Warning:</emphasis> Deleting your library
            will lose all of your metadata. This includes saved hotcues, loops,
            comments, ratings, and other library related metadata. Only delete
            your library if you are fine with losing these.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>~/.mixxx/analysis</filename>
        </term>
        <listitem>
          <para>
            The directory where &dhpackage; stores any additional analysis data
            that can't fit into the library file. Currently only the waveforms
            are saved here. If you delete your library file you should also
            delete this folder as the waveforms around 1MB each and are unusable
            without the library that generated them.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>~/.mixxx/controllers</filename>
        </term>
        <listitem>
          <para>
            The directory where &dhpackage; stores user controller mappings.
            All mappings that are created with the MIDI learning wizard are
            saved here. If there are mappings for a controller in the default
            resource directory and this directory &dhpackage; will use
            the one saved here.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>/usr/share/mixxx</filename>
        </term>
        <listitem>
          <para>
            The directory where &dhpackage; stores its default resources like
            skins, MIDI/HID controller mappings, keyboard mappings, and
            translations.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>/usr/share/doc/mixxx/Mixxx-Manual.pdf</filename>
        </term>
        <listitem>
          <para>
            The &dhpackage; manual in pdf format.
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>
          <filename class='directory'>/usr/share/doc/mixxx/Mixxx-Keyboard-Shortcuts.pdf</filename>
        </term>
        <listitem>
          <para>
            An overview of the default keyboard shortcuts in pdf format.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

  <refsect1>
    <title>BUGS</title>
    <para>
      To report a bug or request a feature, go to the &dhpackage; bug tracker on Github:
      <ulink url="https://github.com/mixxxdj/mixxx/issues/new"></ulink>
    </para>
  </refsect1>

  <refsect1>
    <title>AUTHORS</title>
    <para>Since 2000, over 150 developers, artists and translators have
      helped create &dhpackage;. The current &dhpackage; development team is
      led by RJ Ryan <email><EMAIL></email>.
    </para>
    <para>
      This manual page was written by &dhusername; &dhemail; for the &debian;
      system (and may be used by others).  Permission is granted to copy,
      distribute and/or modify this document under the terms of the &gnu;
      General Public License, Version 2 any later version published by the Free
      Software Foundation.
    </para>
    <para>
      On Debian systems, the complete text of the GNU General Public
      License can be found in /usr/share/common-licenses/GPL-2.
    </para>
  </refsect1>

  <refsect1>
    <title>History</title>
    <variablelist>
       <varlistentry>
        <term>April 12, 2013</term>
        <listitem>
          <para>Completely revised for &dhpackage; 1.11 by Max Linke
            <email></email> and S.Brandt <email><EMAIL></email>
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term>November 26, 2004</term>
        <listitem>
          <para>Initial version by Paul Brossier <email><EMAIL></email>.
          </para>
        </listitem>
      </varlistentry>
    </variablelist>
  </refsect1>

</refentry>

<!-- Keep this comment at the end of the file
Local variables:
mode: sgml
sgml-omittag:t
sgml-shorttag:t
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:2
sgml-indent-data:t
sgml-parent-document:nil
sgml-default-dtd-file:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
