diff --git a/lib/qm-dsp/dsp/chromagram/ConstantQ.cpp b/lib/qm-dsp/dsp/chromagram/ConstantQ.cpp
index d268756..f2fad31 100644
--- a/lib/qm-dsp/dsp/chromagram/ConstantQ.cpp
+++ b/lib/qm-dsp/dsp/chromagram/ConstantQ.cpp
@@ -61,14 +61,15 @@ void ConstantQ::sparsekernel()
 
         // Compute a complex sinusoid windowed with a hamming window
         // of the right length
-        
-        int windowLength = (int)ceil
-            (m_dQ * m_FS / (m_FMin * pow(2, (double)j / (double)m_BPO)));
+
+        const double samplesPerCycle =
+            m_FS / (m_FMin * pow(2, (double)j / (double)m_BPO));
+        int windowLength = (int)ceil(m_dQ * samplesPerCycle);
 
         int origin = m_FFTLength/2 - windowLength/2;
 
         for (int i = 0; i < windowLength; ++i) {
-            double angle = (2.0 * M_PI * m_dQ * i) / windowLength;
+            double angle = (2.0 * M_PI * i) / samplesPerCycle;
             windowRe[origin + i] = cos(angle);
             windowIm[origin + i] = sin(angle);
         }
diff --git a/lib/qm-dsp/dsp/transforms/FFT.cpp b/lib/qm-dsp/dsp/transforms/FFT.cpp
index da476b8..8833255 100644
--- a/lib/qm-dsp/dsp/transforms/FFT.cpp
+++ b/lib/qm-dsp/dsp/transforms/FFT.cpp
@@ -10,8 +10,8 @@
 
 #include "maths/MathUtilities.h"
 
-#include "kiss_fft.h"
-#include "kiss_fftr.h"
+#include "ext/kissfft/kiss_fft.h"
+#include "ext/kissfft/tools/kiss_fftr.h"
 
 #include <cmath>
 
diff --git a/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.c b/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.c
index b8e238b..8adb0f0 100644
--- a/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.c
+++ b/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.c
@@ -13,7 +13,7 @@ THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 */
 
 #include "kiss_fftr.h"
-#include "_kiss_fft_guts.h"
+#include "../_kiss_fft_guts.h"
 
 struct kiss_fftr_state{
     kiss_fft_cfg substate;
diff --git a/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.h b/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.h
index 72e5a57..81d8a8e 100644
--- a/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.h
+++ b/lib/qm-dsp/ext/kissfft/tools/kiss_fftr.h
@@ -1,7 +1,7 @@
 #ifndef KISS_FTR_H
 #define KISS_FTR_H
 
-#include "kiss_fft.h"
+#include "../kiss_fft.h"
 #ifdef __cplusplus
 extern "C" {
 #endif

diff --git a/lib/qm-dsp/dsp/keydetection/GetKeyMode.cpp b/lib/qm-dsp/dsp/keydetection/GetKeyMode.cpp
index 879f8a8..7d62253 100644
--- a/lib/qm-dsp/dsp/keydetection/GetKeyMode.cpp
+++ b/lib/qm-dsp/dsp/keydetection/GetKeyMode.cpp
@@ -76,10 +76,11 @@ GetKeyMode::GetKeyMode(Config config) :
 
     // Set C3 (= MIDI #48) as our base:
     // This implies that key = 1 => Cmaj, key = 12 => Bmaj, key = 13 => Cmin, etc.
+    const float centsOffset = -12.0f / kBinsPerOctave * 100; // 3 bins per note, start with the first
     chromaConfig.min =
-        Pitch::getFrequencyForPitch( 48, 0, config.tuningFrequency );
+        Pitch::getFrequencyForPitch( 48, centsOffset, config.tuningFrequency );
     chromaConfig.max =
-        Pitch::getFrequencyForPitch( 96, 0, config.tuningFrequency );
+        Pitch::getFrequencyForPitch( 96, centsOffset, config.tuningFrequency );
 
     chromaConfig.BPO = kBinsPerOctave;
     chromaConfig.CQThresh = 0.0054;
@@ -232,14 +233,11 @@ int GetKeyMode::process(double *pcmData)
     }
 
     for (k = 0; k < kBinsPerOctave; k++) {
-        // The Chromagram has the center of C at bin 0, while the major
-        // and minor profiles have the center of C at 1. We want to have
-        // the correlation for C result also at 1.
-        // To achieve this we have to shift two times:
-        m_majCorr[k] = krumCorr
-            (m_meanHPCP, m_majProfileNorm, k - 2, kBinsPerOctave);
-        m_minCorr[k] = krumCorr
-            (m_meanHPCP, m_minProfileNorm, k - 2, kBinsPerOctave);
+        // The cromagram and the major and minor profiles have the has the
+        // center of C at bin 1. We want to have the correlation for C result
+        // also at 1. To achieve this we have to shift by one:
+        m_majCorr[k] = krumCorr(m_meanHPCP, m_majProfileNorm, (int)k - 1, kBinsPerOctave);
+        m_minCorr[k] = krumCorr(m_meanHPCP, m_minProfileNorm, (int)k - 1, kBinsPerOctave);
     }
 
     // m_MajCorr[1] is C center  1 / 3 + 1 = 1
@@ -314,3 +312,4 @@ double* GetKeyMode::getKeyStrengths() {
 
     return m_keyStrengths;
 }
+

