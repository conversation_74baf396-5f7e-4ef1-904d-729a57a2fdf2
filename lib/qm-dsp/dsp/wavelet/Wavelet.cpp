/* -*- c-basic-offset: 4 indent-tabs-mode: nil -*-  vi:set ts=8 sts=4 sw=4: */

/*
    QM DSP Library

    Centre for Digital Music, Queen Mary, University of London.
    This file copyright 2009 <PERSON>.

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License as
    published by the Free Software Foundation; either version 2 of the
    License, or (at your option) any later version.  See the file
    COPYING included with this distribution for more information.
*/

#include "Wavelet.h"

#include <cassert>

std::string
Wavelet::getWaveletName(Type wavelet)
{
    switch (wavelet) {
        case Haar: return "Haar";
        case Daubechies_2: return "Daubechies 2";
        case Daubechies_3: return "Daubechies 3";
        case Daubechies_4: return "Daubechies 4";
        case Daubechies_5: return "Daubechies 5";
        case Daubechies_6: return "Daubechies 6";
        case Daubechies_7: return "Daubechies 7";
        case Daubechies_8: return "Daubechies 8";
        case Daubechies_9: return "Daubechies 9";
        case Daubechies_10: return "Daubechies 10";
        case Daubechies_20: return "Daubechies 20";
        case Daubechies_40: return "Daubechies 40";
        case Symlet_2: return "Symlet 2";
        case Symlet_3: return "Symlet 3";
        case Symlet_4: return "Symlet 4";
        case Symlet_5: return "Symlet 5";
        case Symlet_6: return "Symlet 6";
        case Symlet_7: return "Symlet 7";
        case Symlet_8: return "Symlet 8";
        case Symlet_9: return "Symlet 9";
        case Symlet_10: return "Symlet 10";
        case Symlet_20: return "Symlet 20";
        case Symlet_30: return "Symlet 30";
        case Coiflet_1: return "Coiflet 1";
        case Coiflet_2: return "Coiflet 2";
        case Coiflet_3: return "Coiflet 3";
        case Coiflet_4: return "Coiflet 4";
        case Coiflet_5: return "Coiflet 5";
        case Biorthogonal_1_3: return "Biorthogonal 1.3";
        case Biorthogonal_1_5: return "Biorthogonal 1.5";
        case Biorthogonal_2_2: return "Biorthogonal 2.2";
        case Biorthogonal_2_4: return "Biorthogonal 2.4";
        case Biorthogonal_2_6: return "Biorthogonal 2.6";
        case Biorthogonal_2_8: return "Biorthogonal 2.8";
        case Biorthogonal_3_1: return "Biorthogonal 3.1";
        case Biorthogonal_3_3: return "Biorthogonal 3.3";
        case Biorthogonal_3_5: return "Biorthogonal 3.5";
        case Biorthogonal_3_7: return "Biorthogonal 3.7";
        case Biorthogonal_3_9: return "Biorthogonal 3.9";
        case Biorthogonal_4_4: return "Biorthogonal 4.4";
        case Biorthogonal_5_5: return "Biorthogonal 5.5";
        case Biorthogonal_6_8: return "Biorthogonal 6.8";
        case Meyer: return "Meyer";
    }

    return "(unknown)";
}

void
Wavelet::createDecompositionFilters(Type wavelet,
                                    std::vector<double> &lpd,
                                    std::vector<double> &hpd)
{
    lpd.clear();
    hpd.clear();

    int flength = 0;
        
    switch (wavelet) {

    case Haar: 
        lpd.push_back(0.70710678118655);
        lpd.push_back(0.70710678118655);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.70710678118655);
        flength = 2;
        break;

    case Daubechies_2:
        lpd.push_back(-0.12940952255092);
        lpd.push_back(0.22414386804186);
        lpd.push_back(0.83651630373747);
        lpd.push_back(0.48296291314469);
        hpd.push_back(-0.48296291314469);
        hpd.push_back(0.83651630373747);
        hpd.push_back(-0.22414386804186);
        hpd.push_back(-0.12940952255092);
        flength = 4;
        break;          

    case Daubechies_3:
        lpd.push_back(0.03522629188210);
        lpd.push_back(-0.08544127388224);
        lpd.push_back(-0.13501102001039);
        lpd.push_back(0.45987750211933);
        lpd.push_back(0.80689150931334);
        lpd.push_back(0.33267055295096);
        hpd.push_back(-0.33267055295096);
        hpd.push_back(0.80689150931334);
        hpd.push_back(-0.45987750211933);
        hpd.push_back(-0.13501102001039);
        hpd.push_back(0.08544127388224);
        hpd.push_back(0.03522629188210);
        flength = 6;
        break;

    case Daubechies_4:
        lpd.push_back(-0.01059740178500);
        lpd.push_back(0.03288301166698);
        lpd.push_back(0.03084138183599);
        lpd.push_back(-0.18703481171888);
        lpd.push_back(-0.02798376941698);
        lpd.push_back(0.63088076792959);
        lpd.push_back(0.71484657055254);
        lpd.push_back(0.23037781330886);
        hpd.push_back(-0.23037781330886);
        hpd.push_back(0.71484657055254);
        hpd.push_back(-0.63088076792959);
        hpd.push_back(-0.02798376941698);
        hpd.push_back(0.18703481171888);
        hpd.push_back(0.03084138183599);
        hpd.push_back(-0.03288301166698);
        hpd.push_back(-0.01059740178500);
        flength = 8;
        break;

    case Daubechies_5:
        lpd.push_back(0.00333572528500);
        lpd.push_back(-0.01258075199902);
        lpd.push_back(-0.00624149021301);
        lpd.push_back(0.07757149384007);
        lpd.push_back(-0.03224486958503);
        lpd.push_back(-0.24229488706619);
        lpd.push_back(0.13842814590110);
        lpd.push_back(0.72430852843857);
        lpd.push_back(0.60382926979747);
        lpd.push_back(0.16010239797413);
        hpd.push_back(-0.16010239797413);
        hpd.push_back(0.60382926979747);
        hpd.push_back(-0.72430852843857);
        hpd.push_back(0.13842814590110);
        hpd.push_back(0.24229488706619);
        hpd.push_back(-0.03224486958503);
        hpd.push_back(-0.07757149384007);
        hpd.push_back(-0.00624149021301);
        hpd.push_back(0.01258075199902);
        hpd.push_back(0.00333572528500);
        flength = 10;
        break;

    case Daubechies_6:
        lpd.push_back(-0.00107730108500);
        lpd.push_back(0.00477725751101);
        lpd.push_back(0.00055384220099);
        lpd.push_back(-0.03158203931803);
        lpd.push_back(0.02752286553002);
        lpd.push_back(0.09750160558708);
        lpd.push_back(-0.12976686756710);
        lpd.push_back(-0.22626469396517);
        lpd.push_back(0.31525035170924);
        lpd.push_back(0.75113390802158);
        lpd.push_back(0.49462389039839);
        lpd.push_back(0.11154074335008);
        hpd.push_back(-0.11154074335008);
        hpd.push_back(0.49462389039839);
        hpd.push_back(-0.75113390802158);
        hpd.push_back(0.31525035170924);
        hpd.push_back(0.22626469396517);
        hpd.push_back(-0.12976686756710);
        hpd.push_back(-0.09750160558708);
        hpd.push_back(0.02752286553002);
        hpd.push_back(0.03158203931803);
        hpd.push_back(0.00055384220099);
        hpd.push_back(-0.00477725751101);
        hpd.push_back(-0.00107730108500);
        flength = 12;
        break;

    case Daubechies_7:
        lpd.push_back(0.00035371380000);
        lpd.push_back(-0.00180164070400);
        lpd.push_back(0.00042957797300);
        lpd.push_back(0.01255099855601);
        lpd.push_back(-0.01657454163102);
        lpd.push_back(-0.03802993693503);
        lpd.push_back(0.08061260915107);
        lpd.push_back(0.07130921926705);
        lpd.push_back(-0.22403618499417);
        lpd.push_back(-0.14390600392911);
        lpd.push_back(0.46978228740536);
        lpd.push_back(0.72913209084656);
        lpd.push_back(0.39653931948231);
        lpd.push_back(0.07785205408506);
        hpd.push_back(-0.07785205408506);
        hpd.push_back(0.39653931948231);
        hpd.push_back(-0.72913209084656);
        hpd.push_back(0.46978228740536);
        hpd.push_back(0.14390600392911);
        hpd.push_back(-0.22403618499417);
        hpd.push_back(-0.07130921926705);
        hpd.push_back(0.08061260915107);
        hpd.push_back(0.03802993693503);
        hpd.push_back(-0.01657454163102);
        hpd.push_back(-0.01255099855601);
        hpd.push_back(0.00042957797300);
        hpd.push_back(0.00180164070400);
        hpd.push_back(0.00035371380000);
        flength = 14;
        break;

    case Daubechies_8:
        lpd.push_back(-0.00011747678400);
        lpd.push_back(0.00067544940600);
        lpd.push_back(-0.00039174037300);
        lpd.push_back(-0.00487035299301);
        lpd.push_back(0.00874609404702);
        lpd.push_back(0.01398102791702);
        lpd.push_back(-0.04408825393106);
        lpd.push_back(-0.01736930100202);
        lpd.push_back(0.12874742662019);
        lpd.push_back(0.00047248457400);
        lpd.push_back(-0.28401554296243);
        lpd.push_back(-0.01582910525602);
        lpd.push_back(0.58535468365487);
        lpd.push_back(0.67563073629801);
        lpd.push_back(0.31287159091447);
        lpd.push_back(0.05441584224308);
        hpd.push_back(-0.05441584224308);
        hpd.push_back(0.31287159091447);
        hpd.push_back(-0.67563073629801);
        hpd.push_back(0.58535468365487);
        hpd.push_back(0.01582910525602);
        hpd.push_back(-0.28401554296243);
        hpd.push_back(-0.00047248457400);
        hpd.push_back(0.12874742662019);
        hpd.push_back(0.01736930100202);
        hpd.push_back(-0.04408825393106);
        hpd.push_back(-0.01398102791702);
        hpd.push_back(0.00874609404702);
        hpd.push_back(0.00487035299301);
        hpd.push_back(-0.00039174037300);
        hpd.push_back(-0.00067544940600);
        hpd.push_back(-0.00011747678400);
        flength = 16;
        break;

    case Daubechies_9:
        lpd.push_back(0.00003934732000);
        lpd.push_back(-0.00025196318900);
        lpd.push_back(0.00023038576400);
        lpd.push_back(0.00184764688296);
        lpd.push_back(-0.00428150368190);
        lpd.push_back(-0.00472320475789);
        lpd.push_back(0.02236166212352);
        lpd.push_back(0.00025094711499);
        lpd.push_back(-0.06763282905952);
        lpd.push_back(0.03072568147832);
        lpd.push_back(0.14854074933476);
        lpd.push_back(-0.09684078322088);
        lpd.push_back(-0.29327378327259);
        lpd.push_back(0.13319738582209);
        lpd.push_back(0.65728807803664);
        lpd.push_back(0.60482312367678);
        lpd.push_back(0.24383467463767);
        lpd.push_back(0.03807794736317);
        hpd.push_back(-0.03807794736317);
        hpd.push_back(0.24383467463767);
        hpd.push_back(-0.60482312367678);
        hpd.push_back(0.65728807803664);
        hpd.push_back(-0.13319738582209);
        hpd.push_back(-0.29327378327259);
        hpd.push_back(0.09684078322088);
        hpd.push_back(0.14854074933476);
        hpd.push_back(-0.03072568147832);
        hpd.push_back(-0.06763282905952);
        hpd.push_back(-0.00025094711499);
        hpd.push_back(0.02236166212352);
        hpd.push_back(0.00472320475789);
        hpd.push_back(-0.00428150368190);
        hpd.push_back(-0.00184764688296);
        hpd.push_back(0.00023038576400);
        hpd.push_back(0.00025196318900);
        hpd.push_back(0.00003934732000);
        flength = 18;
        break;

    case Daubechies_10:
        lpd.push_back(-0.00001326420300);
        lpd.push_back(0.00009358867000);
        lpd.push_back(-0.00011646685499);
        lpd.push_back(-0.00068585669500);
        lpd.push_back(0.00199240529499);
        lpd.push_back(0.00139535174699);
        lpd.push_back(-0.01073317548298);
        lpd.push_back(0.00360655356699);
        lpd.push_back(0.03321267405893);
        lpd.push_back(-0.02945753682195);
        lpd.push_back(-0.07139414716586);
        lpd.push_back(0.09305736460381);
        lpd.push_back(0.12736934033574);
        lpd.push_back(-0.19594627437660);
        lpd.push_back(-0.24984642432649);
        lpd.push_back(0.28117234366043);
        lpd.push_back(0.68845903945259);
        lpd.push_back(0.52720118893092);
        lpd.push_back(0.18817680007762);
        lpd.push_back(0.02667005790095);
        hpd.push_back(-0.02667005790095);
        hpd.push_back(0.18817680007762);
        hpd.push_back(-0.52720118893092);
        hpd.push_back(0.68845903945259);
        hpd.push_back(-0.28117234366043);
        hpd.push_back(-0.24984642432649);
        hpd.push_back(0.19594627437660);
        hpd.push_back(0.12736934033574);
        hpd.push_back(-0.09305736460381);
        hpd.push_back(-0.07139414716586);
        hpd.push_back(0.02945753682195);
        hpd.push_back(0.03321267405893);
        hpd.push_back(-0.00360655356699);
        hpd.push_back(-0.01073317548298);
        hpd.push_back(-0.00139535174699);
        hpd.push_back(0.00199240529499);
        hpd.push_back(0.00068585669500);
        hpd.push_back(-0.00011646685499);
        hpd.push_back(-0.00009358867000);
        hpd.push_back(-0.00001326420300);
        flength = 20;
        break;

    case Daubechies_20:
        lpd.push_back(-0.00000000029988);
        lpd.push_back(0.00000000405613);
        lpd.push_back(-0.00000001814843);
        lpd.push_back(0.00000000020143);
        lpd.push_back(0.00000026339242);
        lpd.push_back(-0.00000068470796);
        lpd.push_back(-0.00000101199401);
        lpd.push_back(0.00000724124829);
        lpd.push_back(-0.00000437614386);
        lpd.push_back(-0.00003710586183);
        lpd.push_back(0.00006774280828);
        lpd.push_back(0.00010153288973);
        lpd.push_back(-0.00038510474870);
        lpd.push_back(-0.00005349759845);
        lpd.push_back(0.00139255961930);
        lpd.push_back(-0.00083156217284);
        lpd.push_back(-0.00358149425960);
        lpd.push_back(0.00442054238705);
        lpd.push_back(0.00672162730228);
        lpd.push_back(-0.01381052613705);
        lpd.push_back(-0.00878932492387);
        lpd.push_back(0.03229429953057);
        lpd.push_back(0.00587468181179);
        lpd.push_back(-0.06172289962416);
        lpd.push_back(0.00563224685793);
        lpd.push_back(0.10229171917503);
        lpd.push_back(-0.02471682733721);
        lpd.push_back(-0.15545875070532);
        lpd.push_back(0.03985024645854);
        lpd.push_back(0.22829105081947);
        lpd.push_back(-0.01672708830868);
        lpd.push_back(-0.32678680043239);
        lpd.push_back(-0.13921208801080);
        lpd.push_back(0.36150229873767);
        lpd.push_back(0.61049323893578);
        lpd.push_back(0.47269618530872);
        lpd.push_back(0.21994211355038);
        lpd.push_back(0.06342378045879);
        lpd.push_back(0.01054939462490);
        lpd.push_back(0.00077995361366);
        hpd.push_back(-0.00077995361366);
        hpd.push_back(0.01054939462490);
        hpd.push_back(-0.06342378045879);
        hpd.push_back(0.21994211355038);
        hpd.push_back(-0.47269618530872);
        hpd.push_back(0.61049323893578);
        hpd.push_back(-0.36150229873767);
        hpd.push_back(-0.13921208801080);
        hpd.push_back(0.32678680043239);
        hpd.push_back(-0.01672708830868);
        hpd.push_back(-0.22829105081947);
        hpd.push_back(0.03985024645854);
        hpd.push_back(0.15545875070532);
        hpd.push_back(-0.02471682733721);
        hpd.push_back(-0.10229171917503);
        hpd.push_back(0.00563224685793);
        hpd.push_back(0.06172289962416);
        hpd.push_back(0.00587468181179);
        hpd.push_back(-0.03229429953057);
        hpd.push_back(-0.00878932492387);
        hpd.push_back(0.01381052613705);
        hpd.push_back(0.00672162730228);
        hpd.push_back(-0.00442054238705);
        hpd.push_back(-0.00358149425960);
        hpd.push_back(0.00083156217284);
        hpd.push_back(0.00139255961930);
        hpd.push_back(0.00005349759845);
        hpd.push_back(-0.00038510474870);
        hpd.push_back(-0.00010153288973);
        hpd.push_back(0.00006774280828);
        hpd.push_back(0.00003710586183);
        hpd.push_back(-0.00000437614386);
        hpd.push_back(-0.00000724124829);
        hpd.push_back(-0.00000101199401);
        hpd.push_back(0.00000068470796);
        hpd.push_back(0.00000026339242);
        hpd.push_back(-0.00000000020143);
        hpd.push_back(-0.00000001814843);
        hpd.push_back(-0.00000000405613);
        hpd.push_back(-0.00000000029988);
        flength = 40;
        break;

    case Daubechies_40:
        lpd.push_back(-0.00000000000000);
        lpd.push_back(0.00000000000000);
        lpd.push_back(-0.00000000000000);
        lpd.push_back(0.00000000000000);
        lpd.push_back(-0.00000000000000);
        lpd.push_back(-0.00000000000001);
        lpd.push_back(0.00000000000003);
        lpd.push_back(-0.00000000000001);
        lpd.push_back(-0.00000000000037);
        lpd.push_back(0.00000000000121);
        lpd.push_back(0.00000000000108);
        lpd.push_back(-0.00000000001441);
        lpd.push_back(0.00000000001995);
        lpd.push_back(0.00000000008134);
        lpd.push_back(-0.00000000029288);
        lpd.push_back(-0.00000000009963);
        lpd.push_back(0.00000000205938);
        lpd.push_back(-0.00000000227332);
        lpd.push_back(-0.00000000873967);
        lpd.push_back(0.00000002288390);
        lpd.push_back(0.00000001741059);
        lpd.push_back(-0.00000012745008);
        lpd.push_back(0.00000005361186);
        lpd.push_back(0.00000048834273);
        lpd.push_back(-0.00000066863337);
        lpd.push_back(-0.00000128043283);
        lpd.push_back(0.00000350778202);
        lpd.push_back(0.00000165493877);
        lpd.push_back(-0.00001288842174);
        lpd.push_back(0.00000406557792);
        lpd.push_back(0.00003629399945);
        lpd.push_back(-0.00003566632371);
        lpd.push_back(-0.00007878802614);
        lpd.push_back(0.00014251943335);
        lpd.push_back(0.00012109867291);
        lpd.push_back(-0.00041818330353);
        lpd.push_back(-0.00007088554491);
        lpd.push_back(0.00099285797789);
        lpd.push_back(-0.00031193527356);
        lpd.push_back(-0.00197480748041);
        lpd.push_back(0.00148388984367);
        lpd.push_back(0.00332132500594);
        lpd.push_back(-0.00412431015737);
        lpd.push_back(-0.00467322831674);
        lpd.push_back(0.00901902126682);
        lpd.push_back(0.00524573780794);
        lpd.push_back(-0.01682072694998);
        lpd.push_back(-0.00388701203892);
        lpd.push_back(0.02781129406516);
        lpd.push_back(-0.00059370667303);
        lpd.push_back(-0.04179364874023);
        lpd.push_back(0.00895082555981);
        lpd.push_back(0.05834125954654);
        lpd.push_back(-0.02094317414735);
        lpd.push_back(-0.07729538082012);
        lpd.push_back(0.03492832601473);
        lpd.push_back(0.09941959711973);
        lpd.push_back(-0.04741061635274);
        lpd.push_back(-0.12673146574763);
        lpd.push_back(0.05216571236437);
        lpd.push_back(0.16185493481917);
        lpd.push_back(-0.03814703831047);
        lpd.push_back(-0.20452405393126);
        lpd.push_back(-0.01395065907035);
        lpd.push_back(0.24017584511066);
        lpd.push_back(0.13119716231525);
        lpd.push_back(-0.21027451192673);
        lpd.push_back(-0.31275608334174);
        lpd.push_back(-0.02817037418845);
        lpd.push_back(0.35209361261033);
        lpd.push_back(0.51902874766301);
        lpd.push_back(0.43815806016110);
        lpd.push_back(0.25977619424035);
        lpd.push_back(0.11476477757177);
        lpd.push_back(0.03842788992792);
        lpd.push_back(0.00969841552091);
        lpd.push_back(0.00179808920458);
        lpd.push_back(0.00023209364055);
        lpd.push_back(0.00001869221611);
        lpd.push_back(0.00000070900603);
        hpd.push_back(-0.00000070900603);
        hpd.push_back(0.00001869221611);
        hpd.push_back(-0.00023209364055);
        hpd.push_back(0.00179808920458);
        hpd.push_back(-0.00969841552091);
        hpd.push_back(0.03842788992792);
        hpd.push_back(-0.11476477757177);
        hpd.push_back(0.25977619424035);
        hpd.push_back(-0.43815806016110);
        hpd.push_back(0.51902874766301);
        hpd.push_back(-0.35209361261033);
        hpd.push_back(-0.02817037418845);
        hpd.push_back(0.31275608334174);
        hpd.push_back(-0.21027451192673);
        hpd.push_back(-0.13119716231525);
        hpd.push_back(0.24017584511066);
        hpd.push_back(0.01395065907035);
        hpd.push_back(-0.20452405393126);
        hpd.push_back(0.03814703831047);
        hpd.push_back(0.16185493481917);
        hpd.push_back(-0.05216571236437);
        hpd.push_back(-0.12673146574763);
        hpd.push_back(0.04741061635274);
        hpd.push_back(0.09941959711973);
        hpd.push_back(-0.03492832601473);
        hpd.push_back(-0.07729538082012);
        hpd.push_back(0.02094317414735);
        hpd.push_back(0.05834125954654);
        hpd.push_back(-0.00895082555981);
        hpd.push_back(-0.04179364874023);
        hpd.push_back(0.00059370667303);
        hpd.push_back(0.02781129406516);
        hpd.push_back(0.00388701203892);
        hpd.push_back(-0.01682072694998);
        hpd.push_back(-0.00524573780794);
        hpd.push_back(0.00901902126682);
        hpd.push_back(0.00467322831674);
        hpd.push_back(-0.00412431015737);
        hpd.push_back(-0.00332132500594);
        hpd.push_back(0.00148388984367);
        hpd.push_back(0.00197480748041);
        hpd.push_back(-0.00031193527356);
        hpd.push_back(-0.00099285797789);
        hpd.push_back(-0.00007088554491);
        hpd.push_back(0.00041818330353);
        hpd.push_back(0.00012109867291);
        hpd.push_back(-0.00014251943335);
        hpd.push_back(-0.00007878802614);
        hpd.push_back(0.00003566632371);
        hpd.push_back(0.00003629399945);
        hpd.push_back(-0.00000406557792);
        hpd.push_back(-0.00001288842174);
        hpd.push_back(-0.00000165493877);
        hpd.push_back(0.00000350778202);
        hpd.push_back(0.00000128043283);
        hpd.push_back(-0.00000066863337);
        hpd.push_back(-0.00000048834273);
        hpd.push_back(0.00000005361186);
        hpd.push_back(0.00000012745008);
        hpd.push_back(0.00000001741059);
        hpd.push_back(-0.00000002288390);
        hpd.push_back(-0.00000000873967);
        hpd.push_back(0.00000000227332);
        hpd.push_back(0.00000000205938);
        hpd.push_back(0.00000000009963);
        hpd.push_back(-0.00000000029288);
        hpd.push_back(-0.00000000008134);
        hpd.push_back(0.00000000001995);
        hpd.push_back(0.00000000001441);
        hpd.push_back(0.00000000000108);
        hpd.push_back(-0.00000000000121);
        hpd.push_back(-0.00000000000037);
        hpd.push_back(0.00000000000001);
        hpd.push_back(0.00000000000003);
        hpd.push_back(0.00000000000001);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(-0.00000000000000);
        flength = 80;
        break;
                        
    case Symlet_2:
        lpd.push_back(-0.12940952255092);
        lpd.push_back(0.22414386804186);
        lpd.push_back(0.83651630373747);
        lpd.push_back(0.48296291314469);
        hpd.push_back(-0.48296291314469);
        hpd.push_back(0.83651630373747);
        hpd.push_back(-0.22414386804186);
        hpd.push_back(-0.12940952255092);
        flength = 4;
        break;

    case Symlet_3:
        lpd.push_back(0.03522629188210);
        lpd.push_back(-0.08544127388224);
        lpd.push_back(-0.13501102001039);
        lpd.push_back(0.45987750211933);
        lpd.push_back(0.80689150931334);
        lpd.push_back(0.33267055295096);
        hpd.push_back(-0.33267055295096);
        hpd.push_back(0.80689150931334);
        hpd.push_back(-0.45987750211933);
        hpd.push_back(-0.13501102001039);
        hpd.push_back(0.08544127388224);
        hpd.push_back(0.03522629188210);
        flength = 6;
        break;

    case Symlet_4:
        lpd.push_back(-0.07576571478927);
        lpd.push_back(-0.02963552764600);
        lpd.push_back(0.49761866763202);
        lpd.push_back(0.80373875180592);
        lpd.push_back(0.29785779560528);
        lpd.push_back(-0.09921954357685);
        lpd.push_back(-0.01260396726204);
        lpd.push_back(0.03222310060404);
        hpd.push_back(-0.03222310060404);
        hpd.push_back(-0.01260396726204);
        hpd.push_back(0.09921954357685);
        hpd.push_back(0.29785779560528);
        hpd.push_back(-0.80373875180592);
        hpd.push_back(0.49761866763202);
        hpd.push_back(0.02963552764600);
        hpd.push_back(-0.07576571478927);
        flength = 8;
        break;

    case Symlet_5:
        lpd.push_back(0.02733306834508);
        lpd.push_back(0.02951949092577);
        lpd.push_back(-0.03913424930238);
        lpd.push_back(0.19939753397739);
        lpd.push_back(0.72340769040242);
        lpd.push_back(0.63397896345821);
        lpd.push_back(0.01660210576452);
        lpd.push_back(-0.17532808990845);
        lpd.push_back(-0.02110183402476);
        lpd.push_back(0.01953888273529);
        hpd.push_back(-0.01953888273529);
        hpd.push_back(-0.02110183402476);
        hpd.push_back(0.17532808990845);
        hpd.push_back(0.01660210576452);
        hpd.push_back(-0.63397896345821);
        hpd.push_back(0.72340769040242);
        hpd.push_back(-0.19939753397739);
        hpd.push_back(-0.03913424930238);
        hpd.push_back(-0.02951949092577);
        hpd.push_back(0.02733306834508);
        flength = 10;
        break;

    case Symlet_6:
        lpd.push_back(0.01540410932703);
        lpd.push_back(0.00349071208422);
        lpd.push_back(-0.11799011114819);
        lpd.push_back(-0.04831174258563);
        lpd.push_back(0.49105594192675);
        lpd.push_back(0.78764114103019);
        lpd.push_back(0.33792942172762);
        lpd.push_back(-0.07263752278646);
        lpd.push_back(-0.02106029251230);
        lpd.push_back(0.04472490177067);
        lpd.push_back(0.00176771186424);
        lpd.push_back(-0.00780070832503);
        hpd.push_back(0.00780070832503);
        hpd.push_back(0.00176771186424);
        hpd.push_back(-0.04472490177067);
        hpd.push_back(-0.02106029251230);
        hpd.push_back(0.07263752278646);
        hpd.push_back(0.33792942172762);
        hpd.push_back(-0.78764114103019);
        hpd.push_back(0.49105594192675);
        hpd.push_back(0.04831174258563);
        hpd.push_back(-0.11799011114819);
        hpd.push_back(-0.00349071208422);
        hpd.push_back(0.01540410932703);
        flength = 12;
        break;
                        
    case Symlet_7:
        lpd.push_back(0.00268181456826);
        lpd.push_back(-0.00104738488868);
        lpd.push_back(-0.01263630340325);
        lpd.push_back(0.03051551316596);
        lpd.push_back(0.06789269350137);
        lpd.push_back(-0.04955283493713);
        lpd.push_back(0.01744125508686);
        lpd.push_back(0.53610191709176);
        lpd.push_back(0.76776431700316);
        lpd.push_back(0.28862963175151);
        lpd.push_back(-0.14004724044296);
        lpd.push_back(-0.10780823770382);
        lpd.push_back(0.00401024487153);
        lpd.push_back(0.01026817670851);
        hpd.push_back(-0.01026817670851);
        hpd.push_back(0.00401024487153);
        hpd.push_back(0.10780823770382);
        hpd.push_back(-0.14004724044296);
        hpd.push_back(-0.28862963175151);
        hpd.push_back(0.76776431700316);
        hpd.push_back(-0.53610191709176);
        hpd.push_back(0.01744125508686);
        hpd.push_back(0.04955283493713);
        hpd.push_back(0.06789269350137);
        hpd.push_back(-0.03051551316596);
        hpd.push_back(-0.01263630340325);
        hpd.push_back(0.00104738488868);
        hpd.push_back(0.00268181456826);
        flength = 14;
        break;

    case Symlet_8:
        lpd.push_back(-0.00338241595101);
        lpd.push_back(-0.00054213233179);
        lpd.push_back(0.03169508781149);
        lpd.push_back(0.00760748732492);
        lpd.push_back(-0.14329423835081);
        lpd.push_back(-0.06127335906766);
        lpd.push_back(0.48135965125837);
        lpd.push_back(0.77718575170052);
        lpd.push_back(0.36444189483533);
        lpd.push_back(-0.05194583810771);
        lpd.push_back(-0.02721902991706);
        lpd.push_back(0.04913717967361);
        lpd.push_back(0.00380875201389);
        lpd.push_back(-0.01495225833705);
        lpd.push_back(-0.00030292051472);
        lpd.push_back(0.00188995033276);
        hpd.push_back(-0.00188995033276);
        hpd.push_back(-0.00030292051472);
        hpd.push_back(0.01495225833705);
        hpd.push_back(0.00380875201389);
        hpd.push_back(-0.04913717967361);
        hpd.push_back(-0.02721902991706);
        hpd.push_back(0.05194583810771);
        hpd.push_back(0.36444189483533);
        hpd.push_back(-0.77718575170052);
        hpd.push_back(0.48135965125837);
        hpd.push_back(0.06127335906766);
        hpd.push_back(-0.14329423835081);
        hpd.push_back(-0.00760748732492);
        hpd.push_back(0.03169508781149);
        hpd.push_back(0.00054213233179);
        hpd.push_back(-0.00338241595101);
        flength = 16;
        break;

    case Symlet_9:
        lpd.push_back(0.00140091552591);
        lpd.push_back(0.00061978088899);
        lpd.push_back(-0.01327196778182);
        lpd.push_back(-0.01152821020768);
        lpd.push_back(0.03022487885828);
        lpd.push_back(0.00058346274612);
        lpd.push_back(-0.05456895843083);
        lpd.push_back(0.23876091460730);
        lpd.push_back(0.71789708276441);
        lpd.push_back(0.61733844914094);
        lpd.push_back(0.03527248803527);
        lpd.push_back(-0.19155083129728);
        lpd.push_back(-0.01823377077940);
        lpd.push_back(0.06207778930289);
        lpd.push_back(0.00885926749340);
        lpd.push_back(-0.01026406402763);
        lpd.push_back(-0.00047315449868);
        lpd.push_back(0.00106949003291);
        hpd.push_back(-0.00106949003291);
        hpd.push_back(-0.00047315449868);
        hpd.push_back(0.01026406402763);
        hpd.push_back(0.00885926749340);
        hpd.push_back(-0.06207778930289);
        hpd.push_back(-0.01823377077940);
        hpd.push_back(0.19155083129728);
        hpd.push_back(0.03527248803527);
        hpd.push_back(-0.61733844914094);
        hpd.push_back(0.71789708276441);
        hpd.push_back(-0.23876091460730);
        hpd.push_back(-0.05456895843083);
        hpd.push_back(-0.00058346274612);
        hpd.push_back(0.03022487885828);
        hpd.push_back(0.01152821020768);
        hpd.push_back(-0.01327196778182);
        hpd.push_back(-0.00061978088899);
        hpd.push_back(0.00140091552591);
        flength = 18;
        break;

    case Symlet_10:
        lpd.push_back(0.00077015980911);
        lpd.push_back(0.00009563267072);
        lpd.push_back(-0.00864129927702);
        lpd.push_back(-0.00146538258130);
        lpd.push_back(0.04592723923109);
        lpd.push_back(0.01160989390371);
        lpd.push_back(-0.15949427888491);
        lpd.push_back(-0.07088053578323);
        lpd.push_back(0.47169066693845);
        lpd.push_back(0.76951003702110);
        lpd.push_back(0.38382676106707);
        lpd.push_back(-0.03553674047383);
        lpd.push_back(-0.03199005688243);
        lpd.push_back(0.04999497207737);
        lpd.push_back(0.00576491203358);
        lpd.push_back(-0.02035493981231);
        lpd.push_back(-0.00080435893202);
        lpd.push_back(0.00459317358531);
        lpd.push_back(0.00005703608362);
        lpd.push_back(-0.00045932942100);
        hpd.push_back(0.00045932942100);
        hpd.push_back(0.00005703608362);
        hpd.push_back(-0.00459317358531);
        hpd.push_back(-0.00080435893202);
        hpd.push_back(0.02035493981231);
        hpd.push_back(0.00576491203358);
        hpd.push_back(-0.04999497207737);
        hpd.push_back(-0.03199005688243);
        hpd.push_back(0.03553674047383);
        hpd.push_back(0.38382676106707);
        hpd.push_back(-0.76951003702110);
        hpd.push_back(0.47169066693845);
        hpd.push_back(0.07088053578323);
        hpd.push_back(-0.15949427888491);
        hpd.push_back(-0.01160989390371);
        hpd.push_back(0.04592723923109);
        hpd.push_back(0.00146538258130);
        hpd.push_back(-0.00864129927702);
        hpd.push_back(-0.00009563267072);
        hpd.push_back(0.00077015980911);
        flength = 20;
        break;

    case Symlet_20:
        lpd.push_back(0.00000036955375);
        lpd.push_back(-0.00000019015676);
        lpd.push_back(-0.00000791936141);
        lpd.push_back(0.00000302566606);
        lpd.push_back(0.00007992967836);
        lpd.push_back(-0.00001928412301);
        lpd.push_back(-0.00049473109157);
        lpd.push_back(0.00007215991188);
        lpd.push_back(0.00208899470819);
        lpd.push_back(-0.00030526283181);
        lpd.push_back(-0.00660658579912);
        lpd.push_back(0.00142308735944);
        lpd.push_back(0.01700404902335);
        lpd.push_back(-0.00331385738375);
        lpd.push_back(-0.03162943714501);
        lpd.push_back(0.00812322835637);
        lpd.push_back(0.02557934951027);
        lpd.push_back(-0.07899434492693);
        lpd.push_back(-0.02981936887758);
        lpd.push_back(0.40583144435233);
        lpd.push_back(0.75116272842520);
        lpd.push_back(0.47199147510012);
        lpd.push_back(-0.05108834292497);
        lpd.push_back(-0.16057829841831);
        lpd.push_back(0.03625095165278);
        lpd.push_back(0.08891966802764);
        lpd.push_back(-0.00684370196580);
        lpd.push_back(-0.03537333675714);
        lpd.push_back(0.00193859706711);
        lpd.push_back(0.01215704094879);
        lpd.push_back(-0.00061112638583);
        lpd.push_back(-0.00347164780287);
        lpd.push_back(0.00012544091723);
        lpd.push_back(0.00074761085979);
        lpd.push_back(-0.00002661555034);
        lpd.push_back(-0.00011739133516);
        lpd.push_back(0.00000452542221);
        lpd.push_back(0.00001228725278);
        lpd.push_back(-0.00000032567026);
        lpd.push_back(-0.00000063291290);
        hpd.push_back(0.00000063291290);
        hpd.push_back(-0.00000032567026);
        hpd.push_back(-0.00001228725278);
        hpd.push_back(0.00000452542221);
        hpd.push_back(0.00011739133516);
        hpd.push_back(-0.00002661555034);
        hpd.push_back(-0.00074761085979);
        hpd.push_back(0.00012544091723);
        hpd.push_back(0.00347164780287);
        hpd.push_back(-0.00061112638583);
        hpd.push_back(-0.01215704094879);
        hpd.push_back(0.00193859706711);
        hpd.push_back(0.03537333675714);
        hpd.push_back(-0.00684370196580);
        hpd.push_back(-0.08891966802764);
        hpd.push_back(0.03625095165278);
        hpd.push_back(0.16057829841831);
        hpd.push_back(-0.05108834292497);
        hpd.push_back(-0.47199147510012);
        hpd.push_back(0.75116272842520);
        hpd.push_back(-0.40583144435233);
        hpd.push_back(-0.02981936887758);
        hpd.push_back(0.07899434492693);
        hpd.push_back(0.02557934951027);
        hpd.push_back(-0.00812322835637);
        hpd.push_back(-0.03162943714501);
        hpd.push_back(0.00331385738375);
        hpd.push_back(0.01700404902335);
        hpd.push_back(-0.00142308735944);
        hpd.push_back(-0.00660658579912);
        hpd.push_back(0.00030526283181);
        hpd.push_back(0.00208899470819);
        hpd.push_back(-0.00007215991188);
        hpd.push_back(-0.00049473109157);
        hpd.push_back(0.00001928412301);
        hpd.push_back(0.00007992967836);
        hpd.push_back(-0.00000302566606);
        hpd.push_back(-0.00000791936141);
        hpd.push_back(0.00000019015676);
        hpd.push_back(0.00000036955375);
        flength = 40;
        break;

    case Symlet_30:
        lpd.push_back(-0.00000000032641);
        lpd.push_back(0.00000000014934);
        lpd.push_back(0.00000001014810);
        lpd.push_back(-0.00000000380480);
        lpd.push_back(-0.00000014986093);
        lpd.push_back(0.00000005465704);
        lpd.push_back(0.00000142300535);
        lpd.push_back(-0.00000055432777);
        lpd.push_back(-0.00000989667666);
        lpd.push_back(0.00000402586608);
        lpd.push_back(0.00005384705071);
        lpd.push_back(-0.00002120203482);
        lpd.push_back(-0.00023724381672);
        lpd.push_back(0.00008209438737);
        lpd.push_back(0.00086145325795);
        lpd.push_back(-0.00023156681831);
        lpd.push_back(-0.00258964866287);
        lpd.push_back(0.00046642571155);
        lpd.push_back(0.00645416706920);
        lpd.push_back(-0.00051919282891);
        lpd.push_back(-0.01284118837655);
        lpd.push_back(0.00037212345706);
        lpd.push_back(0.01829725025850);
        lpd.push_back(-0.00795200356952);
        lpd.push_back(-0.02521794435506);
        lpd.push_back(0.02965858822312);
        lpd.push_back(0.02927100294045);
        lpd.push_back(-0.09630227559219);
        lpd.push_back(-0.03849155121162);
        lpd.push_back(0.40072250790936);
        lpd.push_back(0.74269376814242);
        lpd.push_back(0.48469805553706);
        lpd.push_back(-0.02843778080778);
        lpd.push_back(-0.16204679936904);
        lpd.push_back(0.02619852204894);
        lpd.push_back(0.08910160832786);
        lpd.push_back(-0.01523693234758);
        lpd.push_back(-0.04889669606591);
        lpd.push_back(0.00760939409040);
        lpd.push_back(0.02556854558808);
        lpd.push_back(-0.00162932098036);
        lpd.push_back(-0.01038876672659);
        lpd.push_back(0.00054379809563);
        lpd.push_back(0.00371380689803);
        lpd.push_back(-0.00027628401612);
        lpd.push_back(-0.00119692132405);
        lpd.push_back(0.00011735865251);
        lpd.push_back(0.00034385220669);
        lpd.push_back(-0.00003293175202);
        lpd.push_back(-0.00008272690387);
        lpd.push_back(0.00000647382532);
        lpd.push_back(0.00001589700426);
        lpd.push_back(-0.00000089828423);
        lpd.push_back(-0.00000232354967);
        lpd.push_back(0.00000008995011);
        lpd.push_back(0.00000024412960);
        lpd.push_back(-0.00000000612778);
        lpd.push_back(-0.00000001650488);
        lpd.push_back(0.00000000025363);
        lpd.push_back(0.00000000055439);
        hpd.push_back(-0.00000000055439);
        hpd.push_back(0.00000000025363);
        hpd.push_back(0.00000001650488);
        hpd.push_back(-0.00000000612778);
        hpd.push_back(-0.00000024412960);
        hpd.push_back(0.00000008995011);
        hpd.push_back(0.00000232354967);
        hpd.push_back(-0.00000089828423);
        hpd.push_back(-0.00001589700426);
        hpd.push_back(0.00000647382532);
        hpd.push_back(0.00008272690387);
        hpd.push_back(-0.00003293175202);
        hpd.push_back(-0.00034385220669);
        hpd.push_back(0.00011735865251);
        hpd.push_back(0.00119692132405);
        hpd.push_back(-0.00027628401612);
        hpd.push_back(-0.00371380689803);
        hpd.push_back(0.00054379809563);
        hpd.push_back(0.01038876672659);
        hpd.push_back(-0.00162932098036);
        hpd.push_back(-0.02556854558808);
        hpd.push_back(0.00760939409040);
        hpd.push_back(0.04889669606591);
        hpd.push_back(-0.01523693234758);
        hpd.push_back(-0.08910160832786);
        hpd.push_back(0.02619852204894);
        hpd.push_back(0.16204679936904);
        hpd.push_back(-0.02843778080778);
        hpd.push_back(-0.48469805553706);
        hpd.push_back(0.74269376814242);
        hpd.push_back(-0.40072250790936);
        hpd.push_back(-0.03849155121162);
        hpd.push_back(0.09630227559219);
        hpd.push_back(0.02927100294045);
        hpd.push_back(-0.02965858822312);
        hpd.push_back(-0.02521794435506);
        hpd.push_back(0.00795200356952);
        hpd.push_back(0.01829725025850);
        hpd.push_back(-0.00037212345706);
        hpd.push_back(-0.01284118837655);
        hpd.push_back(0.00051919282891);
        hpd.push_back(0.00645416706920);
        hpd.push_back(-0.00046642571155);
        hpd.push_back(-0.00258964866287);
        hpd.push_back(0.00023156681831);
        hpd.push_back(0.00086145325795);
        hpd.push_back(-0.00008209438737);
        hpd.push_back(-0.00023724381672);
        hpd.push_back(0.00002120203482);
        hpd.push_back(0.00005384705071);
        hpd.push_back(-0.00000402586608);
        hpd.push_back(-0.00000989667666);
        hpd.push_back(0.00000055432777);
        hpd.push_back(0.00000142300535);
        hpd.push_back(-0.00000005465704);
        hpd.push_back(-0.00000014986093);
        hpd.push_back(0.00000000380480);
        hpd.push_back(0.00000001014810);
        hpd.push_back(-0.00000000014934);
        hpd.push_back(-0.00000000032641);
        flength = 60;
        break;

    case Coiflet_1:
        lpd.push_back(-0.01565572813546);
        lpd.push_back(-0.07273261951285);
        lpd.push_back(0.38486484686420);
        lpd.push_back(0.85257202021226);
        lpd.push_back(0.33789766245781);
        lpd.push_back(-0.07273261951285);
        hpd.push_back(0.07273261951285);
        hpd.push_back(0.33789766245781);
        hpd.push_back(-0.85257202021226);
        hpd.push_back(0.38486484686420);
        hpd.push_back(0.07273261951285);
        hpd.push_back(-0.01565572813546);
        flength = 6;
        break;

    case Coiflet_2:
        lpd.push_back(-0.00072054944536);
        lpd.push_back(-0.00182320887070);
        lpd.push_back(0.00561143481939);
        lpd.push_back(0.02368017194633);
        lpd.push_back(-0.05943441864646);
        lpd.push_back(-0.07648859907831);
        lpd.push_back(0.41700518442169);
        lpd.push_back(0.81272363544554);
        lpd.push_back(0.38611006682116);
        lpd.push_back(-0.06737255472196);
        lpd.push_back(-0.04146493678176);
        lpd.push_back(0.01638733646352);
        hpd.push_back(-0.01638733646352);
        hpd.push_back(-0.04146493678176);
        hpd.push_back(0.06737255472196);
        hpd.push_back(0.38611006682116);
        hpd.push_back(-0.81272363544554);
        hpd.push_back(0.41700518442169);
        hpd.push_back(0.07648859907831);
        hpd.push_back(-0.05943441864646);
        hpd.push_back(-0.02368017194633);
        hpd.push_back(0.00561143481939);
        hpd.push_back(0.00182320887070);
        hpd.push_back(-0.00072054944536);
        flength = 12;
        break;

    case Coiflet_3:
        lpd.push_back(-0.00003459977284);
        lpd.push_back(-0.00007098330314);
        lpd.push_back(0.00046621696011);
        lpd.push_back(0.00111751877089);
        lpd.push_back(-0.00257451768875);
        lpd.push_back(-0.00900797613666);
        lpd.push_back(0.01588054486362);
        lpd.push_back(0.03455502757306);
        lpd.push_back(-0.08230192710689);
        lpd.push_back(-0.07179982161931);
        lpd.push_back(0.42848347637762);
        lpd.push_back(0.79377722262562);
        lpd.push_back(0.40517690240962);
        lpd.push_back(-0.06112339000267);
        lpd.push_back(-0.06577191128186);
        lpd.push_back(0.02345269614184);
        lpd.push_back(0.00778259642733);
        lpd.push_back(-0.00379351286449);
        hpd.push_back(0.00379351286449);
        hpd.push_back(0.00778259642733);
        hpd.push_back(-0.02345269614184);
        hpd.push_back(-0.06577191128186);
        hpd.push_back(0.06112339000267);
        hpd.push_back(0.40517690240962);
        hpd.push_back(-0.79377722262562);
        hpd.push_back(0.42848347637762);
        hpd.push_back(0.07179982161931);
        hpd.push_back(-0.08230192710689);
        hpd.push_back(-0.03455502757306);
        hpd.push_back(0.01588054486362);
        hpd.push_back(0.00900797613666);
        hpd.push_back(-0.00257451768875);
        hpd.push_back(-0.00111751877089);
        hpd.push_back(0.00046621696011);
        hpd.push_back(0.00007098330314);
        hpd.push_back(-0.00003459977284);
        flength = 18;
        break;

    case Coiflet_4:
        lpd.push_back(-0.00000178498500);
        lpd.push_back(-0.00000325968024);
        lpd.push_back(0.00003122987587);
        lpd.push_back(0.00006233903446);
        lpd.push_back(-0.00025997455249);
        lpd.push_back(-0.00058902075624);
        lpd.push_back(0.00126656192930);
        lpd.push_back(0.00375143615728);
        lpd.push_back(-0.00565828668661);
        lpd.push_back(-0.01521173152795);
        lpd.push_back(0.02508226184486);
        lpd.push_back(0.03933442712334);
        lpd.push_back(-0.09622044203399);
        lpd.push_back(-0.06662747426343);
        lpd.push_back(0.43438605649147);
        lpd.push_back(0.78223893092050);
        lpd.push_back(0.41530840703043);
        lpd.push_back(-0.05607731331675);
        lpd.push_back(-0.08126669968088);
        lpd.push_back(0.02668230015605);
        lpd.push_back(0.01606894396478);
        lpd.push_back(-0.00734616632764);
        lpd.push_back(-0.00162949201260);
        lpd.push_back(0.00089231366858);
        hpd.push_back(-0.00089231366858);
        hpd.push_back(-0.00162949201260);
        hpd.push_back(0.00734616632764);
        hpd.push_back(0.01606894396478);
        hpd.push_back(-0.02668230015605);
        hpd.push_back(-0.08126669968088);
        hpd.push_back(0.05607731331675);
        hpd.push_back(0.41530840703043);
        hpd.push_back(-0.78223893092050);
        hpd.push_back(0.43438605649147);
        hpd.push_back(0.06662747426343);
        hpd.push_back(-0.09622044203399);
        hpd.push_back(-0.03933442712334);
        hpd.push_back(0.02508226184486);
        hpd.push_back(0.01521173152795);
        hpd.push_back(-0.00565828668661);
        hpd.push_back(-0.00375143615728);
        hpd.push_back(0.00126656192930);
        hpd.push_back(0.00058902075624);
        hpd.push_back(-0.00025997455249);
        hpd.push_back(-0.00006233903446);
        hpd.push_back(0.00003122987587);
        hpd.push_back(0.00000325968024);
        hpd.push_back(-0.00000178498500);
        flength = 24;
        break;

    case Coiflet_5:
        lpd.push_back(-0.00000009517657);
        lpd.push_back(-0.00000016744289);
        lpd.push_back(0.00000206376185);
        lpd.push_back(0.00000373465518);
        lpd.push_back(-0.00002131502681);
        lpd.push_back(-0.00004134043227);
        lpd.push_back(0.00014054114970);
        lpd.push_back(0.00030225958181);
        lpd.push_back(-0.00063813134305);
        lpd.push_back(-0.00166286370201);
        lpd.push_back(0.00243337321266);
        lpd.push_back(0.00676418544805);
        lpd.push_back(-0.00916423116248);
        lpd.push_back(-0.01976177894257);
        lpd.push_back(0.03268357426711);
        lpd.push_back(0.04128920875018);
        lpd.push_back(-0.10557420870334);
        lpd.push_back(-0.06203596396290);
        lpd.push_back(0.43799162617184);
        lpd.push_back(0.77428960365296);
        lpd.push_back(0.42156620669085);
        lpd.push_back(-0.05204316317624);
        lpd.push_back(-0.09192001055970);
        lpd.push_back(0.02816802897094);
        lpd.push_back(0.02340815678584);
        lpd.push_back(-0.01013111751985);
        lpd.push_back(-0.00415935878139);
        lpd.push_back(0.00217823635811);
        lpd.push_back(0.00035858968790);
        lpd.push_back(-0.00021208083980);
        hpd.push_back(0.00021208083980);
        hpd.push_back(0.00035858968790);
        hpd.push_back(-0.00217823635811);
        hpd.push_back(-0.00415935878139);
        hpd.push_back(0.01013111751985);
        hpd.push_back(0.02340815678584);
        hpd.push_back(-0.02816802897094);
        hpd.push_back(-0.09192001055970);
        hpd.push_back(0.05204316317624);
        hpd.push_back(0.42156620669085);
        hpd.push_back(-0.77428960365296);
        hpd.push_back(0.43799162617184);
        hpd.push_back(0.06203596396290);
        hpd.push_back(-0.10557420870334);
        hpd.push_back(-0.04128920875018);
        hpd.push_back(0.03268357426711);
        hpd.push_back(0.01976177894257);
        hpd.push_back(-0.00916423116248);
        hpd.push_back(-0.00676418544805);
        hpd.push_back(0.00243337321266);
        hpd.push_back(0.00166286370201);
        hpd.push_back(-0.00063813134305);
        hpd.push_back(-0.00030225958181);
        hpd.push_back(0.00014054114970);
        hpd.push_back(0.00004134043227);
        hpd.push_back(-0.00002131502681);
        hpd.push_back(-0.00000373465518);
        hpd.push_back(0.00000206376185);
        hpd.push_back(0.00000016744289);
        hpd.push_back(-0.00000009517657);
        flength = 30;
        break;

    case Biorthogonal_1_3:
        lpd.push_back(-0.08838834764832);
        lpd.push_back(0.08838834764832);
        lpd.push_back(0.70710678118655);
        lpd.push_back(0.70710678118655);
        lpd.push_back(0.08838834764832);
        lpd.push_back(-0.08838834764832);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.70710678118655);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 6;
        break;

    case Biorthogonal_1_5:
        lpd.push_back(0.01657281518406);
        lpd.push_back(-0.01657281518406);
        lpd.push_back(-0.12153397801644);
        lpd.push_back(0.12153397801644);
        lpd.push_back(0.70710678118655);
        lpd.push_back(0.70710678118655);
        lpd.push_back(0.12153397801644);
        lpd.push_back(-0.12153397801644);
        lpd.push_back(-0.01657281518406);
        lpd.push_back(0.01657281518406);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.70710678118655);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 10;
        break;

    case Biorthogonal_2_2:
        lpd.push_back(0.00000000000000);
        lpd.push_back(-0.17677669529664);
        lpd.push_back(0.35355339059327);
        lpd.push_back(1.06066017177982);
        lpd.push_back(0.35355339059327);
        lpd.push_back(-0.17677669529664);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 6;
        break;

    case Biorthogonal_2_4:
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.03314563036812);
        lpd.push_back(-0.06629126073624);
        lpd.push_back(-0.17677669529664);
        lpd.push_back(0.41984465132951);
        lpd.push_back(0.99436891104358);
        lpd.push_back(0.41984465132951);
        lpd.push_back(-0.17677669529664);
        lpd.push_back(-0.06629126073624);
        lpd.push_back(0.03314563036812);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 10;
        break;


    case Biorthogonal_2_6:
        lpd.push_back(0.00000000000000);
        lpd.push_back(-0.00690533966002);
        lpd.push_back(0.01381067932005);
        lpd.push_back(0.04695630968817);
        lpd.push_back(-0.10772329869639);
        lpd.push_back(-0.16987135563661);
        lpd.push_back(0.44746600996961);
        lpd.push_back(0.96674755240348);
        lpd.push_back(0.44746600996961);
        lpd.push_back(-0.16987135563661);
        lpd.push_back(-0.10772329869639);
        lpd.push_back(0.04695630968817);
        lpd.push_back(0.01381067932005);
        lpd.push_back(-0.00690533966002);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 14;
        break;

    case Biorthogonal_2_8:
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.00151054305063);
        lpd.push_back(-0.00302108610126);
        lpd.push_back(-0.01294751186255);
        lpd.push_back(0.02891610982635);
        lpd.push_back(0.05299848189069);
        lpd.push_back(-0.13491307360774);
        lpd.push_back(-0.16382918343409);
        lpd.push_back(0.46257144047592);
        lpd.push_back(0.95164212189718);
        lpd.push_back(0.46257144047592);
        lpd.push_back(-0.16382918343409);
        lpd.push_back(-0.13491307360774);
        lpd.push_back(0.05299848189069);
        lpd.push_back(0.02891610982635);
        lpd.push_back(-0.01294751186255);
        lpd.push_back(-0.00302108610126);
        lpd.push_back(0.00151054305063);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.70710678118655);
        hpd.push_back(0.35355339059327);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 18;
        break;

    case Biorthogonal_3_1:
        lpd.push_back(-0.35355339059327);
        lpd.push_back(1.06066017177982);
        lpd.push_back(1.06066017177982);
        lpd.push_back(-0.35355339059327);
        hpd.push_back(-0.17677669529664);
        hpd.push_back(0.53033008588991);
        hpd.push_back(-0.53033008588991);
        hpd.push_back(0.17677669529664);
        flength = 4;
        break;

    case Biorthogonal_3_3:
        lpd.push_back(0.06629126073624);
        lpd.push_back(-0.19887378220872);
        lpd.push_back(-0.15467960838456);
        lpd.push_back(0.99436891104358);
        lpd.push_back(0.99436891104358);
        lpd.push_back(-0.15467960838456);
        lpd.push_back(-0.19887378220872);
        lpd.push_back(0.06629126073624);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.17677669529664);
        hpd.push_back(0.53033008588991);
        hpd.push_back(-0.53033008588991);
        hpd.push_back(0.17677669529664);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 8;
        break;

    case Biorthogonal_3_5:
        lpd.push_back(-0.01381067932005);
        lpd.push_back(0.04143203796015);
        lpd.push_back(0.05248058141619);
        lpd.push_back(-0.26792717880897);
        lpd.push_back(-0.07181553246426);
        lpd.push_back(0.96674755240348);
        lpd.push_back(0.96674755240348);
        lpd.push_back(-0.07181553246426);
        lpd.push_back(-0.26792717880897);
        lpd.push_back(0.05248058141619);
        lpd.push_back(0.04143203796015);
        lpd.push_back(-0.01381067932005);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.17677669529664);
        hpd.push_back(0.53033008588991);
        hpd.push_back(-0.53033008588991);
        hpd.push_back(0.17677669529664);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 12;
        break;

    case Biorthogonal_3_7:
        lpd.push_back(0.00302108610126);
        lpd.push_back(-0.00906325830378);
        lpd.push_back(-0.01683176542131);
        lpd.push_back(0.07466398507402);
        lpd.push_back(0.03133297870736);
        lpd.push_back(-0.30115912592284);
        lpd.push_back(-0.02649924094535);
        lpd.push_back(0.95164212189718);
        lpd.push_back(0.95164212189718);
        lpd.push_back(-0.02649924094535);
        lpd.push_back(-0.30115912592284);
        lpd.push_back(0.03133297870736);
        lpd.push_back(0.07466398507402);
        lpd.push_back(-0.01683176542131);
        lpd.push_back(-0.00906325830378);
        lpd.push_back(0.00302108610126);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.17677669529664);
        hpd.push_back(0.53033008588991);
        hpd.push_back(-0.53033008588991);
        hpd.push_back(0.17677669529664);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 16;
        break;

    case Biorthogonal_3_9:
        lpd.push_back(-0.00067974437278);
        lpd.push_back(0.00203923311835);
        lpd.push_back(0.00506031921961);
        lpd.push_back(-0.02061891264111);
        lpd.push_back(-0.01411278793018);
        lpd.push_back(0.09913478249423);
        lpd.push_back(0.01230013626942);
        lpd.push_back(-0.32019196836078);
        lpd.push_back(0.00205002271157);
        lpd.push_back(0.94212570067821);
        lpd.push_back(0.94212570067821);
        lpd.push_back(0.00205002271157);
        lpd.push_back(-0.32019196836078);
        lpd.push_back(0.01230013626942);
        lpd.push_back(0.09913478249423);
        lpd.push_back(-0.01411278793018);
        lpd.push_back(-0.02061891264111);
        lpd.push_back(0.00506031921961);
        lpd.push_back(0.00203923311835);
        lpd.push_back(-0.00067974437278);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.17677669529664);
        hpd.push_back(0.53033008588991);
        hpd.push_back(-0.53033008588991);
        hpd.push_back(0.17677669529664);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 20;
        break;

    case Biorthogonal_4_4:
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.03782845550726);
        lpd.push_back(-0.02384946501956);
        lpd.push_back(-0.11062440441844);
        lpd.push_back(0.37740285561283);
        lpd.push_back(0.85269867900889);
        lpd.push_back(0.37740285561283);
        lpd.push_back(-0.11062440441844);
        lpd.push_back(-0.02384946501956);
        lpd.push_back(0.03782845550726);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(-0.06453888262870);
        hpd.push_back(0.04068941760916);
        hpd.push_back(0.41809227322162);
        hpd.push_back(-0.78848561640558);
        hpd.push_back(0.41809227322162);
        hpd.push_back(0.04068941760916);
        hpd.push_back(-0.06453888262870);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        flength = 10;
        break;

    case Biorthogonal_5_5:
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.03968708834741);
        lpd.push_back(0.00794810863724);
        lpd.push_back(-0.05446378846824);
        lpd.push_back(0.34560528195603);
        lpd.push_back(0.73666018142821);
        lpd.push_back(0.34560528195603);
        lpd.push_back(-0.05446378846824);
        lpd.push_back(0.00794810863724);
        lpd.push_back(0.03968708834741);
        lpd.push_back(0.00000000000000);
        hpd.push_back(-0.01345670945912);
        hpd.push_back(-0.00269496688011);
        hpd.push_back(0.13670658466433);
        hpd.push_back(-0.09350469740094);
        hpd.push_back(-0.47680326579848);
        hpd.push_back(0.89950610974865);
        hpd.push_back(-0.47680326579848);
        hpd.push_back(-0.09350469740094);
        hpd.push_back(0.13670658466433);
        hpd.push_back(-0.00269496688011);
        hpd.push_back(-0.01345670945912);
        hpd.push_back(0.00000000000000);
        flength = 12;
        break;

    case Biorthogonal_6_8:
        lpd.push_back(0.00000000000000);
        lpd.push_back(0.00190883173648);
        lpd.push_back(-0.00191428612909);
        lpd.push_back(-0.01699063986760);
        lpd.push_back(0.01193456527973);
        lpd.push_back(0.04973290349094);
        lpd.push_back(-0.07726317316720);
        lpd.push_back(-0.09405920349574);
        lpd.push_back(0.42079628460983);
        lpd.push_back(0.82592299745840);
        lpd.push_back(0.42079628460983);
        lpd.push_back(-0.09405920349574);
        lpd.push_back(-0.07726317316720);
        lpd.push_back(0.04973290349094);
        lpd.push_back(0.01193456527973);
        lpd.push_back(-0.01699063986760);
        lpd.push_back(-0.00191428612909);
        lpd.push_back(0.00190883173648);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(0.01442628250562);
        hpd.push_back(-0.01446750489679);
        hpd.push_back(-0.07872200106263);
        hpd.push_back(0.04036797903034);
        hpd.push_back(0.41784910915027);
        hpd.push_back(-0.75890772945365);
        hpd.push_back(0.41784910915027);
        hpd.push_back(0.04036797903034);
        hpd.push_back(-0.07872200106263);
        hpd.push_back(-0.01446750489679);
        hpd.push_back(0.01442628250562);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        hpd.push_back(0.00000000000000);
        hpd.push_back(-0.00000000000000);
        flength = 18;
        break;

    case Meyer:
        lpd.push_back(0.00000000000000);
        lpd.push_back(-0.00000150974086);
        lpd.push_back(0.00000127876676);
        lpd.push_back(0.00000044958556);
        lpd.push_back(-0.00000209656887);
        lpd.push_back(0.00000172322355);
        lpd.push_back(0.00000069808228);
        lpd.push_back(-0.00000287940803);
        lpd.push_back(0.00000238314839);
        lpd.push_back(0.00000098251560);
        lpd.push_back(-0.00000421778919);
        lpd.push_back(0.00000335350154);
        lpd.push_back(0.00000167472186);
        lpd.push_back(-0.00000603450134);
        lpd.push_back(0.00000483755580);
        lpd.push_back(0.00000240228802);
        lpd.push_back(-0.00000955630985);
        lpd.push_back(0.00000721652769);
        lpd.push_back(0.00000484907830);
        lpd.push_back(-0.00001420692858);
        lpd.push_back(0.00001050391427);
        lpd.push_back(0.00000618758030);
        lpd.push_back(-0.00002443800585);
        lpd.push_back(0.00002010638769);
        lpd.push_back(0.00001499352360);
        lpd.push_back(-0.00004642876428);
        lpd.push_back(0.00003234131191);
        lpd.push_back(0.00003740966576);
        lpd.push_back(-0.00010277900508);
        lpd.push_back(0.00002446195684);
        lpd.push_back(0.00014971351539);
        lpd.push_back(-0.00007559287026);
        lpd.push_back(-0.00013991314822);
        lpd.push_back(-0.00009351289388);
        lpd.push_back(0.00016118981973);
        lpd.push_back(0.00085950021376);
        lpd.push_back(-0.00057818579527);
        lpd.push_back(-0.00270216873394);
        lpd.push_back(0.00219477533646);
        lpd.push_back(0.00604551059646);
        lpd.push_back(-0.00638672861855);
        lpd.push_back(-0.01104464190054);
        lpd.push_back(0.01525091315859);
        lpd.push_back(0.01740388821018);
        lpd.push_back(-0.03209406335451);
        lpd.push_back(-0.02432178395952);
        lpd.push_back(0.06366730088447);
        lpd.push_back(0.03062124394342);
        lpd.push_back(-0.13269661535886);
        lpd.push_back(-0.03504828739060);
        lpd.push_back(0.44409503076653);
        lpd.push_back(0.74375100490379);
        lpd.push_back(0.44409503076653);
        lpd.push_back(-0.03504828739060);
        lpd.push_back(-0.13269661535886);
        lpd.push_back(0.03062124394342);
        lpd.push_back(0.06366730088447);
        lpd.push_back(-0.02432178395952);
        lpd.push_back(-0.03209406335451);
        lpd.push_back(0.01740388821018);
        lpd.push_back(0.01525091315859);
        lpd.push_back(-0.01104464190054);
        lpd.push_back(-0.00638672861855);
        lpd.push_back(0.00604551059646);
        lpd.push_back(0.00219477533646);
        lpd.push_back(-0.00270216873394);
        lpd.push_back(-0.00057818579527);
        lpd.push_back(0.00085950021376);
        lpd.push_back(0.00016118981973);
        lpd.push_back(-0.00009351289388);
        lpd.push_back(-0.00013991314822);
        lpd.push_back(-0.00007559287026);
        lpd.push_back(0.00014971351539);
        lpd.push_back(0.00002446195684);
        lpd.push_back(-0.00010277900508);
        lpd.push_back(0.00003740966576);
        lpd.push_back(0.00003234131191);
        lpd.push_back(-0.00004642876428);
        lpd.push_back(0.00001499352360);
        lpd.push_back(0.00002010638769);
        lpd.push_back(-0.00002443800585);
        lpd.push_back(0.00000618758030);
        lpd.push_back(0.00001050391427);
        lpd.push_back(-0.00001420692858);
        lpd.push_back(0.00000484907830);
        lpd.push_back(0.00000721652769);
        lpd.push_back(-0.00000955630985);
        lpd.push_back(0.00000240228802);
        lpd.push_back(0.00000483755580);
        lpd.push_back(-0.00000603450134);
        lpd.push_back(0.00000167472186);
        lpd.push_back(0.00000335350154);
        lpd.push_back(-0.00000421778919);
        lpd.push_back(0.00000098251560);
        lpd.push_back(0.00000238314839);
        lpd.push_back(-0.00000287940803);
        lpd.push_back(0.00000069808228);
        lpd.push_back(0.00000172322355);
        lpd.push_back(-0.00000209656887);
        lpd.push_back(0.00000044958556);
        lpd.push_back(0.00000127876676);
        lpd.push_back(-0.00000150974086);
        hpd.push_back(0.00000150974086);
        hpd.push_back(0.00000127876676);
        hpd.push_back(-0.00000044958556);
        hpd.push_back(-0.00000209656887);
        hpd.push_back(-0.00000172322355);
        hpd.push_back(0.00000069808228);
        hpd.push_back(0.00000287940803);
        hpd.push_back(0.00000238314839);
        hpd.push_back(-0.00000098251560);
        hpd.push_back(-0.00000421778919);
        hpd.push_back(-0.00000335350154);
        hpd.push_back(0.00000167472186);
        hpd.push_back(0.00000603450134);
        hpd.push_back(0.00000483755580);
        hpd.push_back(-0.00000240228802);
        hpd.push_back(-0.00000955630985);
        hpd.push_back(-0.00000721652769);
        hpd.push_back(0.00000484907830);
        hpd.push_back(0.00001420692858);
        hpd.push_back(0.00001050391427);
        hpd.push_back(-0.00000618758030);
        hpd.push_back(-0.00002443800585);
        hpd.push_back(-0.00002010638769);
        hpd.push_back(0.00001499352360);
        hpd.push_back(0.00004642876428);
        hpd.push_back(0.00003234131191);
        hpd.push_back(-0.00003740966576);
        hpd.push_back(-0.00010277900508);
        hpd.push_back(-0.00002446195684);
        hpd.push_back(0.00014971351539);
        hpd.push_back(0.00007559287026);
        hpd.push_back(-0.00013991314822);
        hpd.push_back(0.00009351289388);
        hpd.push_back(0.00016118981973);
        hpd.push_back(-0.00085950021376);
        hpd.push_back(-0.00057818579527);
        hpd.push_back(0.00270216873394);
        hpd.push_back(0.00219477533646);
        hpd.push_back(-0.00604551059646);
        hpd.push_back(-0.00638672861855);
        hpd.push_back(0.01104464190054);
        hpd.push_back(0.01525091315859);
        hpd.push_back(-0.01740388821018);
        hpd.push_back(-0.03209406335451);
        hpd.push_back(0.02432178395952);
        hpd.push_back(0.06366730088447);
        hpd.push_back(-0.03062124394342);
        hpd.push_back(-0.13269661535886);
        hpd.push_back(0.03504828739060);
        hpd.push_back(0.44409503076653);
        hpd.push_back(-0.74375100490379);
        hpd.push_back(0.44409503076653);
        hpd.push_back(0.03504828739060);
        hpd.push_back(-0.13269661535886);
        hpd.push_back(-0.03062124394342);
        hpd.push_back(0.06366730088447);
        hpd.push_back(0.02432178395952);
        hpd.push_back(-0.03209406335451);
        hpd.push_back(-0.01740388821018);
        hpd.push_back(0.01525091315859);
        hpd.push_back(0.01104464190054);
        hpd.push_back(-0.00638672861855);
        hpd.push_back(-0.00604551059646);
        hpd.push_back(0.00219477533646);
        hpd.push_back(0.00270216873394);
        hpd.push_back(-0.00057818579527);
        hpd.push_back(-0.00085950021376);
        hpd.push_back(0.00016118981973);
        hpd.push_back(0.00009351289388);
        hpd.push_back(-0.00013991314822);
        hpd.push_back(0.00007559287026);
        hpd.push_back(0.00014971351539);
        hpd.push_back(-0.00002446195684);
        hpd.push_back(-0.00010277900508);
        hpd.push_back(-0.00003740966576);
        hpd.push_back(0.00003234131191);
        hpd.push_back(0.00004642876428);
        hpd.push_back(0.00001499352360);
        hpd.push_back(-0.00002010638769);
        hpd.push_back(-0.00002443800585);
        hpd.push_back(-0.00000618758030);
        hpd.push_back(0.00001050391427);
        hpd.push_back(0.00001420692858);
        hpd.push_back(0.00000484907830);
        hpd.push_back(-0.00000721652769);
        hpd.push_back(-0.00000955630985);
        hpd.push_back(-0.00000240228802);
        hpd.push_back(0.00000483755580);
        hpd.push_back(0.00000603450134);
        hpd.push_back(0.00000167472186);
        hpd.push_back(-0.00000335350154);
        hpd.push_back(-0.00000421778919);
        hpd.push_back(-0.00000098251560);
        hpd.push_back(0.00000238314839);
        hpd.push_back(0.00000287940803);
        hpd.push_back(0.00000069808228);
        hpd.push_back(-0.00000172322355);
        hpd.push_back(-0.00000209656887);
        hpd.push_back(-0.00000044958556);
        hpd.push_back(0.00000127876676);
        hpd.push_back(0.00000150974086);
        hpd.push_back(0.00000000000000);
        flength = 102;
        break;
    }

    // avoid compiler warning for unused value if assert is not compiled in:
    (void)flength;

    assert(flength == int(lpd.size()));
    assert(flength == int(hpd.size()));
}

