/* -*- c-basic-offset: 4 indent-tabs-mode: nil -*-  vi:set ts=8 sts=4 sw=4: */
/*
    QM DSP Library

    Centre for Digital Music, Queen Mary, University of London.
    This file copyright 2009 <PERSON>.

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License as
    published by the Free Software Foundation; either version 2 of the
    License, or (at your option) any later version.  See the file
    COPYING included with this distribution for more information.
*/

#ifndef QM_DSP_WAVELET_H
#define QM_DSP_WAVELET_H

#include <string>
#include <vector>

class Wavelet
{
public:
    enum Type {
        Haar = 0,
        Daubechies_2,
        Daubechies_3,
        Daubechies_4,
        Daubechies_5,
        Da<PERSON><PERSON><PERSON>_6,
        Da<PERSON><PERSON><PERSON>_7,
        Da<PERSON><PERSON><PERSON>_8,
        Da<PERSON><PERSON><PERSON>_9,
        Da<PERSON><PERSON><PERSON>_10,
        <PERSON><PERSON><PERSON><PERSON>_20,
        <PERSON><PERSON><PERSON><PERSON>_40,
        <PERSON><PERSON><PERSON>_2,
        <PERSON><PERSON><PERSON>_3,
        <PERSON><PERSON><PERSON>_4,
        <PERSON><PERSON><PERSON>_5,
        <PERSON><PERSON><PERSON><PERSON>6,
        <PERSON><PERSON><PERSON><PERSON>7,
        <PERSON><PERSON><PERSON><PERSON>8,
        <PERSON><PERSON><PERSON><PERSON>9,
        <PERSON><PERSON><PERSON><PERSON>10,
        <PERSON><PERSON><PERSON><PERSON>20,
        <PERSON><PERSON><PERSON><PERSON>30,
        <PERSON><PERSON><PERSON>_1,
        <PERSON><PERSON><PERSON>_2,
        <PERSON><PERSON><PERSON>_3,
        <PERSON><PERSON><PERSON>_4,
        <PERSON><PERSON><PERSON>_5,
        <PERSON><PERSON><PERSON><PERSON><PERSON>_1_3,
        <PERSON><PERSON>thogonal_1_5,
        Biorthogonal_2_2,
        Biorthogonal_2_4,
        Biorthogonal_2_6,
        Biorthogonal_2_8,
        Biorthogonal_3_1,
        Biorthogonal_3_3,
        Biorthogonal_3_5,
        Biorthogonal_3_7,
        Biorthogonal_3_9,
        Biorthogonal_4_4,
        Biorthogonal_5_5,
        Biorthogonal_6_8,
        Meyer,

        LastType = Meyer
    };

    static std::string getWaveletName(Type);

    static void createDecompositionFilters(Type,
                                           std::vector<double> &lpd,
                                           std::vector<double> &hpd);
};

#endif
