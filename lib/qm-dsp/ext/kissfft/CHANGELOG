1.3.0 2012-07-18
  removed non-standard malloc.h from kiss_fft.h

  moved -lm to end of link line

  checked various return values

  converted python Numeric code to NumPy
 
  fixed test of int32_t on 64 bit OS

  added padding in a couple of places to allow SIMD alignment of structs

1.2.9 2010-05-27
  threadsafe ( including OpenMP )

  first edition of kissfft.hh the C++ template fft engine
 
1.2.8 
  Changed memory.h to string.h -- apparently more standard
  
  Added openmp extensions.  This can have fairly linear speedups for larger FFT sizes.

1.2.7 
  Shrank the real-fft memory footprint. Thanks to <PERSON>.

1.2.6 (Nov 14, 2006) The "thanks to GenArts" release.
  Added multi-dimensional real-optimized FFT, see tools/kiss_fftndr
  Thanks go to GenArts, Inc. for sponsoring the development.

1.2.5 (June 27, 2006) The "release for no good reason" release.
   Changed some harmless code to make some compilers' warnings go away.
   Added some more digits to pi -- why not.
   Added kiss_fft_next_fast_size() function to help people decide how much to pad.
   Changed multidimensional test from 8 dimensions to only 3 to avoid testing 
   problems with fixed point (sorry <PERSON><PERSON>).

1.2.4 (Oct 27, 2005)   The "oops, inverse fixed point real fft was borked" release. 
   Fixed scaling bug for inverse fixed point real fft -- also fixed test code that should've been failing.
    Thanks to <PERSON>-<PERSON> Valin for bug report.

   Use sys/types.h for more portable types than short,int,long => int16_t,int32_t,int64_t
   If your system does not have these, you may need to define them -- but at least it breaks in a 
   loud and easily fixable way -- unlike silently using the wrong size type.

   Hopefully tools/psdpng.c is fixed -- thanks to Steve Kellog for pointing out the weirdness.

1.2.3 (June 25, 2005)   The "you want to use WHAT as a sample" release.
    Added ability to use 32 bit fixed point samples -- requires a 64 bit intermediate result, a la 'long long'

    Added ability to do 4 FFTs in parallel by using SSE SIMD instructions. This is accomplished by
    using the __m128 (vector of 4 floats) as kiss_fft_scalar.  Define USE_SIMD to use this.
    
    I know, I know ...  this is drifting a bit from the "kiss" principle, but the speed advantages 
    make it worth it for some.  Also recent gcc makes it SOO easy to use vectors of 4 floats like a POD type.

1.2.2 (May 6, 2005)   The Matthew release
    Replaced fixed point division with multiply&shift.  Thanks to Jean-Marc Valin for 
    discussions regarding.  Considerable speedup for fixed-point.

    Corrected overflow protection in real fft routines  when using fixed point.
    Finder's Credit goes to Robert Oschler of robodance for pointing me at the bug.
    This also led to the CHECK_OVERFLOW_OP macro.

1.2.1 (April 4, 2004) 
    compiles cleanly with just about every -W warning flag under the sun

    reorganized kiss_fft_state so it could be read-only/const. This may be useful for embedded systems
    that are willing to predeclare twiddle factors, factorization.

    Fixed C_MUL,S_MUL on 16-bit platforms.

    tmpbuf will only be allocated if input & output buffers are same
    scratchbuf will only be allocated for ffts that are not multiples of 2,3,5
 
    NOTE: The tmpbuf,scratchbuf changes may require synchronization code for multi-threaded apps.


1.2 (Feb 23, 2004)
    interface change -- cfg object is forward declaration of struct instead of void*
    This maintains type saftey and lets the compiler warn/error about stupid mistakes.
            (prompted by suggestion from Erik de Castro Lopo)

    small speed improvements

    added psdpng.c -- sample utility that will create png spectrum "waterfalls" from an input file
        ( not terribly useful yet)

1.1.1 (Feb 1, 2004 )
    minor bug fix -- only affects odd rank, in-place, multi-dimensional FFTs

1.1 : (Jan 30,2004)
    split sample_code/ into test/ and tools/

    Removed 2-D fft and added N-D fft (arbitrary)

    modified fftutil.c to allow multi-d FFTs

    Modified core fft routine to allow an input stride via kiss_fft_stride()
    (eased support of multi-D ffts)

    Added fast convolution filtering (FIR filtering using overlap-scrap method, with tail scrap)

    Add kfc.[ch]: the KISS FFT Cache. It takes care of allocs for you ( suggested by Oscar Lesta ).

1.0.1 (Dec 15, 2003)
    fixed bug that occurred when nfft==1. Thanks to Steven Johnson.
    
1.0 : (Dec 14, 2003)
    changed kiss_fft function from using a single buffer, to two buffers.
    If the same buffer pointer is supplied for both in and out, kiss will
    manage the buffer copies.

    added kiss_fft2d and kiss_fftr as separate source files (declarations in kiss_fft.h )

0.4 :(Nov 4,2003) optimized for radix 2,3,4,5

0.3 :(Oct 28, 2003) woops, version 2 didn't actually factor out any radices other than 2.
        Thanks to Steven Johnson for finding this one.

0.2 :(Oct 27, 2003) added mixed radix, only radix 2,4 optimized versions

0.1 :(May 19 2003)  initial release, radix 2 only
