# yaml-language-server: $schema=https://json.schemastore.org/github-workflow

name: "Detect stale issues and pull request"
on:
  schedule:
    - cron: "0 0 * * *"

permissions:
  contents: read

jobs:
  stale:
    permissions:
      issues: write # for actions/stale to close stale issues
      pull-requests: write # for actions/stale to close stale PRs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-pr-message: "This PR is marked as stale because it has been open 90 days with no activity."
          stale-pr-label: "stale"
          days-before-pr-stale: 90
          days-before-pr-close: -1
          exempt-pr-labels: "needs review"
          close-issue-message: "Expired for Mixxx because there has been no activity for 60 days."
          stale-issue-label: "stale"
          days-before-issue-stale: 60
          days-before-issue-close: 0
          only-issue-labels: "incomplete"
