# yaml-language-server: $schema=https://json.schemastore.org/github-workflow
#
# This workflow will triage pull requests and apply a label based on the
# paths that are modified in the pull request.
#
# To use this workflow, you will need to set up a .github/labeler.yml
# file with configuration.  For more information, see:
# https://github.com/actions/labeler

name: "Pull Request Labeler"
on:
  - pull_request_target

permissions:
  contents: read

jobs:
  triage:
    permissions:
      contents: read # for actions/labeler to determine modified files
      pull-requests: write # for actions/labeler to add labels to PRs
    runs-on: ubuntu-latest

    steps:
      - uses: actions/labeler@v5.0.0
        with:
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          sync-labels: false
