# yaml-language-server: $schema=https://json.schemastore.org/github-workflow

name: Changelog

on:
  workflow_call:

permissions: {}
jobs:
  trigger-changelog-update:
    name: Trigger Changelog update on manual repository
    runs-on: ubuntu-latest
    steps:
      - name: Get current branch name
        uses: nelonoel/branch-name@v1.0.1
      - name: Start workflow run on manual repository
        uses: peter-evans/repository-dispatch@v3
        if: env.MIXXXBOT_TOKEN != null
        with:
          token: ${{ env.MIXXXBOT_TOKEN }}
          repository: mixxxdj/manual
          event-type: update-changelog
          client-payload: '{"branch": "${{ env.BRANCH_NAME }}", "ref": "${{ github.ref }}", "sha": "${{ github.sha }}"}'
        env:
          MIXXXBOT_TOKEN: ${{ secrets.MIXXXBOT_CHANGELOG_AUTOUPDATER_PAT }}
