# yaml-language-server: $schema=https://json.schemastore.org/github-workflow

name: build-checks

on:
  workflow_call:

permissions:
  contents: read #  to fetch code (actions/checkout)

jobs:
  build-checks:
    permissions:
      contents: read #  to fetch code (actions/checkout)
      checks: write #  to create new checks (coverallsapp/github-action)

    strategy:
      matrix:
        include:
          - name: clazy
          - name: clang-tidy
          - name: coverage
    runs-on: ubuntu-24.04
    name: ${{ matrix.name }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v4.2.2
      - name: Add clazy PPA
        if: matrix.name == 'clazy'
        # Ubuntu2404 comes with v1.11, which is too old for the Qt6.4 shipped with Ubuntu 24.04
        run: |
          sudo add-apt-repository ppa:daschuer/clazy
          sudo apt update
      - name: Install build dependencies
        run: tools/debian_buildenv.sh setup
      - name: Create build directory
        run: mkdir build
      - name: Configure (clazy)
        if: matrix.name == 'clazy'
        # Disable optimizations as workaround for Clang 9 bug: https://bugs.llvm.org/show_bug.cgi?id=45034
        run: |
          clazy --version
          cmake \
            -DCMAKE_BUILD_TYPE=Debug \
            -DWARNINGS_FATAL=ON \
            -DQT6=ON \
            -DOPTIMIZE=off \
            -DBATTERY=ON \
            -DBROADCAST=ON \
            -DBULK=ON \
            -DHID=ON \
            -DLILV=ON \
            -DOPUS=ON \
            -DQTKEYCHAIN=ON \
            -DVINYLCONTROL=ON \
            -DFFMPEG=ON \
            -DKEYFINDER=ON \
            -DLOCALECOMPARE=ON \
            -DMAD=ON \
            -DMODPLUG=ON \
            -DWAVPACK=ON \
            ..
        working-directory: build
        env:
          LD: clang++
          CC: clang
          CXX: clazy
      - name: Configure (clang-tidy)
        if: matrix.name == 'clang-tidy'
        # Our code contains the use of infinity(), which Clang >= 18 reports
        # as error, if you compile with -fast-math
        run: |
          cmake \
            -DCMAKE_BUILD_TYPE=Debug \
            -DCLANG_TIDY=clang-tidy \
            -DWARNINGS_FATAL=ON \
            -DQT6=ON \
            -DBATTERY=ON \
            -DBROADCAST=ON \
            -DBULK=ON \
            -DHID=ON \
            -DLILV=ON \
            -DOPUS=ON \
            -DQTKEYCHAIN=ON \
            -DVINYLCONTROL=ON \
            -DFFMPEG=ON \
            -DKEYFINDER=ON \
            -DLOCALECOMPARE=ON \
            -DMAD=ON \
            -DMODPLUG=ON \
            -DWAVPACK=ON \
            -DCMAKE_CXX_FLAGS="-Wno-nan-infinity-disabled" \
            ..
        working-directory: build
        env:
          LD: clang++
          CC: clang
          CXX: clang++
      - name: Configure (coverage)
        if: matrix.name == 'coverage'
        run: |
          cmake \
            -DCMAKE_BUILD_TYPE=Debug \
            -DOPTIMIZE=off \
            -DQT6=ON \
            -DQML=ON \
            -DCOVERAGE=ON \
            -DWARNINGS_FATAL=OFF \
            -DDEBUG_ASSERTIONS_FATAL=OFF \
            -DBATTERY=ON \
            -DBROADCAST=ON \
            -DBULK=ON \
            -DHID=ON \
            -DLILV=ON \
            -DOPUS=ON \
            -DQTKEYCHAIN=ON \
            -DVINYLCONTROL=ON \
            -DFFMPEG=ON \
            -DKEYFINDER=ON \
            -DLOCALECOMPARE=ON \
            -DMAD=ON \
            -DMODPLUG=ON \
            -DWAVPACK=ON \
            ..
        working-directory: build
      - name: Set up problem matcher
        uses: ammaraskar/gcc-problem-matcher@0.3.0
      - name: Build
        # Do not abort on errors and build/check the whole project
        run: cmake --build . -j $(nproc) -- --keep-going
        working-directory: build
        env:
          CLAZY_CHECKS: level2,container-inside-loop,heap-allocated-small-trivial-type,isempty-vs-count,qhash-with-char-pointer-key,qproperty-type-mismatch,tr-non-literal,no-rule-of-two-soft,no-non-pod-global-static,no-qproperty-without-notify,no-qstring-allocations,no-function-args-by-value,no-copyable-polymorphic,no-ctor-missing-parent-argument,no-missing-qobject-macro,no-rule-of-three,no-returning-void-expression
          CLAZY_IGNORE_DIRS: lib/.*
      - name: "Test"
        if: matrix.name == 'coverage'
        run: ctest --timeout 45
        working-directory: build
        env:
          # Render analyzer waveform tests to an offscreen buffer
          QT_QPA_PLATFORM: ${{ matrix.qt_qpa_platform }}
          GTEST_COLOR: 1
          # Only use single thread to prevent *.gcna files from overwriting each other
          CTEST_PARALLEL_LEVEL: 1
          CTEST_OUTPUT_ON_FAILURE: 1
      - name: "Generate Coverage Report"
        if: matrix.name == 'coverage'
        run: >-
          lcov
          --capture
          --directory .
          --base-directory ..
          --include "${PWD%/*}/src/*"
          --exclude "${PWD%/*}/src/test/*"
          --exclude "${PWD%/*}/build/*"
          --exclude "${PWD%/*}/lib/*"
          --output-file lcov.info
        working-directory: build
      - name: "Upload Coverage Report to coveralls.io"
        if: matrix.name == 'coverage'
        continue-on-error: true
        uses: coverallsapp/github-action@v2.3.6
        with:
          flag-name: ubuntu-24.04
          path-to-lcov: build/lcov.info
          github-token: ${{ secrets.GITHUB_TOKEN }}
