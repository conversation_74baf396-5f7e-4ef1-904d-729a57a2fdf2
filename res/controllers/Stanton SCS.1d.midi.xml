<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.9.0">
    <info>
        <name>Stanton SCS.1d (unfinished)</name>
        <author><PERSON></author>
        <description>This is a work-in-progress preset for a single Stanton SCS.1d turntable controller.
        Requires scripting and native HSS1394 support on OSX and Windows,
        or ALSA's HSS1394 MIDI driver on Linux (in the ALSA tree as of 12 November 2012.)</description>
        <manual>stanton_scs1d</manual>
    </info>
    <controller id="Stanton SCS.1d">
        <scriptfiles>
            <file filename="Stanton-SCS1d-scripts.js" functionprefix="StantonSCS1d"/>
        </scriptfiles>

            <controls>
                <control>   <!-- Firmware version response, platter message on Linux -->
                    <status>0xf0</status>
                    <group>[Master]</group>
                    <key>StantonSCS1d.inboundSysex</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <!--    Encoder section    -->

                <control>
                    <status>0x90</status>
                    <midino>0x04</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoderBank</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x04</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoderBank</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xb0</status>
                    <midino>0x7c</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder4</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x03</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder4</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x03</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder4</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x11</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display4button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x11</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display4button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xb0</status>
                    <midino>0x7d</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder3</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x02</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder3</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x02</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder3</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x10</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display3button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x10</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display3button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xb0</status>
                    <midino>0x7e</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x18</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display2button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x18</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display2button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xb0</status>
                    <midino>0x7f</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x0</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x0</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.encoder1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x19</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display1button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x19</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.display1button</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Transport Buttons    -->

                <control>
                    <status>0x90</status>
                    <midino>0x37</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pfl</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x37</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pfl</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x29</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.playButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x29</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.playButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2b</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.cueButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2b</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.cueButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2a</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.syncButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2a</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.syncButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x28</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.bpmButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x28</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.bpmButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2e</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.ffwd</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2e</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.ffwd</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2f</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.rew</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2f</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.rew</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Platter    -->

                <control>
                    <status>0xf9</status>
                    <group>[Master]</group>
                    <key>StantonSCS1d.vinylMoved</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Mode button section    -->

                <control>
                    <status>0x90</status>
                    <midino>0x1E</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.EnterButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1E</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.EnterButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x40</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.DeckChange</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x40</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.DeckChange</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1A</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.setupButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1A</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.setupButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1B</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.controlButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1B</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.controlButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1C</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.browseButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1C</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.browseButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1D</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.vinylButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1D</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.vinylButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Pitch slider section    -->

                <control>
                    <status>0x90</status>
                    <midino>0x06</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.rangeButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x06</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.rangeButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x07</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pitchReset</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x07</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pitchReset</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xb0</status>
                    <midino>0x00</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pitchSlider</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Trigger pad section    -->

                <control>
                    <status>0x90</status>
                    <midino>0x20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x21</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x21</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x22</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x22</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x23</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x23</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.pad</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x30</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x30</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x31</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x31</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x32</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop3</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x32</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop3</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x33</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop4</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x33</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padTop4</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x34</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.velocityButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x34</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.velocityButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x35</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padBankButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                                <control>
                    <status>0x80</status>
                    <midino>0x35</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padBankButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x36</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padBankButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x36</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.padBankButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Trigger/Loop Buttons - Banks   -->

                <control>
                    <status>0x90</status>
                    <midino>0x16</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x16</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x17</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x17</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x5</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x5</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerBankSelect</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>

                <!--    Trigger/Loop Buttons    -->

                <control>
                    <status>0x90</status>
                    <midino>8</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>8</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>9</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>9</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>10</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>10</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>11</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>11</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>12</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>12</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>13</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>13</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>14</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>14</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>15</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>15</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>18</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>18</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>19</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>19</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>21</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>21</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1d.triggerButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
            </controls>
        </controller>
    </MixxxMIDIPreset>
