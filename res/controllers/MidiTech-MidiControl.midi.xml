<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.6.2+">
  <info>
    <name>MidiTech MidiControl</name>
    <author></author>
    <description>MIDI Mapping for MidiTech MidiControl</description>
    <manual>miditech_midicontrol</manual>
  </info>
  <controller id="MidiTech-MidiControl" port="Port">
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x10</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x0b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x0c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x0d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x0e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x29</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x29</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x2d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x2d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x2e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x2e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x30</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x30</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x11</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x12</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x15</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x41</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x41</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x4c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x4c</midino>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
