<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.2.3" schemaVersion="1">
  <info>
    <name>Numark iDJ Live II</name>
    <author><PERSON></author>
    <description>Complete mapping for iDJ Live II</description>
    <manual>numark_idj_live_ii</manual>
  </info>
  <controller id="Numark">
    <scriptfiles>
      <file functionprefix="Numark" filename="Numark-iDJ-Live-II-scripts.js"/>
    </scriptfiles>
    <controls>
      <!-- Master -->
      <control>
	<group>[Master]</group>
	<key>Numark.toggleScratchMode</key>
	<status>0x90</status>
	<midino>0x48</midino>
	<options>
	  <script-binding/>
	</options>
      </control>
      <control>
	<group>[Master]</group>
	<key>gain</key>
	<status>0xB0</status>
	<midino>0x17</midino>
      </control>
      <control>
	<group>[Master]</group>
	<key>crossfader</key>
	<status>0xB0</status>
	<midino>0x0A</midino>
	<options>
	  <invert/>
	</options>
      </control>
      <!-- Library -->
      <control>
	<group>[Library]</group>
	<key>MoveVertical</key>
	<status>0xB0</status>
	<midino>0x1A</midino>
	<options>
	  <selectknob/>
	</options>
      </control>
      <control>
	<group>[Skin]</group>
	<key>show_maximized_library</key>
	<status>0x90</status>
	<midino>0x4F</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <!-- Deck 1 jog -->
      <control>
        <group>[Channel1]</group>
        <key>Numark.jogTouch</key>
        <status>0x90</status>
        <midino>0x4D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>Numark.jogTouch</key>
        <status>0x80</status>
        <midino>0x4D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>Numark.jog</key>
        <status>0xB0</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Deck 1 knobs -->
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x08</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB0</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB0</status>
        <midino>0x10</midino>
      </control>
      <!-- Deck 1 buttons -->
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x3B</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x3B</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>sync_enabled</key>
        <status>0x80</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x44</midino>
      </control>
      <!-- Deck 2 jog -->
      <control>
        <group>[Channel2]</group>
        <key>Numark.jogTouch</key>
        <status>0x90</status>
        <midino>0x4E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>Numark.jogTouch</key>
        <status>0x80</status>
        <midino>0x4E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>Numark.jog</key>
        <status>0xB0</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Deck 2 knobs -->
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x09</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB0</status>
        <midino>0x15</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB0</status>
        <midino>0x11</midino>
      </control>
      <!-- Deck 2 buttons -->
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x3C</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x3C</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>sync_enabled</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x46</midino>
      </control>
    </controls>
    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x4A</midino>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x4C</midino>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x40</midino>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x47</midino>
        <minimum>0.5</minimum>
      </output>
    </outputs>
  </controller>
</MixxxControllerPreset>
