<MixxxMIDIPreset mixxxVersion="1.12+" schemaVersion="1">
    <info>
      <name>Hercules DJ Control Instinct</name>
      <author><PERSON><PERSON></author>
      <description></description>
      <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=3907</forums>
      <manual>hercules_djcontrol_instinct</manual>
    </info>
    <controller id="Hercules DJControl Instinct MID" port="">
    <scriptfiles>
            <file functionprefix="HCInstinct" filename="Hercules-DJ-Control-Instinct-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5</midino>
                <group>[Sampler1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x6</midino>
                <group>[Sampler2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x7</midino>
                <group>[Sampler3]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x8</midino>
                <group>[Sampler4]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x9</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xa</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xc</midino>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x15</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x32</midino>
                <group>[Channel1]</group>
                <key>HCInstinct.wheelTurn0</key>
                <description>Left Jog Wheel pressed and turning</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>HCInstinct.wheelTurn1</key>
                <description>Right JogWheel pressed and turning</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x34</midino>
                <group>[Channel1]</group>
                <key>HCInstinct.tempPitch</key>
                <description>A Pitch Click up/down</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x35</midino>
                <group>[Channel2]</group>
                <key>HCInstinct.tempPitch</key>
                <description>B Pitch Click up/down</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1c</midino>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1e</midino>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x36</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x37</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x38</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x39</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x1f</midino>
                <group>[Sampler2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3b</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x23</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x25</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x26</midino>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x27</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Sampler1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x28</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Sampler2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x29</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Sampler3]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2a</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Sampler4]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x9</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2d</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xa</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2e</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xc</midino>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2f</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x31</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x32</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>HCInstinct.pitch</key>
                <description>A Pitch -</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>HCInstinct.pitch</key>
                <description>A Pitch +</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x35</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x36</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x37</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x38</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x39</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1a</midino>
                <group>[Channel1]</group>
                <key>HCInstinct.wheelTouch0</key>
                <description>Left JogWheel switch </description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1f</midino>
                <group>[Sampler2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x23</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x25</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x26</midino>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x27</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x28</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x29</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2a</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2b</midino>
                <group>[Channel2]</group>
                <key>HCInstinct.pitch</key>
                <description>B Pitch -</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2c</midino>
                <group>[Channel2]</group>
                <key>HCInstinct.pitch</key>
                <description>B Pitch -</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2d</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2e</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2f</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x30</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x31</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x32</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>HCInstinct.wheelTouch1</key>
                <description>Right JogWheel switch </description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x35</midino>
                <group>[Master]</group>
                <key>HCInstinct.vinylButtonHandler</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x36</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x37</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x38</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x39</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1c</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x5</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x10</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x6</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x19</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x7</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1a</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_start_position</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>1e+07</maximum>
                <status>0x90</status>
                <midino>0x1</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x1c</midino>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <status>0x90</status>
                <midino>0x1e</midino>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x34</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x23</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1b</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x48</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0xf</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x8</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x24</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
