<!--Numark MixTrack Mixxx Mapping v1.0b 26/06/2011 <PERSON> matte<PERSON>@magm3.com -->
<MixxxMIDIPreset mixxxVersion="1.9.0+" schemaVersion="1">
    <info>
      <name>Numark MIXTRACK</name>
      <author><PERSON> (<EMAIL>), RAWRR, and uncleeugene</author>
      <description>Numark MixTrack Mapping v1.0b</description>
      <manual>numark_mixtrack</manual>
    </info>
    <controller id="Numark Mix Track MIDI 1">
        <scriptfiles>
            <file functionprefix="NumarkMixTrack" filename="Numark-MixTrack-scripts.js"/>
        </scriptfiles>
        <controls>
        <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Master]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Playlist]</group>
                <key>NumarkMixTrack.selectKnob</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x69</midino>
                <group>[Playlist]</group>
                <key>NumarkMixTrack.toggleDirectoryMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <options>
                    <normal/>
                </options>
            </control>
           <control>
                <status>0xb0</status>
                <midino>0x1c</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1b</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1d</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <diff/>
                </options>
            </control>


<!-- CHANNEL 1 -->
            <control>
                <status>0x90</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>cue_goto</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x59</midino>
                <group>[Channel1]</group>
                <key>cue_set</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x51</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.ToggleKeylock</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x65</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5C</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5B</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5A</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x61</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x54</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.loopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x55</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x44</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x48</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4e</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.WheelTouch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrack.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x63</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
            </control>


<!-- CHANNEL 2 -->
            <control>
                <status>0x90</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4c</midino>
                <group>[Channel2]</group>
                <key>cue_goto</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5d</midino>
                <group>[Channel2]</group>
                <key>cue_set</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.ToggleKeylock</key>
                <options>
                    <script-binding/>
                </options>
        </control>
            <control>
                <status>0x90</status>
                <midino>0x66</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x60</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5f</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5e</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x62</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.loopOut</key>
                <options>
                     <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x58</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x46</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x50</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
        <control>
                <status>0x90</status>
                <midino>0x4d</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.WheelTouch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrack.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x64</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>

       <outputs>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3b</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_goto</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x4a</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5a</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x65</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5b</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x63</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>rate</key>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x70</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
<!-- CHANNEL 2 -->
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x42</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_goto</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x4c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x66</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5e</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5f</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x60</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x64</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
        <output>
                <group>[Channel2]</group>
                <key>rate</key>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x71</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
