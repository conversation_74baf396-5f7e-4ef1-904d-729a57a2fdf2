<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="" schemaVersion="1">
    <info>
        <name>Hercules DJControl Compact</name>
        <author><PERSON></author>
        <description>Controller mapping for Hercules DJControl Compact.  Users
who want the labeled "mid" knob to adjust high eq instead may use the MIDI
wizard to remap it, or edit the controller XML file directly to change this
setting.</description>
        <manual>hercules_djcontrol_compact</manual>
    </info>
    <controller id="DJControl">
        <scriptfiles>
            <file functionprefix="HercDJCompact" filename="Hercules-DJControl-Compact-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <!-- Set the key on the next line to "parameter3" to make the
                mid knob for Deck A adjust the high eq instead. -->
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <!-- Set the key on the next line to "parameter3" to make the
                mid knob for Deck B adjust the high eq instead. -->
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x3F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatlooproll_0.5_activate</key>
                <description>MIDI Learned from 14 messages.</description>
                <status>0x90</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <description>MIDI Learned from 14 messages.</description>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HercDJCompact.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 18 messages.</description>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <status>0x90</status>
                <midino>0x49</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HercDJCompact.scratch</key>
                <status>0x90</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[AutoDJ]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x2E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 12 messages.</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <description>MIDI Learned from 950 messages.</description>
                <status>0xB0</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatlooproll_0.25_activate</key>
                <description>MIDI Learned from 12 messages.</description>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description>MIDI Learned from 12 messages.</description>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <description>MIDI Learned from 16 messages.</description>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>start_stop</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>start_stop</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 26 messages.</description>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatlooproll_0.125_activate</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x90</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <description>MIDI Learned from 16 messages.</description>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>MIDI Learned from 264 messages.</description>
                <status>0xB0</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatlooproll_1_activate</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Recording]</group>
                <key>toggle_recording</key>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 24 messages.</description>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_8_activate</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 14 messages.</description>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <description>MIDI Learned from 14 messages.</description>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HercDJCompact.pitch</key>
                <status>0xB0</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatlooproll_0.5_activate</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x1F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <description>MIDI Learned from 22 messages.</description>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>MIDI Learned from 474 messages.</description>
                <status>0xB0</status>
                <midino>0x3D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <description>MIDI Learned from 12 messages.</description>
                <status>0x90</status>
                <midino>0x3A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HercDJCompact.pitch</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatlooproll_0.25_activate</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatlooproll_1_activate</key>
                <description>MIDI Learned from 16 messages.</description>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <description>MIDI Learned from 16 messages.</description>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HercDJCompact.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 20 messages.</description>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <description>MIDI Learned from 376 messages.</description>
                <status>0xB0</status>
                <midino>0x3C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_8_activate</key>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatlooproll_0.125_activate</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x1D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <description>MIDI Learned from 1239 messages.</description>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <normal/>
                </options>
            </control>
         <control>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>cue_gotoandplay</key>
                <description>MIDI Learned from 16 messages.</description>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>cue_gotoandstop</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Sampler4]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler8]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler4]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler8]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x3A</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x3E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x08</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x3B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x11</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler5]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler5]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x22</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x25</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x23</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x26</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x49</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler6]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler6]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[AutoDJ]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x2D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[AutoDJ]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x2E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x1D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x21</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x20</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x09</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x1F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x0A</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x0E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler3]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler7]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler3]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Sampler7]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <status>0x90</status>
                <midino>0x4F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x39</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <status>0x90</status>
                <midino>0x3D</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
