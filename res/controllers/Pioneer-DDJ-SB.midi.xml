<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
    <info>
        <name>Pioneer DDJ-SB</name>
        <author>Joan <PERSON>rdi<PERSON> Jo<PERSON>é</author>
        <description>Pioneer DDJ-SB configuration for 4 decks. Use the tempo range buttons to switch decks.</description>
        <manual>pioneer_ddj_sb</manual>
    </info>
    <controller id="PIONEER">
        <scriptfiles>
            <file functionprefix="PioneerDDJSB" filename="Pioneer-DDJ-SB-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Sampler4]</group>
                <key>eject</key>
                <status>0x97</status>
                <midino>0x7B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>eject</key>
                <status>0x98</status>
                <midino>0x7B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>eject</key>
                <status>0x97</status>
                <midino>0x7A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>eject</key>
                <status>0x98</status>
                <midino>0x7A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>eject</key>
                <status>0x97</status>
                <midino>0x79</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>eject</key>
                <status>0x98</status>
                <midino>0x79</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>eject</key>
                <status>0x97</status>
                <midino>0x78</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>eject</key>
                <status>0x98</status>
                <midino>0x78</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>LoadSelectedTrack</key>
                <status>0x97</status>
                <midino>0x73</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>LoadSelectedTrack</key>
                <status>0x98</status>
                <midino>0x73</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>LoadSelectedTrack</key>
                <status>0x97</status>
                <midino>0x72</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>LoadSelectedTrack</key>
                <status>0x98</status>
                <midino>0x72</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x97</status>
                <midino>0x71</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x97</status>
                <midino>0x70</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x98</status>
                <midino>0x71</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x98</status>
                <midino>0x70</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.headphoneCueButton</key>
                <status>0x90</status>
                <midino>0x68</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.headphoneCueButton</key>
                <status>0x91</status>
                <midino>0x68</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x90</status>
                <midino>0x67</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x91</status>
                <midino>0x67</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>next_chain</key>
                <status>0x97</status>
                <midino>0x69</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>next_chain</key>
                <status>0x98</status>
                <midino>0x69</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>prev_chain</key>
                <status>0x97</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x94</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>prev_chain</key>
                <status>0x98</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[PreviewDeck1]</group>
                <key>play</key>
                <status>0x96</status>
                <midino>0x66</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x95</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x94</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.deckToggleButton</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <status>0x96</status>
                <midino>0x65</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x95</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x94</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.deckToggleButton</key>
                <status>0x91</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButtonShifted</key>
                <status>0x95</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.muteButton</key>
                <status>0x97</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.quantizeButton</key>
                <status>0x90</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.muteButton</key>
                <status>0x98</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.highKillButton</key>
                <status>0x97</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.quantizeButton</key>
                <status>0x91</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.highKillButton</key>
                <status>0x98</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.midKillButton</key>
                <status>0x97</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.midKillButton</key>
                <status>0x98</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.lowKillButton</key>
                <status>0x97</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.syncButton</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.lowKillButton</key>
                <status>0x98</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.syncButton</key>
                <status>0x91</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.headphoneCueButton</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Samplers]</group>
                <key>show_samplers</key>
                <status>0x96</status>
                <midino>0x59</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.headphoneCueButton</key>
                <status>0x91</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1]</group>
                <key>show</key>
                <status>0x96</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.slipButton</key>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.slipButton</key>
                <status>0x91</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x97</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopRollButtons</key>
                <status>0x98</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.brakeButton</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.brakeButton</key>
                <status>0x91</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.reverseRollButton</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.reverseRollButton</key>
                <status>0x91</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x94</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x95</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x94</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x95</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x94</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxButton</key>
                <status>0x95</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loadButton</key>
                <status>0x96</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loadButton</key>
                <status>0x96</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.shiftButton</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>PioneerDDJSB.shiftedRotarySelector</key>
                <status>0xB6</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>PioneerDDJSB.rotarySelectorShiftedClick</key>
                <status>0x96</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>PioneerDDJSB.rotarySelectorClick</key>
                <status>0x96</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[PreviewDeck1]</group>
                <key>LoadSelectedTrackAndPlay</key>
                <status>0x96</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x91</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>stop</key>
                <status>0x97</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogTouch</key>
                <status>0x91</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>stop</key>
                <status>0x98</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>stop</key>
                <status>0x97</status>
                <midino>0x3A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>stop</key>
                <status>0x98</status>
                <midino>0x3A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>stop</key>
                <status>0x97</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>stop</key>
                <status>0x98</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>stop</key>
                <status>0x97</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>stop</key>
                <status>0x98</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>start_play</key>
                <status>0x97</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>start_play</key>
                <status>0x98</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>start_play</key>
                <status>0x97</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>start_play</key>
                <status>0x98</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>start_play</key>
                <status>0x97</status>
                <midino>0x31</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>start_play</key>
                <status>0x98</status>
                <midino>0x31</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>start_play</key>
                <status>0x97</status>
                <midino>0x30</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>start_play</key>
                <status>0x98</status>
                <midino>0x30</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopDoubleButton</key>
                <status>0x97</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopDoubleButton</key>
                <status>0x98</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopMoveForwardButton</key>
                <status>0x97</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopMoveForwardButton</key>
                <status>0x98</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopMoveBackButton</key>
                <status>0x97</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopMoveBackButton</key>
                <status>0x98</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopHalveButton</key>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopHalveButton</key>
                <status>0x98</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopExitButton</key>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>PioneerDDJSB.rotarySelector</key>
                <status>0xB6</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopExitButton</key>
                <status>0x98</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopOutButton</key>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.keyLockButton</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB6</status>
                <midino>0x3F</midino>
                <options>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopOutButton</key>
                <status>0x98</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.loopInButton</key>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.keyLockButton</key>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.loopInButton</key>
                <status>0x98</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.vinylButton</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.vinylButton</key>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.deckFaderLSB</key>
                <status>0xB0</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.deckFaderLSB</key>
                <status>0xB1</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterKnobLSB</key>
                <status>0xB6</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterKnobLSB</key>
                <status>0xB6</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterLowKnobLSB</key>
                <status>0xB0</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterLowKnobLSB</key>
                <status>0xB1</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.cueButton</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.cueButton</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.playButton</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterMidKnobLSB</key>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.playButton</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterMidKnobLSB</key>
                <status>0xB1</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x97</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.beatloopButtons</key>
                <status>0x98</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterHighKnobLSB</key>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogRingTickShift</key>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterHighKnobLSB</key>
                <status>0xB1</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogRingTickShift</key>
                <status>0xB1</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogPlatterTick</key>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogPlatterTick</key>
                <status>0xB1</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogPlatterTick</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxKnobLSB</key>
                <status>0xB4</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x97</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogPlatterTick</key>
                <status>0xB1</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogRingTick</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxKnobLSB</key>
                <status>0xB5</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.clearHotCueButtons</key>
                <status>0x98</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogRingTick</key>
                <status>0xB1</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.tempoSliderLSB</key>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.jogPlatterTickShift</key>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.tempoSliderLSB</key>
                <status>0xB1</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB6</status>
                <midino>0x25</midino>
                <options>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.jogPlatterTickShift</key>
                <status>0xB1</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.fxKnobShiftedLSB</key>
                <status>0xB4</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.fxKnobShiftedLSB</key>
                <status>0xB5</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x97</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB6</status>
                <midino>0x1F</midino>
                <options>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.hotCueButtons</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.deckFaderMSB</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterKnobMSB</key>
                <status>0xB6</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.deckFaderMSB</key>
                <status>0xB1</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterKnobMSB</key>
                <status>0xB6</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterLowKnobMSB</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterLowKnobMSB</key>
                <status>0xB1</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterMidKnobMSB</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterMidKnobMSB</key>
                <status>0xB1</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.filterHighKnobMSB</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.filterHighKnobMSB</key>
                <status>0xB1</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>PioneerDDJSB.fxKnobMSB</key>
                <status>0xB4</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>PioneerDDJSB.fxKnobMSB</key>
                <status>0xB5</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.tempoSliderMSB</key>
                <status>0xB0</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB6</status>
                <midino>0x05</midino>
                <options>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.tempoSliderMSB</key>
                <status>0xB1</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>PioneerDDJSB.fxKnobShiftedMSB</key>
                <status>0xB4</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>PioneerDDJSB.fxKnobShiftedMSB</key>
                <status>0xB5</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
