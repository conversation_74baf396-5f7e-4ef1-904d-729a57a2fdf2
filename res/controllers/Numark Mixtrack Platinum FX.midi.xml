<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.4.0" schemaVersion="1">
    <info>
        <name>Numark Mixtrack Platinum FX</name>
        <author>Evoixmr mixxx 2.4 version updated from - QGazQ tweaks of Octopussy based on mapping from h67ma, bad1dea5, photoenix, <PERSON>, <PERSON>, <PERSON><PERSON></author>
        <description>Mapping for the Numark Mixtrack Platinum FX.</description>
        <forums>https://mixxx.discourse.group/t/numark-mixtrack-platinum-fx-mapping/19985</forums>
    </info>
    <controller id="MixTrack">
        <scriptfiles>
            <file filename="lodash.mixxx.js" functionprefix=""/>
            <file filename="midi-components-0.0.js" functionprefix=""/>
            <file filename="Numark-Mixtrack-Platinum-FX-scripts.js" functionprefix="MixtrackPlatinumFX"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].playButton.input</key>
                <status>0x80</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].playButton.input</key>
                <status>0x81</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].playButton.input</key>
                <status>0x82</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].playButton.input</key>
                <status>0x83</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect1</key>
                <status>0x88</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].playButton.input</key>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].playButton.input</key>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].playButton.input</key>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].playButton.input</key>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect1</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MoveVertical</key>
                <description>MIDI Learned from 1 messages.</description>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>ScrollVertical</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0xBF</status>
                <midino>0x01</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].cueButton.input</key>
                <status>0x80</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].cueButton.input</key>
                <status>0x81</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].cueButton.input</key>
                <status>0x82</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].cueButton.input</key>
                <status>0x83</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect2</key>
                <status>0x88</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].cueButton.input</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].cueButton.input</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].cueButton.input</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[3].cueButton.input</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect2</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].syncButton.input</key>
                <status>0x80</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].syncButton.input</key>
                <status>0x81</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].syncButton.input</key>
                <status>0x82</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].syncButton.input</key>
                <status>0x83</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect3</key>
                <status>0x88</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loadButton.input</key>
                <status>0x8F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].syncButton.input</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].syncButton.input</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].syncButton.input</key>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].syncButton.input</key>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effect3</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loadButton.input</key>
                <status>0x9F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].syncButton.input</key>
                <status>0x80</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].syncButton.input</key>
                <status>0x81</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].syncButton.input</key>
                <status>0x82</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].syncButton.input</key>
                <status>0x83</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect1</key>
                <status>0x89</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loadButton.input</key>
                <status>0x8F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].syncButton.input</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].syncButton.input</key>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].syncButton.input</key>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].syncButton.input</key>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect1</key>
                <status>0x99</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loadButton.input</key>
                <status>0x9F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].enableSwitch</key>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].enableSwitch</key>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].playButtonbeatgrid</key>
                <status>0x80</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].playButtonbeatgrid</key>
                <status>0x81</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].playButtonbeatgrid</key>
                <status>0x82</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].playButtonbeatgrid</key>
                <status>0x83</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect2</key>
                <status>0x89</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loadButton.input</key>
                <status>0x8F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].playButtonbeatgrid</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].playButtonbeatgrid</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].playButtonbeatgrid</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].playButtonbeatgrid</key>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect2</key>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loadButton.input</key>
                <status>0x9F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].dryWetKnob.input</key>
                <status>0xB8</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].dryWetKnob.input</key>
                <status>0xB8</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].cueButton.input</key>
                <status>0x80</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].cueButton.input</key>
                <status>0x81</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].cueButton.input</key>
                <status>0x82</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].cueButton.input</key>
                <status>0x83</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect3</key>
                <status>0x89</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loadButton.input</key>
                <status>0x8F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].cueButton.input</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].cueButton.input</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].cueButton.input</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].cueButton.input</key>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effect3</key>
                <status>0x99</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loadButton.input</key>
                <status>0x9F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effectParam.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effectParam.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effectParam2.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effectParam2.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinumFX.effect[0].effectParam3.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinumFX.effect[1].effectParam3.input</key>
                <status>0xB8</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinumFX.browse.knobButton.input</key>
                <status>0x8F</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.wheelTouch</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.wheelTouch</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.wheelTouch</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.wheelTouch</key>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinumFX.browse.knobButton.input</key>
                <status>0x9F</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.wheelTurn</key>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.wheelTurn</key>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.wheelTurn</key>
                <status>0xB3</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].scratchToggle.input</key>
                <status>0x80</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].scratchToggle.input</key>
                <status>0x81</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].scratchToggle.input</key>
                <status>0x82</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].scratchToggle.input</key>
                <status>0x83</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinumFX.browse.knobButton.input</key>
                <status>0x8F</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].scratchToggle.input</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].scratchToggle.input</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].scratchToggle.input</key>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].scratchToggle.input</key>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinumFX.browse.knobButton.input</key>
                <status>0x9F</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x80</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x81</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x82</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x83</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deckSwitch</key>
                <status>0x93</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB1</status>
                <midino>0x08</midino>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].tap.input</key>
                <status>0x88</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].tap.input</key>
                <status>0x89</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].tap.input</key>
                <status>0x98</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].tap.input</key>
                <status>0x99</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitch.inputMSB</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitch.inputMSB</key>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitch.inputMSB</key>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitch.inputMSB</key>
                <status>0xB3</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendUp.input</key>
                <status>0x80</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendUp.input</key>
                <status>0x81</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendUp.input</key>
                <status>0x82</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendUp.input</key>
                <status>0x83</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendUp.input</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendUp.input</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendUp.input</key>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendUp.input</key>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendDown.input</key>
                <status>0x80</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendDown.input</key>
                <status>0x81</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendDown.input</key>
                <status>0x82</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendDown.input</key>
                <status>0x83</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendDown.input</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendDown.input</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendDown.input</key>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendDown.input</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.gains.cueGain.input</key>
                <status>0xBF</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.gains.cueMix.input</key>
                <status>0xBF</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x84</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x85</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x86</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x87</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.modeButtonPress</key>
                <status>0x94</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.modeButtonPress</key>
                <status>0x95</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.modeButtonPress</key>
                <status>0x96</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.modeButtonPress</key>
                <status>0x97</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].gain.input</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].gain.input</key>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].gain.input</key>
                <status>0xB2</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].gain.input</key>
                <status>0xB3</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[0].treble.input</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[1].treble.input</key>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[2].treble.input</key>
                <status>0xB2</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[3].treble.input</key>
                <status>0xB3</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[0].mid.input</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[1].mid.input</key>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[2].mid.input</key>
                <status>0xB2</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[3].mid.input</key>
                <status>0xB3</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[0].bass.input</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[1].bass.input</key>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[2].bass.input</key>
                <status>0xB2</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinumFX.deck[3].bass.input</key>
                <status>0xB3</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>MixtrackPlatinumFX.deck[0].filter.input</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>MixtrackPlatinumFX.deck[1].filter.input</key>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>MixtrackPlatinumFX.deck[2].filter.input</key>
                <status>0xB2</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>MixtrackPlatinumFX.deck[3].filter.input</key>
                <status>0xB3</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pflButton.input</key>
                <status>0x80</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pflButton.input</key>
                <status>0x81</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pflButton.input</key>
                <status>0x82</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pflButton.input</key>
                <status>0x83</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pflButton.input</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pflButton.input</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pflButton.input</key>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pflButton.input</key>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].volume.input</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].volume.input</key>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].volume.input</key>
                <status>0xB2</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].volume.input</key>
                <status>0xB3</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x80</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x81</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x82</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x83</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x92</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x93</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x84</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x85</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x86</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x87</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].padSection.padPress</key>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].padSection.padPress</key>
                <status>0x95</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].padSection.padPress</key>
                <status>0x96</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].padSection.padPress</key>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinumFX.gains.mainGain.input</key>
                <status>0xBE</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitch.inputLSB</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitch.inputLSB</key>
                <status>0xB1</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitch.inputLSB</key>
                <status>0xB2</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitch.inputLSB</key>
                <status>0xB3</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendUp.input</key>
                <status>0x80</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendUp.input</key>
                <status>0x81</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendUp.input</key>
                <status>0x82</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendUp.input</key>
                <status>0x83</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendUp.input</key>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendUp.input</key>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendUp.input</key>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendUp.input</key>
                <status>0x93</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendDown.input</key>
                <status>0x80</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendDown.input</key>
                <status>0x81</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendDown.input</key>
                <status>0x82</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendDown.input</key>
                <status>0x83</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].pitchBendDown.input</key>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].pitchBendDown.input</key>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].pitchBendDown.input</key>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].pitchBendDown.input</key>
                <status>0x93</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopHalf.input</key>
                <status>0x84</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopHalf.input</key>
                <status>0x85</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopHalf.input</key>
                <status>0x86</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopHalf.input</key>
                <status>0x87</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopHalf.input</key>
                <status>0x94</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopHalf.input</key>
                <status>0x95</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopHalf.input</key>
                <status>0x96</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopHalf.input</key>
                <status>0x97</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopDouble.input</key>
                <status>0x84</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopDouble.input</key>
                <status>0x85</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopDouble.input</key>
                <status>0x86</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopDouble.input</key>
                <status>0x87</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopDouble.input</key>
                <status>0x94</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopDouble.input</key>
                <status>0x95</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopDouble.input</key>
                <status>0x96</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopDouble.input</key>
                <status>0x97</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopHalf.input</key>
                <status>0x84</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopHalf.input</key>
                <status>0x85</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopHalf.input</key>
                <status>0x86</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopHalf.input</key>
                <status>0x87</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopHalf.input</key>
                <status>0x94</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopHalf.input</key>
                <status>0x95</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopHalf.input</key>
                <status>0x96</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopHalf.input</key>
                <status>0x97</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopDouble.input</key>
                <status>0x84</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopDouble.input</key>
                <status>0x85</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopDouble.input</key>
                <status>0x86</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopDouble.input</key>
                <status>0x87</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loopDouble.input</key>
                <status>0x94</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loopDouble.input</key>
                <status>0x95</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loopDouble.input</key>
                <status>0x96</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loopDouble.input</key>
                <status>0x97</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loop.input</key>
                <status>0x84</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loop.input</key>
                <status>0x85</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loop.input</key>
                <status>0x86</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loop.input</key>
                <status>0x87</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loop.input</key>
                <status>0x94</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loop.input</key>
                <status>0x95</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loop.input</key>
                <status>0x96</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loop.input</key>
                <status>0x97</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loop.input</key>
                <status>0x84</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loop.input</key>
                <status>0x85</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loop.input</key>
                <status>0x86</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loop.input</key>
                <status>0x87</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinumFX.deck[0].loop.input</key>
                <status>0x94</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinumFX.deck[1].loop.input</key>
                <status>0x95</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinumFX.deck[2].loop.input</key>
                <status>0x96</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinumFX.deck[3].loop.input</key>
                <status>0x97</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x80</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x81</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x92</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x82</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x93</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x83</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
