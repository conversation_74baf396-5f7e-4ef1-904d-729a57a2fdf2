<?xml version="1.0" encoding="UTF-8"?>
<MixxxControllerPreset mixxxVersion="2.2.3" schemaVersion="1">
  <info>
    <name>Stanton DJC.4</name>
    <author><PERSON>, <PERSON></author>
    <description>The Stanton DJC.4 is a 4 deck controller with large, touch-sensitive jog wheels and a built-in audio interface (2 inputs, 2 outputs). It features 4 FX units and a master VU meter.</description>
    <manual>stanton_djc_4</manual>
  </info>
  <controller id="DJC-4">
    <scriptfiles>
      <file functionprefix="" filename="lodash.mixxx.js"/>
      <file functionprefix="" filename="midi-components-0.0.js"/>
      <file functionprefix="DJC4" filename="Stanton-DJC-4-scripts.js" />
    </scriptfiles>
    <controls>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].beatLoopEncoder.input</key>
        <status>0xB0</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatloop_activate</key>
        <status>0x90</status>
        <midino>0x01</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].beatLoopEncoder.input</key>
        <status>0xB1</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatloop_activate</key>
        <status>0x91</status>
        <midino>0x01</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].beatLoopEncoder.input</key>
        <status>0xB2</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatloop_activate</key>
        <status>0x92</status>
        <midino>0x01</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].beatLoopEncoder.input</key>
        <status>0xB3</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatloop_activate</key>
        <status>0x93</status>
        <midino>0x01</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].wheelTurn</key>
        <status>0xB0</status>
        <midino>0x02</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_halve</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].wheelTurn</key>
        <status>0xB1</status>
        <midino>0x02</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_halve</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].wheelTurn</key>
        <status>0xB2</status>
        <midino>0x02</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_halve</key>
        <status>0x92</status>
        <midino>0x02</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].wheelTurn</key>
        <status>0xB3</status>
        <midino>0x02</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_halve</key>
        <status>0x93</status>
        <midino>0x02</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_double</key>
        <status>0x90</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_double</key>
        <status>0x91</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB1</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_double</key>
        <status>0x92</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pregain</key>
        <status>0xB2</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_double</key>
        <status>0x93</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pregain</key>
        <status>0xB3</status>
        <midino>0x03</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_in</key>
        <status>0x90</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_in</key>
        <status>0x91</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_in</key>
        <status>0x92</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_in</key>
        <status>0x93</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB0</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB1</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB2</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB3</status>
        <midino>0x04</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_out</key>
        <status>0x90</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_out</key>
        <status>0x91</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_out</key>
        <status>0x92</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_out</key>
        <status>0x93</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB0</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB1</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB2</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB3</status>
        <midino>0x05</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reloop_toggle</key>
        <status>0x90</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reloop_toggle</key>
        <status>0x91</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>reloop_toggle</key>
        <status>0x92</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>reloop_toggle</key>
        <status>0x93</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB0</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB1</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB2</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB3</status>
        <midino>0x06</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x07</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB1</status>
        <midino>0x07</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>volume</key>
        <status>0xB2</status>
        <midino>0x07</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>volume</key>
        <status>0xB3</status>
        <midino>0x07</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[0].dryWetKnob.input</key>
        <status>0xB0</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>DJC4.effectUnit[1].dryWetKnob.input</key>
        <status>0xB1</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>DJC4.effectUnit[2].dryWetKnob.input</key>
        <status>0xB2</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>DJC4.effectUnit[3].dryWetKnob.input</key>
        <status>0xB3</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <status>0x90</status>
        <midino>0x08</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <status>0x91</status>
        <midino>0x08</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_activate</key>
        <status>0x92</status>
        <midino>0x08</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_activate</key>
        <status>0x93</status>
        <midino>0x08</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <status>0x90</status>
        <midino>0x09</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <status>0x91</status>
        <midino>0x09</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_activate</key>
        <status>0x92</status>
        <midino>0x09</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_activate</key>
        <status>0x93</status>
        <midino>0x09</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>DJC4.effectUnit[0].knobs[1].input</key>
        <status>0xB0</status>
        <midino>0x09</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>DJC4.effectUnit[1].knobs[1].input</key>
        <status>0xB1</status>
        <midino>0x09</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>DJC4.effectUnit[2].knobs[1].input</key>
        <status>0xB2</status>
        <midino>0x09</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>DJC4.effectUnit[3].knobs[1].input</key>
        <status>0xB3</status>
        <midino>0x09</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <status>0x90</status>
        <midino>0x0A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <status>0x91</status>
        <midino>0x0A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_activate</key>
        <status>0x92</status>
        <midino>0x0A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_activate</key>
        <status>0x93</status>
        <midino>0x0A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>DJC4.effectUnit[0].knobs[2].input</key>
        <status>0xB0</status>
        <midino>0x0A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>DJC4.effectUnit[1].knobs[2].input</key>
        <status>0xB1</status>
        <midino>0x0A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>DJC4.effectUnit[2].knobs[2].input</key>
        <status>0xB2</status>
        <midino>0x0A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>DJC4.effectUnit[3].knobs[2].input</key>
        <status>0xB3</status>
        <midino>0x0A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_activate</key>
        <status>0x90</status>
        <midino>0x0B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_activate</key>
        <status>0x91</status>
        <midino>0x0B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_activate</key>
        <status>0x92</status>
        <midino>0x0B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_activate</key>
        <status>0x93</status>
        <midino>0x0B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[0].knobs[3].input</key>
        <status>0xB0</status>
        <midino>0x0B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[1].knobs[3].input</key>
        <status>0xB1</status>
        <midino>0x0B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[2].knobs[3].input</key>
        <status>0xB2</status>
        <midino>0x0B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[3].knobs[3].input</key>
        <status>0xB3</status>
        <midino>0x0B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJC4.deck[0].samplerButtons[0].input</key>
        <status>0x90</status>
        <midino>0x0C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJC4.deck[1].samplerButtons[0].input</key>
        <status>0x91</status>
        <midino>0x0C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJC4.deck[2].samplerButtons[0].input</key>
        <status>0x92</status>
        <midino>0x0C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJC4.deck[3].samplerButtons[0].input</key>
        <status>0x93</status>
        <midino>0x0C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJC4.deck[0].samplerButtons[1].input</key>
        <status>0x90</status>
        <midino>0x0D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJC4.deck[1].samplerButtons[1].input</key>
        <status>0x91</status>
        <midino>0x0D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJC4.deck[2].samplerButtons[1].input</key>
        <status>0x92</status>
        <midino>0x0D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJC4.deck[3].samplerButtons[1].input</key>
        <status>0x93</status>
        <midino>0x0D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler]</group>
        <key>DJC4.samplerVolume</key>
        <status>0xB0</status>
        <midino>0x0D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>DJC4.browseEncoder.input</key>
        <status>0xB0</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJC4.deck[0].samplerButtons[2].input</key>
        <status>0x90</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJC4.deck[1].samplerButtons[2].input</key>
        <status>0x91</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJC4.deck[2].samplerButtons[2].input</key>
        <status>0x92</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJC4.deck[3].samplerButtons[2].input</key>
        <status>0x93</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJC4.deck[0].samplerButtons[3].input</key>
        <status>0x90</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJC4.deck[1].samplerButtons[3].input</key>
        <status>0x91</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJC4.deck[2].samplerButtons[3].input</key>
        <status>0x92</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJC4.deck[3].samplerButtons[3].input</key>
        <status>0x93</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0x91</status>
        <midino>0x10</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>keylock</key>
        <status>0x92</status>
        <midino>0x10</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>keylock</key>
        <status>0x93</status>
        <midino>0x10</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>DJC4.crossfaderCurve</key>
        <status>0xB0</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x12</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x91</status>
        <midino>0x12</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatsync</key>
        <status>0x92</status>
        <midino>0x12</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatsync</key>
        <status>0x93</status>
        <midino>0x12</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x13</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x91</status>
        <midino>0x13</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>rate_temp_down</key>
        <status>0x92</status>
        <midino>0x13</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>rate_temp_down</key>
        <status>0x93</status>
        <midino>0x13</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0xB0</status>
        <midino>0x13</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x14</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x91</status>
        <midino>0x14</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>rate_temp_up</key>
        <status>0x92</status>
        <midino>0x14</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>rate_temp_up</key>
        <status>0x93</status>
        <midino>0x14</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headGain</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].toggleScratchMode</key>
        <status>0x90</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].toggleScratchMode</key>
        <status>0x91</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].toggleScratchMode</key>
        <status>0x92</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].toggleScratchMode</key>
        <status>0x93</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>bpm_tap</key>
        <status>0x90</status>
        <midino>0x16</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>bpm_tap</key>
        <status>0x91</status>
        <midino>0x16</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>bpm_tap</key>
        <status>0x92</status>
        <midino>0x16</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>bpm_tap</key>
        <status>0x93</status>
        <midino>0x16</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0x90</status>
        <midino>0x17</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0x91</status>
        <midino>0x17</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_default</key>
        <status>0x92</status>
        <midino>0x17</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_default</key>
        <status>0x93</status>
        <midino>0x17</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x18</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x91</status>
        <midino>0x18</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>play</key>
        <status>0x92</status>
        <midino>0x18</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>play</key>
        <status>0x93</status>
        <midino>0x18</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x90</status>
        <midino>0x19</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x91</status>
        <midino>0x19</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x92</status>
        <midino>0x19</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x93</status>
        <midino>0x19</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x90</status>
        <midino>0x1A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x91</status>
        <midino>0x1A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x92</status>
        <midino>0x1A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x93</status>
        <midino>0x1A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x92</status>
        <midino>0x1B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x93</status>
        <midino>0x1B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x1C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x1C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pfl</key>
        <status>0x92</status>
        <midino>0x1C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pfl</key>
        <status>0x93</status>
        <midino>0x1C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[0].effectFocusButton.input</key>
        <status>0x90</status>
        <midino>0x1D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[1].effectFocusButton.input</key>
        <status>0x91</status>
        <midino>0x1D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[2].effectFocusButton.input</key>
        <status>0x92</status>
        <midino>0x1D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[3].effectFocusButton.input</key>
        <status>0x93</status>
        <midino>0x1D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x1E</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x1E</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x1E</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x1E</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>DJC4.effectUnit[0].enableButtons[1].input</key>
        <status>0x90</status>
        <midino>0x1F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>DJC4.effectUnit[1].enableButtons[1].input</key>
        <status>0x91</status>
        <midino>0x1F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect1]</group>
        <key>DJC4.effectUnit[2].enableButtons[1].input</key>
        <status>0x92</status>
        <midino>0x1F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect1]</group>
        <key>DJC4.effectUnit[3].enableButtons[1].input</key>
        <status>0x93</status>
        <midino>0x1F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>DJC4.effectUnit[0].enableButtons[2].input</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>DJC4.effectUnit[1].enableButtons[2].input</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect2]</group>
        <key>DJC4.effectUnit[2].enableButtons[2].input</key>
        <status>0x92</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect2]</group>
        <key>DJC4.effectUnit[3].enableButtons[2].input</key>
        <status>0x93</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].wheelTurn</key>
        <status>0xB0</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].wheelTurn</key>
        <status>0xB1</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].wheelTurn</key>
        <status>0xB2</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].wheelTurn</key>
        <status>0xB3</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[0].enableButtons[3].input</key>
        <status>0x90</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[1].enableButtons[3].input</key>
        <status>0x91</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[2].enableButtons[3].input</key>
        <status>0x92</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[3].enableButtons[3].input</key>
        <status>0x93</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x22</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>LoadSelectedTrack</key>
        <status>0x92</status>
        <midino>0x22</midino>
        <options>
          <snormal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x91</status>
        <midino>0x23</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>LoadSelectedTrack</key>
        <status>0x93</status>
        <midino>0x23</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel1]]</group>
        <key>super1</key>
        <status>0xB0</status>
        <midino>0x24</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]]</group>
        <key>super1</key>
        <status>0xB1</status>
        <midino>0x24</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel3]]</group>
        <key>super1</key>
        <status>0xB2</status>
        <midino>0x24</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel4]]</group>
        <key>super1</key>
        <status>0xB3</status>
        <midino>0x24</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[0].dryWetKnob.input</key>
        <status>0xB0</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>DJC4.effectUnit[1].dryWetKnob.input</key>
        <status>0xB1</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>DJC4.effectUnit[2].dryWetKnob.input</key>
        <status>0xB2</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>DJC4.effectUnit[3].dryWetKnob.input</key>
        <status>0xB3</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].wheelTouch</key>
        <status>0x90</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].wheelTouch</key>
        <status>0x91</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].wheelTouch</key>
        <status>0x92</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].wheelTouch</key>
        <status>0x93</status>
        <midino>0x26</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>MoveFocus</key>
        <status>0x90</status>
        <midino>0x27</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>DJC4.browseEncoder.input</key>
        <status>0xB0</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>DJC4.shiftButton</key>
        <status>0x90</status>
        <midino>0x2D</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatlooproll_activate</key>
        <status>0x90</status>
        <midino>0x33</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatlooproll_activate</key>
        <status>0x91</status>
        <midino>0x33</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatlooproll_activate</key>
        <status>0x92</status>
        <midino>0x33</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatlooproll_activate</key>
        <status>0x93</status>
        <midino>0x33</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_in_goto</key>
        <status>0x90</status>
        <midino>0x36</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_in_goto</key>
        <status>0x91</status>
        <midino>0x36</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_in_goto</key>
        <status>0x92</status>
        <midino>0x36</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_in_goto</key>
        <status>0x93</status>
        <midino>0x36</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_out_goto</key>
        <status>0x90</status>
        <midino>0x37</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_out_goto</key>
        <status>0x91</status>
        <midino>0x37</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_out_goto</key>
        <status>0x92</status>
        <midino>0x37</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_out_goto</key>
        <status>0x93</status>
        <midino>0x37</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reloop_andstop</key>
        <status>0x90</status>
        <midino>0x38</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reloop_andstop</key>
        <status>0x91</status>
        <midino>0x38</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>reloop_andstop</key>
        <status>0x92</status>
        <midino>0x38</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>reloop_andstop</key>
        <status>0x93</status>
        <midino>0x38</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_clear</key>
        <status>0x90</status>
        <midino>0x3A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_clear</key>
        <status>0x91</status>
        <midino>0x3A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_clear</key>
        <status>0x92</status>
        <midino>0x3A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_clear</key>
        <status>0x93</status>
        <midino>0x3A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_clear</key>
        <status>0x90</status>
        <midino>0x3B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_clear</key>
        <status>0x91</status>
        <midino>0x3B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_clear</key>
        <status>0x92</status>
        <midino>0x3B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_clear</key>
        <status>0x93</status>
        <midino>0x3B</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_clear</key>
        <status>0x90</status>
        <midino>0x3C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_clear</key>
        <status>0x91</status>
        <midino>0x3C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_clear</key>
        <status>0x92</status>
        <midino>0x3C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_clear</key>
        <status>0x93</status>
        <midino>0x3C</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_clear</key>
        <status>0x90</status>
        <midino>0x3D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_clear</key>
        <status>0x91</status>
        <midino>0x3D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_clear</key>
        <status>0x92</status>
        <midino>0x3D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_clear</key>
        <status>0x93</status>
        <midino>0x3D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJC4.deck[0].samplerButtons[0].input</key>
        <status>0x90</status>
        <midino>0x3E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJC4.deck[1].samplerButtons[0].input</key>
        <status>0x91</status>
        <midino>0x3E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJC4.deck[3].samplerButtons[0].input</key>
        <status>0x92</status>
        <midino>0x3E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJC4.deck[3].samplerButtons[0].input</key>
        <status>0x93</status>
        <midino>0x3E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJC4.deck[0].samplerButtons[1].input</key>
        <status>0x90</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJC4.deck[1].samplerButtons[1].input</key>
        <status>0x91</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJC4.deck[2].samplerButtons[1].input</key>
        <status>0x92</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJC4.deck[3].samplerButtons[1].input</key>
        <status>0x93</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJC4.deck[0].samplerButtons[2].input</key>
        <status>0x90</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJC4.deck[1].samplerButtons[2].input</key>
        <status>0x91</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJC4.deck[2].samplerButtons[2].input</key>
        <status>0x92</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJC4.deck[3].samplerButtons[2].input</key>
        <status>0x93</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJC4.deck[0].samplerButtons[3].input</key>
        <status>0x90</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJC4.deck[1].samplerButtons[3].input</key>
        <status>0x91</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJC4.deck[2].samplerButtons[3].input</key>
        <status>0x92</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJC4.deck[3].samplerButtons[3].input</key>
        <status>0x93</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>quantize</key>
        <status>0x90</status>
        <midino>0x42</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>quantize</key>
        <status>0x91</status>
        <midino>0x42</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>quantize</key>
        <status>0x92</status>
        <midino>0x42</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>quantize</key>
        <status>0x93</status>
        <midino>0x42</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x44</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x91</status>
        <midino>0x44</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>reverse</key>
        <status>0x92</status>
        <midino>0x44</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>reverse</key>
        <status>0x93</status>
        <midino>0x44</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_gotoandstop</key>
        <status>0x90</status>
        <midino>0x49</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_gotoandstop</key>
        <status>0x91</status>
        <midino>0x49</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_gotoandstop</key>
        <status>0x92</status>
        <midino>0x49</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_gotoandstop</key>
        <status>0x93</status>
        <midino>0x49</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x4A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x91</status>
        <midino>0x4A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_set</key>
        <status>0x92</status>
        <midino>0x4A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_set</key>
        <status>0x93</status>
        <midino>0x4A</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel1]_Effect1]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x4D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]_Effect1]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x4D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel3]_Effect1]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x4D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel4]_Effect1]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x4D</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[0].effectFocusButton.input</key>
        <status>0x90</status>
        <midino>0x4F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[1].effectFocusButton.input</key>
        <status>0x91</status>
        <midino>0x4F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[2].effectFocusButton.input</key>
        <status>0x92</status>
        <midino>0x4F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>DJC4.effectUnit[3].effectFocusButton.input</key>
        <status>0x93</status>
        <midino>0x4F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>DJC4.effectUnit[0].enableButtons[1].input</key>
        <status>0x90</status>
        <midino>0x51</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>DJC4.effectUnit[1].enableButtons[1].input</key>
        <status>0x91</status>
        <midino>0x51</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>DJC4.effectUnit[2].enableButtons[1].input</key>
        <status>0x92</status>
        <midino>0x51</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>DJC4.effectUnit[3].enableButtons[1].input</key>
        <status>0x93</status>
        <midino>0x51</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>DJC4.effectUnit[0].enableButtons[2].input</key>
        <status>0x90</status>
        <midino>0x52</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>DJC4.effectUnit[1].enableButtons[2].input</key>
        <status>0x91</status>
        <midino>0x52</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>DJC4.effectUnit[2].enableButtons[2].input</key>
        <status>0x92</status>
        <midino>0x52</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>DJC4.effectUnit[3].enableButtons[2].input</key>
        <status>0x93</status>
        <midino>0x52</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[0].enableButtons[3].input</key>
        <status>0x90</status>
        <midino>0x53</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[1].enableButtons[3].input</key>
        <status>0x91</status>
        <midino>0x53</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>DJC4.effectUnit[2].enableButtons[3].input</key>
        <status>0x92</status>
        <midino>0x53</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>DJC4.effectUnit[3].enableButtons[3].input</key>
        <status>0x93</status>
        <midino>0x53</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>MoveLeft</key>
        <status>0x90</status>
        <midino>0x54</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>MoveRight</key>
        <status>0x91</status>
        <midino>0x55</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>MoveLeft</key>
        <status>0x92</status>
        <midino>0x54</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>MoveRight</key>
        <status>0x93</status>
        <midino>0x55</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJC4.deck[0].wheelTouch</key>
        <status>0x90</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJC4.deck[1].wheelTouch</key>
        <status>0x91</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>DJC4.deck[2].wheelTouch</key>
        <status>0x92</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>DJC4.deck[3].wheelTouch</key>
        <status>0x93</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Skin]</group>
        <key>show_maximized_library</key>
        <status>0x90</status>
        <midino>0x59</midino>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xE0</status>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xE1</status>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>rate</key>
        <status>0xE2</status>
        <options>
          <normal />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>rate</key>
        <status>0xE3</status>
        <options>
          <normal />
        </options>
      </control>
    </controls>
    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>loop_halve</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_halve</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_halve</key>
        <status>0x92</status>
        <midino>0x02</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_halve</key>
        <status>0x93</status>
        <midino>0x02</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_double</key>
        <status>0x90</status>
        <midino>0x03</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_double</key>
        <status>0x91</status>
        <midino>0x03</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_double</key>
        <status>0x92</status>
        <midino>0x03</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_double</key>
        <status>0x93</status>
        <midino>0x03</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_start_position</key>
        <status>0x90</status>
        <midino>0x04</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_start_position</key>
        <status>0x91</status>
        <midino>0x04</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_start_position</key>
        <status>0x92</status>
        <midino>0x04</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_start_position</key>
        <status>0x93</status>
        <midino>0x04</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_end_position</key>
        <status>0x90</status>
        <midino>0x05</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_end_position</key>
        <status>0x91</status>
        <midino>0x05</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_end_position</key>
        <status>0x92</status>
        <midino>0x05</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_end_position</key>
        <status>0x93</status>
        <midino>0x05</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0</minimum>
        <maximum>2147483647</maximum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_enabled</key>
        <status>0x90</status>
        <midino>0x06</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_enabled</key>
        <status>0x91</status>
        <midino>0x06</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_enabled</key>
        <status>0x92</status>
        <midino>0x06</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_enabled</key>
        <status>0x93</status>
        <midino>0x06</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_1_enabled</key>
        <status>0x90</status>
        <midino>0x08</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_1_enabled</key>
        <status>0x91</status>
        <midino>0x08</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_1_enabled</key>
        <status>0x92</status>
        <midino>0x08</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_1_enabled</key>
        <status>0x93</status>
        <midino>0x08</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_2_enabled</key>
        <status>0x90</status>
        <midino>0x09</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_2_enabled</key>
        <status>0x91</status>
        <midino>0x09</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_2_enabled</key>
        <status>0x92</status>
        <midino>0x09</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_2_enabled</key>
        <status>0x93</status>
        <midino>0x09</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_3_enabled</key>
        <status>0x90</status>
        <midino>0x0A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_3_enabled</key>
        <status>0x91</status>
        <midino>0x0A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_3_enabled</key>
        <status>0x92</status>
        <midino>0x0A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_3_enabled</key>
        <status>0x93</status>
        <midino>0x0A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_4_enabled</key>
        <status>0x90</status>
        <midino>0x0B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_4_enabled</key>
        <status>0x91</status>
        <midino>0x0B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_4_enabled</key>
        <status>0x92</status>
        <midino>0x0B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_4_enabled</key>
        <status>0x93</status>
        <midino>0x0B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0x91</status>
        <midino>0x10</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>keylock</key>
        <status>0x92</status>
        <midino>0x10</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>keylock</key>
        <status>0x93</status>
        <midino>0x10</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x12</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x91</status>
        <midino>0x12</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>beatsync</key>
        <status>0x92</status>
        <midino>0x12</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>beatsync</key>
        <status>0x93</status>
        <midino>0x12</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x13</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x91</status>
        <midino>0x13</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>rate_temp_down</key>
        <status>0x92</status>
        <midino>0x13</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>rate_temp_down</key>
        <status>0x93</status>
        <midino>0x13</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x14</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x91</status>
        <midino>0x14</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>rate_temp_up</key>
        <status>0x92</status>
        <midino>0x14</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>rate_temp_up</key>
        <status>0x93</status>
        <midino>0x14</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x16</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play_indicator</key>
        <status>0x91</status>
        <midino>0x16</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>play_indicator</key>
        <status>0x92</status>
        <midino>0x16</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>play_indicator</key>
        <status>0x93</status>
        <midino>0x16</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>cue_indicator</key>
        <status>0x90</status>
        <midino>0x17</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>cue_indicator</key>
        <status>0x91</status>
        <midino>0x17</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>cue_indicator</key>
        <status>0x92</status>
        <midino>0x17</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>cue_indicator</key>
        <status>0x93</status>
        <midino>0x17</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x18</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play_indicator</key>
        <status>0x91</status>
        <midino>0x18</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>play_indicator</key>
        <status>0x92</status>
        <midino>0x18</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>play_indicator</key>
        <status>0x93</status>
        <midino>0x18</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x90</status>
        <midino>0x19</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x91</status>
        <midino>0x19</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x92</status>
        <midino>0x19</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter3</key>
        <status>0x93</status>
        <midino>0x19</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x90</status>
        <midino>0x1A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x91</status>
        <midino>0x1A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x92</status>
        <midino>0x1A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter2</key>
        <status>0x93</status>
        <midino>0x1A</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x92</status>
        <midino>0x1B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>button_parameter1</key>
        <status>0x93</status>
        <midino>0x1B</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x1C</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x1C</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>pfl</key>
        <status>0x92</status>
        <midino>0x1C</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>pfl</key>
        <status>0x93</status>
        <midino>0x1C</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x1E</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x1E</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x1E</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x1E</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x22</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>play_indicator</key>
        <status>0x92</status>
        <midino>0x22</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play_indicator</key>
        <status>0x91</status>
        <midino>0x23</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>play_indicator</key>
        <status>0x93</status>
        <midino>0x23</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[QuickEffectRack1_[Channel1]_Effect1]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x27</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[QuickEffectRack1_[Channel2]_Effect1]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x27</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[QuickEffectRack1_[Channel3]_Effect1]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x27</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
      <output>
        <group>[QuickEffectRack1_[Channel4]_Effect1]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x27</midino>
        <on>0x7F</on>
        <off>0x00</off>
        <maximum>0.5</maximum>
        <minimum>0.0</minimum>
      </output>
    </outputs>
  </controller>
</MixxxControllerPreset>
