<?xml version='1.0' encoding='utf-8'?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.10.0+">
  <info>
    <name>Pioneer CDJ-2000</name>
    <author><PERSON><PERSON> Tuohela &lt;<EMAIL>&gt;</author>
    <description>Pioneer CDJ-2000 configuration for 2 decks on midi channels 1 and 2</description>
    <manual>pioneer_cdj_2000</manual>
  </info>
  <controller id="PIONEER CDJ-2000" port="">
    <scriptfiles>
      <file functionprefix="PioneerCDJ2000" filename="Pioneer-CDJ-2000-scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <!--select_knob_push-->
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0X90</status>
        <midino>0X33</midino>
        <options> <invert/> <selectknob/> </options>
      </control>
      <control>
        <!--seek_backward-->
        <group>[Channel1]</group>
        <key>back</key>
        <status>0X90</status>
        <midino>0X03</midino>
      </control>
      <control>
        <!--autocue_time_mode-->
        <group>[Channel1]</group>
        <key>beats_translate_curpos</key>
        <status>0X90</status>
        <midino>0X0E</midino>
      </control>
      <control>
        <!--tempo_range-->
        <group>[Channel1]</group>
        <key>bpm_tap</key>
        <status>0X90</status>
        <midino>0X10</midino>
      </control>
      <control>
        <!--cue-->
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0X90</status>
        <midino>0X01</midino>
      </control>
      <control>
        <!--eject-->
        <group>[Channel1]</group>
        <key>eject</key>
        <status>0X90</status>
        <midino>0X2F</midino>
      </control>
      <control>
        <!--seek_forward-->
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0X90</status>
        <midino>0X02</midino>
      </control>
      <control>
        <!--autobeat_loop_1-->
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <status>0X90</status>
        <midino>0X28</midino>
      </control>
      <control>
        <!--hot_cue_a-->
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <status>0X90</status>
        <midino>0X18</midino>
      </control>
      <control>
        <!--autobeat_loop_2-->
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <status>0X90</status>
        <midino>0X29</midino>
      </control>
      <control>
        <!--hot_cue_b-->
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <status>0X90</status>
        <midino>0X19</midino>
      </control>
      <control>
        <!--autobeat_loop_4-->
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <status>0X90</status>
        <midino>0X2A</midino>
      </control>
      <control>
        <!--hot_cue_c-->
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <status>0X90</status>
        <midino>0X1A</midino>
      </control>
      <control>
        <!--autobeat_loop_8-->
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <status>0X90</status>
        <midino>0X2B</midino>
      </control>
      <control>
        <!--hot_cue_call-->
        <group>[Channel1]</group>
        <key>hotcue_4_activate</key>
        <status>0X90</status>
        <midino>0X1C</midino>
      </control>
      <control>
        <!--jog_dial-->
        <group>[Channel1]</group>
        <key>PioneerCDJ2000.jog_wheel</key>
        <status>0XB0</status>
        <midino>0X30</midino>
        <options> <script-binding/> </options>
      </control>
      <control>
        <!--master_tempo-->
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0X90</status>
        <midino>0X11</midino>
      </control>
      <control>
        <!--loop_call_next-->
        <group>[Channel1]</group>
        <key>loop_double</key>
        <status>0X90</status>
        <midino>0X0B</midino>
      </control>
      <control>
        <!--autobeat_select-->
        <group>[Channel1]</group>
        <key>loop_enabled</key>
        <status>0X90</status>
        <midino>0X2D</midino>
      </control>
      <control>
        <!--loop_call_previous-->
        <group>[Channel1]</group>
        <key>loop_halve</key>
        <status>0X90</status>
        <midino>0X0C</midino>
      </control>
      <control>
        <!--loop_in-->
        <group>[Channel1]</group>
        <key>loop_in</key>
        <status>0X90</status>
        <midino>0X06</midino>
      </control>
      <control>
        <!--loop_out-->
        <group>[Channel1]</group>
        <key>loop_out</key>
        <status>0X90</status>
        <midino>0X07</midino>
      </control>
      <control>
        <!--needle_touch-->
        <group>[Channel1]</group>
        <key>loop_scale</key>
        <status>0XB0</status>
        <midino>0X1C</midino>
        <options> <invert/> </options>
      </control>
      <control>
        <!--play_pause-->
        <group>[Channel1]</group>
        <key>play</key>
        <status>0X90</status>
        <midino>0X00</midino>
      </control>
      <control>
        <!--tempo-->
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0XB0</status>
        <midino>0X1D</midino>
        <options> <invert/> </options>
      </control>
      <control>
        <!--reloop_exit-->
        <group>[Channel1]</group>
        <key>reloop_exit</key>
        <status>0X90</status>
        <midino>0X08</midino>
      </control>
      <control>
        <!--reverse-->
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0X90</status>
        <midino>0X21</midino>
      </control>
      <control>
        <!--jog_scratch-->
        <group>[Channel1]</group>
        <key>PioneerCDJ2000.jog_scratch</key>
        <status>0XB0</status>
        <midino>0X10</midino>
        <options> <script-binding/> </options>
      </control>
      <control>
        <!--jog_touch-->
        <group>[Channel1]</group>
        <key>scratch2_enable</key>
        <status>0X90</status>
        <midino>0X20</midino>
      </control>
      <control>
        <!--next_track-->
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0X90</status>
        <midino>0X04</midino>
      </control>
      <control>
        <!--previous_track-->
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0X90</status>
        <midino>0X05</midino>
      </control>
      <control>
        <!--select_knob-->
        <group>[Playlist]</group>
        <key>PioneerCDJ2000.select_track_knob</key>
        <status>0XB0</status>
        <midino>0X4F</midino>
        <options> <script-binding/> </options>
      </control>
      <control>
        <!--select_knob_push-->
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0X91</status>
        <midino>0X33</midino>
        <options> <invert/> <selectknob/> </options>
      </control>
      <control>
        <!--seek_backward-->
        <group>[Channel2]</group>
        <key>back</key>
        <status>0X91</status>
        <midino>0X03</midino>
      </control>
      <control>
        <!--autocue_time_mode-->
        <group>[Channel2]</group>
        <key>beats_translate_curpos</key>
        <status>0X91</status>
        <midino>0X0E</midino>
      </control>
      <control>
        <!--tempo_range-->
        <group>[Channel2]</group>
        <key>bpm_tap</key>
        <status>0X91</status>
        <midino>0X10</midino>
      </control>
      <control>
        <!--cue-->
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0X91</status>
        <midino>0X01</midino>
      </control>
      <control>
        <!--eject-->
        <group>[Channel2]</group>
        <key>eject</key>
        <status>0X91</status>
        <midino>0X2F</midino>
      </control>
      <control>
        <!--seek_forward-->
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0X91</status>
        <midino>0X02</midino>
      </control>
      <control>
        <!--autobeat_loop_1-->
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <status>0X91</status>
        <midino>0X28</midino>
      </control>
      <control>
        <!--hot_cue_a-->
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <status>0X91</status>
        <midino>0X18</midino>
      </control>
      <control>
        <!--autobeat_loop_2-->
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <status>0X91</status>
        <midino>0X29</midino>
      </control>
      <control>
        <!--hot_cue_b-->
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <status>0X91</status>
        <midino>0X19</midino>
      </control>
      <control>
        <!--autobeat_loop_4-->
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <status>0X91</status>
        <midino>0X2A</midino>
      </control>
      <control>
        <!--hot_cue_c-->
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <status>0X91</status>
        <midino>0X1A</midino>
      </control>
      <control>
        <!--autobeat_loop_8-->
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <status>0X91</status>
        <midino>0X2B</midino>
      </control>
      <control>
        <!--hot_cue_call-->
        <group>[Channel2]</group>
        <key>hotcue_4_activate</key>
        <status>0X91</status>
        <midino>0X1C</midino>
      </control>
      <control>
        <!--jog_dial-->
        <group>[Channel2]</group>
        <key>PioneerCDJ2000.jog_wheel</key>
        <status>0XB1</status>
        <midino>0X30</midino>
        <options> <script-binding/> </options>
      </control>
      <control>
        <!--master_tempo-->
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0X91</status>
        <midino>0X11</midino>
      </control>
      <control>
        <!--loop_call_next-->
        <group>[Channel2]</group>
        <key>loop_double</key>
        <status>0X91</status>
        <midino>0X0B</midino>
      </control>
      <control>
        <!--autobeat_select-->
        <group>[Channel2]</group>
        <key>loop_enabled</key>
        <status>0X91</status>
        <midino>0X2D</midino>
      </control>
      <control>
        <!--loop_call_previous-->
        <group>[Channel2]</group>
        <key>loop_halve</key>
        <status>0X91</status>
        <midino>0X0C</midino>
      </control>
      <control>
        <!--loop_in-->
        <group>[Channel2]</group>
        <key>loop_in</key>
        <status>0X91</status>
        <midino>0X06</midino>
      </control>
      <control>
        <!--loop_out-->
        <group>[Channel2]</group>
        <key>loop_out</key>
        <status>0X91</status>
        <midino>0X07</midino>
      </control>
      <control>
        <!--needle_touch-->
        <group>[Channel2]</group>
        <key>loop_scale</key>
        <status>0XB1</status>
        <midino>0X1C</midino>
        <options> <invert/> </options>
      </control>
      <control>
        <!--play_pause-->
        <group>[Channel2]</group>
        <key>play</key>
        <status>0X91</status>
        <midino>0X00</midino>
      </control>
      <control>
        <!--tempo-->
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0XB1</status>
        <midino>0X1D</midino>
        <options> <invert/> </options>
      </control>
      <control>
        <!--reloop_exit-->
        <group>[Channel2]</group>
        <key>reloop_exit</key>
        <status>0X91</status>
        <midino>0X08</midino>
      </control>
      <control>
        <!--reverse-->
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0X91</status>
        <midino>0X21</midino>
      </control>
      <control>
        <!--jog_scratch-->
        <group>[Channel2]</group>
        <key>PioneerCDJ2000.jog_scratch</key>
        <status>0XB1</status>
        <midino>0X10</midino>
        <options> <script-binding/> </options>
      </control>
      <control>
        <!--jog_touch-->
        <group>[Channel2]</group>
        <key>scratch2_enable</key>
        <status>0X91</status>
        <midino>0X20</midino>
      </control>
      <control>
        <!--next_track-->
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0X91</status>
        <midino>0X04</midino>
      </control>
      <control>
        <!--previous_track-->
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0X91</status>
        <midino>0X05</midino>
      </control>
      <control>
        <!--select_knob-->
        <group>[Playlist]</group>
        <key>PioneerCDJ2000.select_track_knob</key>
        <status>0XB1</status>
        <midino>0X4F</midino>
        <options> <script-binding/> </options>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
