<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="1.10.1+" schemaVersion="1">
    <info>
        <name>Korg nanoPAD2</name>
        <author><PERSON></author>
        <description>This is a mapping for a Korg nanoPAD2. It provides 16 hotcue controls for deck 1, 14 for deck 2, with the ability to clear each hotcue. Also provides loop controls, kill frequency switches, mute button, reverse play, etc.</description>
        <forums>https://mixxx.discourse.group/t/akai-lpd8-mapping-4-decks-30-hotcues-loops-etc-v2/13064</forums>
        <manual>korg_nanopad2</manual>
    </info>
    <controller id="NANOPAD2">
        <scriptfiles>
            <file functionprefix="NANOPAD2RK" filename="Korg-nanoPAD2-scripts.js"/>
        </scriptfiles>
        <controls>

<!-- ###################  SCENE 1 ################ -->
<!-- HOTCUES DECK 1 - SCENE 1 -->
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2d</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2f</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

<!-- HOTCUES DECK 2 - SCENE 1 -->
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2c</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x2e</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control><!-- CLEAR BUTTON -->
                <group>[Channel2]</group>
                <key>NANOPAD2RK.clear</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control><!-- CLEAR BUTTON OFF -->
                <group>[Channel2]</group>
                <key>NANOPAD2RK.noclear</key>
                <status>0x80</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>



<!-- ###################  SCENE 2 ################ -->
<!-- HOTCUES DECK 1 - SCENE 2 -->
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3d</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3f</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

<!-- HOTCUES DECK 2 - SCENE 2 -->
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3c</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3e</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.hotcueButton</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control><!-- CLEAR BUTTON -->
                <group>[Channel2]</group>
                <key>NANOPAD2RK.clear</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control><!-- CLEAR BUTTON OFF -->
                <group>[Channel2]</group>
                <key>NANOPAD2RK.noclear</key>
                <status>0x80</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!-- ###################  SCENE 3 ################ -->
<!-- LOOP DECK 1 - SCENE 3 -->
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopminus</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x4b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x4b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.loopplus</key>
                <status>0x90</status>
                <midino>0x4d</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x4f</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.reloopButton</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.reloopButtonRelease</key>
                <status>0x80</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

<!-- LOOP DECK 2 - SCENE 3 -->
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopminus</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButton</key>
                <status>0x90</status>
                <midino>0x4a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopButtonRelease</key>
                <status>0x80</status>
                <midino>0x4a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.loopplus</key>
                <status>0x90</status>
                <midino>0x4c</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x4e</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.reloopButton</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.reloopButtonRelease</key>
                <status>0x80</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!-- ###################  SCENE 4 ################ -->
<!-- MISC DECK 1 - SCENE 4 -->
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.muteOn</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NANOPAD2RK.muteOff</key>
                <status>0x80</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <control>
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x57</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x90</status>
                <midino>0x59</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x59</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x90</status>
                <midino>0x5b</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x5b</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5d</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5d</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5f</midino>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5f</midino>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x61</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x63</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>


<!-- MISC DECK 2 - SCENE 4 -->
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.muteOn</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NANOPAD2RK.muteOff</key>
                <status>0x80</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <control>
                <status>0x90</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x90</status>
                <midino>0x58</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x58</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x90</status>
                <midino>0x5a</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
        <control>
                <status>0x80</status>
                <midino>0x5a</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <switch/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5c</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5c</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5e</midino>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5e</midino>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x60</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x62</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>



        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
