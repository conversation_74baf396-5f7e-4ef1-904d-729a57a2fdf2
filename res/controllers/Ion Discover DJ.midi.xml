<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="1.8.0+" schemaVersion="1">
    <info>
        <name>Ion Discover DJ</name>
        <author><PERSON></author>
        <description>This preset provides functionality for the Ion Discover DJ controller for Mixxx</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=939</forums>
        <manual>ion_discover_dj</manual>
    </info>
    <controller id="Ion">
        <scriptfiles>
            <file functionprefix="IonDiscoverDJ" filename="Ion-Discover-DJ-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.toggle_scratch_mode_on</key>
                <status>0x80</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x80</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.jog_touch</key>
                <status>0x80</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_perm_down</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x80</status>
                <midino>0x3C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>IonDiscoverDJ.jog_touch</key>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x80</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.toggle_scratch_mode_on</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0x80</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>IonDiscoverDJ.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x80</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.jog_touch</key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_perm_up</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x80</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x80</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_perm_down</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x80</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_perm_up</key>
                <description>MIDI Learned from 8 messages.</description>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0x80</status>
                <midino>0x4A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>IonDiscoverDJ.LoadSelectedTrackCh1</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_perm_up</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x80</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x80</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x80</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_perm_down</key>
                <description>MIDI Learned from 10 messages.</description>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>IonDiscoverDJ.LoadSelectedTrackCh2</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_perm_down</key>
                <description>MIDI Learned from 6 messages.</description>
                <status>0x80</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>IonDiscoverDJ.jog_touch</key>
                <status>0x80</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_perm_up</key>
                <description>MIDI Learned from 4 messages.</description>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>SelectTrackKnob</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
