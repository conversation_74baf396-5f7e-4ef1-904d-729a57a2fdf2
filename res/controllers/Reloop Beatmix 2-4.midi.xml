<?xml version="1.0" encoding="utf-8"?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
    <info>
        <name>Reloop Beatmix 2/4</name>
        <author><PERSON><PERSON><PERSON><PERSON> &lt;<EMAIL>&gt;</author>
        <description>Controller mapping for the Reloop Beatmix 2/4.</description>
        <forums>https://mixxx.discourse.group/t/reloop-beatmix-2-4-mapping/16049</forums>
        <manual>reloop_beatmix_2</manual>
    </info>
    <controller id="Reloop Beatmix 2/4">
        <scriptfiles>
            <file functionprefix="ReloopBeatmix24" filename="Reloop-Beatmix-2-4-scripts.js"/>
        </scriptfiles>
        <controls>
            <!--
             *************
             * Channel 1 *
             *************
             -->
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>SYNC</description>
                <status>0x91</status>
                <midino>0x21</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.MasterSync</key>
                <description>SHIFT+SYNC</description>
                <status>0x91</status>
                <midino>0x61</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_gotoandplay</key>
                <description>CUP</description>
                <status>0x91</status>
                <midino>0x22</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.Range</key>
                <description>SHIFT+CUP</description>
                <status>0x91</status>
                <midino>0x62</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>CUE</description>
                <status>0x91</status>
                <midino>0x23</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_gotoandstop</key>
                <description>SHIFT+CUE</description>
                <status>0x91</status>
                <midino>0x63</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <description>PLAY/PAUSE</description>
                <status>0x91</status>
                <midino>0x24</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reverseroll</key>
                <description>SHIFT+PLAY/PAUSE</description>
                <status>0x91</status>
                <midino>0x64</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description>PAD1A</description>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT+PAD1A</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD1B</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD1B</description>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description>PAD2A</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT+PAD2A</description>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD2B</description>
                <status>0x91</status>
                <midino>0x11</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD2B</description>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <description>PAD3A</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT+PAD3A</description>
                <status>0x91</status>
                <midino>0x0A</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD3B</description>
                <status>0x91</status>
                <midino>0x12</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD3B</description>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <description>PAD4A</description>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT+PAD4A</description>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD4B</description>
                <status>0x91</status>
                <midino>0x13</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD4B</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_1_toggle</key>
                <description>PAD5A</description>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.LoopSet</key>
                <description>SHIFT+PAD5A</description>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD5B</description>
                <status>0x91</status>
                <midino>0x14</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD5B</description>
                <status>0x91</status>
                <midino>0x1C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_2_toggle</key>
                <description>PAD6A</description>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description>SHIFT+PAD6A</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD6B</description>
                <status>0x91</status>
                <midino>0x15</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD6B</description>
                <status>0x91</status>
                <midino>0x1D</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_4_toggle</key>
                <description>PAD7A</description>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <description>SHIFT+PAD7A</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD7B</description>
                <status>0x91</status>
                <midino>0x16</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD7B</description>
                <status>0x91</status>
                <midino>0x1E</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_8_toggle</key>
                <description>PAD8A</description>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description>SHIFT+PAD8A</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD8B</description>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD8B</description>
                <status>0x91</status>
                <midino>0X1F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND-</description>
                <status>0x91</status>
                <midino>0x26</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <description>PITCHBEND+</description>
                <status>0x91</status>
                <midino>0x27</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND-</description>
                <status>0x91</status>
                <midino>0x66</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND+</description>
                <status>0x91</status>
                <midino>0x67</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.LoadButton</key>
                <description>LOAD</description>
                <status>0x91</status>
                <midino>0x50</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>PFL</description>
                <status>0x91</status>
                <midino>0x52</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.WheelTouch</key>
                <description>WHELTOUCH</description>
                <status>0x91</status>
                <midino>0x3F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>GAIN KNOB</description>
                <status>0xB1</status>
                <midino>0x10</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <description>FILTERHIGH</description>
                <status>0xB1</status>
                <midino>0x11</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <description>FILTERMID</description>
                <status>0xB1</status>
                <midino>0x12</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <description>FILTERLOW</description>
                <status>0xB1</status>
                <midino>0x13</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>LINEFADER</description>
                <status>0xB1</status>
                <midino>0x14</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>WHEELTURN</description>
                <status>0xB1</status>
                <midino>0x60</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>SHIFT+WHEELTURN</description>
                <status>0xB1</status>
                <midino>0x70</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatmix24.PitchSlider</key>
                <description>PITCH 1</description>
                <status>0xE1</status>
                <options>
                    <Script-binding/>
                </options>
            </control>

            <!--
             *************
             * Channel 2 *
             *************
             -->
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>SYNC</description>
                <status>0x92</status>
                <midino>0x21</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.MasterSync</key>
                <description>SHIFT+SYNC</description>
                <status>0x92</status>
                <midino>0x61</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_gotoandplay</key>
                <description>CUP</description>
                <status>0x92</status>
                <midino>0x22</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.Range</key>
                <description>SHIFT+CUP</description>
                <status>0x92</status>
                <midino>0x62</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>CUE</description>
                <status>0x92</status>
                <midino>0x23</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_gotoandstop</key>
                <description>SHIFT+CUE</description>
                <status>0x92</status>
                <midino>0x63</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <description>PLAY/PAUSE</description>
                <status>0x92</status>
                <midino>0x24</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reverseroll</key>
                <description>SHIFT+PLAY/PAUSE</description>
                <status>0x92</status>
                <midino>0x64</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description>PAD1A</description>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT+PAD1A</description>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD1B</description>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD1B</description>
                <status>0x92</status>
                <midino>0x18</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description>PAD2A</description>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT+PAD2A</description>
                <status>0x92</status>
                <midino>0x09</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD2B</description>
                <status>0x92</status>
                <midino>0x11</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD2B</description>
                <status>0x92</status>
                <midino>0x19</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <description>PAD3A</description>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT+PAD3A</description>
                <status>0x92</status>
                <midino>0x0A</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD3B</description>
                <status>0x92</status>
                <midino>0x12</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD3B</description>
                <status>0x92</status>
                <midino>0x1A</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <description>PAD4A</description>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT+PAD4A</description>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD4B</description>
                <status>0x92</status>
                <midino>0x13</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD4B</description>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_1_toggle</key>
                <description>PAD5A</description>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.LoopSet</key>
                <description>SHIFT+PAD5A</description>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD5B</description>
                <status>0x92</status>
                <midino>0x14</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD5B</description>
                <status>0x92</status>
                <midino>0x1C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_2_toggle</key>
                <description>PAD6A</description>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <description>SHIFT+PAD6A</description>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD6B</description>
                <status>0x92</status>
                <midino>0x15</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD6B</description>
                <status>0x92</status>
                <midino>0x1D</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_4_toggle</key>
                <description>PAD7A</description>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <description>SHIFT+PAD7A</description>
                <status>0x92</status>
                <midino>0x0E</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD7B</description>
                <status>0x92</status>
                <midino>0x16</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD7B</description>
                <status>0x92</status>
                <midino>0x1E</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_8_toggle</key>
                <description>PAD8A</description>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description>SHIFT+PAD8A</description>
                <status>0x92</status>
                <midino>0x0F</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD8B</description>
                <status>0x92</status>
                <midino>0x17</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD8B</description>
                <status>0x92</status>
                <midino>0X1F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND-</description>
                <status>0x92</status>
                <midino>0x26</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <description>PITCHBEND+</description>
                <status>0x92</status>
                <midino>0x27</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND-</description>
                <status>0x92</status>
                <midino>0x66</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND+</description>
                <status>0x92</status>
                <midino>0x67</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.LoadButton</key>
                <description>LOAD</description>
                <status>0x92</status>
                <midino>0x50</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>PFL</description>
                <status>0x92</status>
                <midino>0x52</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.WheelTouch</key>
                <description>WHELTOUCH</description>
                <status>0x92</status>
                <midino>0x3F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>GAIN KNOB</description>
                <status>0xB2</status>
                <midino>0x10</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <description>FILTERHIGH</description>
                <status>0xB2</status>
                <midino>0x11</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <description>FILTERMID</description>
                <status>0xB2</status>
                <midino>0x12</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <description>FILTERLOW</description>
                <status>0xB2</status>
                <midino>0x13</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>LINEFADER</description>
                <status>0xB2</status>
                <midino>0x14</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>WHEELTURN</description>
                <status>0xB2</status>
                <midino>0x60</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>SHIFT+WHEELTURN</description>
                <status>0xB2</status>
                <midino>0x70</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatmix24.PitchSlider</key>
                <description>PITCH 2</description>
                <status>0xE2</status>
                <options>
                    <Script-binding/>
                </options>
            </control>

            <!--
             *************
             * Channel 3 *
             *************
             -->
            <control>
                <group>[Channel3]</group>
                <key>sync_enabled</key>
                <description>SYNC</description>
                <status>0x93</status>
                <midino>0x21</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.MasterSync</key>
                <description>SHIFT+SYNC</description>
                <status>0x93</status>
                <midino>0x61</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>cue_gotoandplay</key>
                <description>CUP</description>
                <status>0x93</status>
                <midino>0x22</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.Range</key>
                <description>SHIFT+CUP</description>
                <status>0x93</status>
                <midino>0x62</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>cue_default</key>
                <description>CUE</description>
                <status>0x93</status>
                <midino>0x23</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>cue_gotoandstop</key>
                <description>SHIFT+CUE</description>
                <status>0x93</status>
                <midino>0x63</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>play</key>
                <description>PLAY/PAUSE</description>
                <status>0x93</status>
                <midino>0x24</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>reverseroll</key>
                <description>SHIFT+PLAY/PAUSE</description>
                <status>0x93</status>
                <midino>0x64</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_1_activate</key>
                <description>PAD1A</description>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT+PAD1A</description>
                <status>0x93</status>
                <midino>0x08</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD1B</description>
                <status>0x93</status>
                <midino>0x10</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD1B</description>
                <status>0x93</status>
                <midino>0x18</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_2_activate</key>
                <description>PAD2A</description>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT+PAD2A</description>
                <status>0x93</status>
                <midino>0x09</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD2B</description>
                <status>0x93</status>
                <midino>0x11</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD2B</description>
                <status>0x93</status>
                <midino>0x19</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_3_activate</key>
                <description>PAD3A</description>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT+PAD3A</description>
                <status>0x93</status>
                <midino>0x0A</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD3B</description>
                <status>0x93</status>
                <midino>0x12</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD3B</description>
                <status>0x93</status>
                <midino>0x1A</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_4_activate</key>
                <description>PAD4A</description>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT+PAD4A</description>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD4B</description>
                <status>0x93</status>
                <midino>0x13</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD4B</description>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_1_toggle</key>
                <description>PAD5A</description>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.LoopSet</key>
                <description>SHIFT+PAD5A</description>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD5B</description>
                <status>0x93</status>
                <midino>0x14</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD5B</description>
                <status>0x93</status>
                <midino>0x1C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_2_toggle</key>
                <description>PAD6A</description>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_halve</key>
                <description>SHIFT+PAD6A</description>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD6B</description>
                <status>0x93</status>
                <midino>0x15</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD6B</description>
                <status>0x93</status>
                <midino>0x1D</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_4_toggle</key>
                <description>PAD7A</description>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_double</key>
                <description>SHIFT+PAD7A</description>
                <status>0x93</status>
                <midino>0x0E</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD7B</description>
                <status>0x93</status>
                <midino>0x16</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD7B</description>
                <status>0x93</status>
                <midino>0x1E</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_8_toggle</key>
                <description>PAD8A</description>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>reloop_exit</key>
                <description>SHIFT+PAD8A</description>
                <status>0x93</status>
                <midino>0x0F</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD8B</description>
                <status>0x93</status>
                <midino>0x17</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD8B</description>
                <status>0x93</status>
                <midino>0X1F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND-</description>
                <status>0x93</status>
                <midino>0x26</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_up</key>
                <description>PITCHBEND+</description>
                <status>0x93</status>
                <midino>0x27</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND-</description>
                <status>0x93</status>
                <midino>0x66</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND+</description>
                <status>0x93</status>
                <midino>0x67</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.LoadButton</key>
                <description>LOAD</description>
                <status>0x93</status>
                <midino>0x50</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>pfl</key>
                <description>PFL</description>
                <status>0x93</status>
                <midino>0x52</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.WheelTouch</key>
                <description>WHELTOUCH</description>
                <status>0x93</status>
                <midino>0x3F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>pregain</key>
                <description>GAIN KNOB</description>
                <status>0xB3</status>
                <midino>0x10</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter3</key>
                <description>FILTERHIGH</description>
                <status>0xB3</status>
                <midino>0x11</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter2</key>
                <description>FILTERMID</description>
                <status>0xB3</status>
                <midino>0x12</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter1</key>
                <description>FILTERLOW</description>
                <status>0xB3</status>
                <midino>0x13</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>volume</key>
                <description>LINEFADER</description>
                <status>0xB3</status>
                <midino>0x14</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>WHEELTURN</description>
                <status>0xB3</status>
                <midino>0x60</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>SHIFT+WHEELTURN</description>
                <status>0xB3</status>
                <midino>0x70</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>ReloopBeatmix24.PitchSlider</key>
                <description>PITCH 3</description>
                <status>0xE3</status>
                <options>
                    <Script-binding/>
                </options>
            </control>

            <!--
             *************
             * Channel 4 *
             *************
             -->
            <control>
                <group>[Channel4]</group>
                <key>sync_enabled</key>
                <description>SYNC</description>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.MasterSync</key>
                <description>SHIFT+SYNC</description>
                <status>0x94</status>
                <midino>0x61</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>cue_gotoandplay</key>
                <description>CUP</description>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.Range</key>
                <description>SHIFT+CUP</description>
                <status>0x94</status>
                <midino>0x62</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>cue_default</key>
                <description>CUE</description>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>cue_gotoandstop</key>
                <description>SHIFT+CUE</description>
                <status>0x94</status>
                <midino>0x63</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>play</key>
                <description>PLAY/PAUSE</description>
                <status>0x94</status>
                <midino>0x24</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>reverseroll</key>
                <description>SHIFT+PLAY/PAUSE</description>
                <status>0x94</status>
                <midino>0x64</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_1_activate</key>
                <description>PAD1A</description>
                <status>0x94</status>
                <midino>0x00</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT+PAD1A</description>
                <status>0x94</status>
                <midino>0x08</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD1B</description>
                <status>0x94</status>
                <midino>0x10</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD1B</description>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_2_activate</key>
                <description>PAD2A</description>
                <status>0x94</status>
                <midino>0x01</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT+PAD2A</description>
                <status>0x94</status>
                <midino>0x09</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD2B</description>
                <status>0x94</status>
                <midino>0x11</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD2B</description>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_3_activate</key>
                <description>PAD3A</description>
                <status>0x94</status>
                <midino>0x02</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT+PAD3A</description>
                <status>0x94</status>
                <midino>0x0A</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD3B</description>
                <status>0x94</status>
                <midino>0x12</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD3B</description>
                <status>0x94</status>
                <midino>0x1A</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_4_activate</key>
                <description>PAD4A</description>
                <status>0x94</status>
                <midino>0x03</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT+PAD4A</description>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD4B</description>
                <status>0x94</status>
                <midino>0x13</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD4B</description>
                <status>0x94</status>
                <midino>0x1B</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_1_toggle</key>
                <description>PAD5A</description>
                <status>0x94</status>
                <midino>0x04</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.LoopSet</key>
                <description>SHIFT+PAD5A</description>
                <status>0x94</status>
                <midino>0x0C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD5B</description>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD5B</description>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_2_toggle</key>
                <description>PAD6A</description>
                <status>0x94</status>
                <midino>0x05</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_halve</key>
                <description>SHIFT+PAD6A</description>
                <status>0x94</status>
                <midino>0x0D</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD6B</description>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD6B</description>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_4_toggle</key>
                <description>PAD7A</description>
                <status>0x94</status>
                <midino>0x06</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_double</key>
                <description>SHIFT+PAD7A</description>
                <status>0x94</status>
                <midino>0x0E</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD7B</description>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD7B</description>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_8_toggle</key>
                <description>PAD8A</description>
                <status>0x94</status>
                <midino>0x07</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>reloop_exit</key>
                <description>SHIFT+PAD8A</description>
                <status>0x94</status>
                <midino>0x0F</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.SamplerPad</key>
                <description>PAD8B</description>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>ReloopBeatmix24.ShiftSamplerPad</key>
                <description>SHIFT+PAD8B</description>
                <status>0x94</status>
                <midino>0X1F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND-</description>
                <status>0x94</status>
                <midino>0x26</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_up</key>
                <description>PITCHBEND+</description>
                <status>0x94</status>
                <midino>0x27</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND-</description>
                <status>0x94</status>
                <midino>0x66</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.ActivateFx</key>
                <description>SHIFT+PITCHBEND+</description>
                <status>0x94</status>
                <midino>0x67</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.LoadButton</key>
                <description>LOAD</description>
                <status>0x94</status>
                <midino>0x50</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>pfl</key>
                <description>PFL</description>
                <status>0x94</status>
                <midino>0x52</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.WheelTouch</key>
                <description>WHELTOUCH</description>
                <status>0x94</status>
                <midino>0x3F</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>pregain</key>
                <description>GAIN KNOB</description>
                <status>0xB4</status>
                <midino>0x10</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter3</key>
                <description>FILTERHIGH</description>
                <status>0xB4</status>
                <midino>0x11</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter2</key>
                <description>FILTERMID</description>
                <status>0xB4</status>
                <midino>0x12</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter1</key>
                <description>FILTERLOW</description>
                <status>0xB4</status>
                <midino>0x13</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>volume</key>
                <description>LINEFADER</description>
                <status>0xB4</status>
                <midino>0x14</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>WHEELTURN</description>
                <status>0xB4</status>
                <midino>0x60</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.WheelTurn</key>
                <description>SHIFT+WHEELTURN</description>
                <status>0xB4</status>
                <midino>0x70</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>ReloopBeatmix24.PitchSlider</key>
                <description>PITCH 4</description>
                <status>0xE4</status>
                <options>
                    <Script-binding/>
                </options>
            </control>
             <!--
             *******************
             * Effects Section *
             *******************
             -->
             <!--
                 - Effect Unit 1
             -->
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>L-FX1</description>
                 <status>0xB1</status>
                 <midino>0x01</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+L-FX1</description>
                 <status>0xB1</status>
                 <midino>0x41</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>L-FX1-OFF</description>
                 <status>0x91</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+L-FX1-OFF</description>
                 <status>0x91</status>
                 <midino>0x74</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>L-FX2</description>
                 <status>0xB1</status>
                 <midino>0x02</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+L-FX2</description>
                 <status>0xB1</status>
                 <midino>0x42</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect2]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>L-FX2-OFF</description>
                 <status>0x91</status>
                 <midino>0x72</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect2]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+L-FX2-OFF</description>
                 <status>0x91</status>
                 <midino>0x75</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>L-FX3</description>
                 <status>0xB1</status>
                 <midino>0x03</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+L-FX3</description>
                 <status>0xB1</status>
                 <midino>0x43</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect3]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>L-FX3-OFF</description>
                 <status>0x91</status>
                 <midino>0x73</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1_Effect3]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+L-FX3-OFF</description>
                 <status>0x91</status>
                 <midino>0x76</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.FxEncoderTurn</key>
                 <description>L-EncoderD1</description>
                 <status>0xB1</status>
                 <midino>0x61</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderTurn</key>
                 <description>SHIFT+L-EncoderD1</description>
                 <status>0xB1</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.FxEncoderPush</key>
                 <description>L-EncoderD1-PUSH</description>
                 <status>0x91</status>
                 <midino>0x25</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderPush</key>
                 <description>SHIFT+L-EncoderD1-PUSH</description>
                 <status>0x91</status>
                 <midino>0x65</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.FxEncoderTurn</key>
                 <description>L-EncoderD3</description>
                 <status>0xB3</status>
                 <midino>0x61</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderTurn</key>
                 <description>SHIFT+L-EncoderD3</description>
                 <status>0xB3</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.FxEncoderPush</key>
                 <description>L-EncoderD3-PUSH</description>
                 <status>0x93</status>
                 <midino>0x25</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit1]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderPush</key>
                 <description>SHIFT+L-EncoderD3-PUSH</description>
                 <status>0x93</status>
                 <midino>0x65</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <!--
                 - Effect Unit 2
             -->
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>R-FX1</description>
                 <status>0xB2</status>
                 <midino>0x01</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+R-FX1</description>
                 <status>0xB2</status>
                 <midino>0x41</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>R-FX1-OFF</description>
                 <status>0x92</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+R-FX1-OFF</description>
                 <status>0x92</status>
                 <midino>0x74</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>R-FX2</description>
                 <status>0xB2</status>
                 <midino>0x02</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+R-FX2</description>
                 <status>0xB2</status>
                 <midino>0x42</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect2]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>R-FX2-OFF</description>
                 <status>0x92</status>
                 <midino>0x72</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect2]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+R-FX2-OFF</description>
                 <status>0x92</status>
                 <midino>0x75</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.FxKnobTurn</key>
                 <description>R-FX3</description>
                 <status>0xB2</status>
                 <midino>0x03</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect1]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobTurn</key>
                 <description>SHIFT+R-FX3</description>
                 <status>0xB2</status>
                 <midino>0x43</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect3]</group>
                 <key>ReloopBeatmix24.FxKnobOnOff</key>
                 <description>R-FX3-OFF</description>
                 <status>0x92</status>
                 <midino>0x73</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2_Effect3]</group>
                 <key>ReloopBeatmix24.ShiftFxKnobOnOff</key>
                 <description>SHIFT+R-FX3-OFF</description>
                 <status>0x92</status>
                 <midino>0x76</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.FxEncoderTurn</key>
                 <description>R-EncoderD2</description>
                 <status>0xB2</status>
                 <midino>0x61</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderTurn</key>
                 <description>SHIFT+R-EncoderD2</description>
                 <status>0xB2</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.FxEncoderPush</key>
                 <description>R-EncoderD2-PUSH</description>
                 <status>0x92</status>
                 <midino>0x25</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderPush</key>
                 <description>SHIFT+R-EncoderD2-PUSH</description>
                 <status>0x92</status>
                 <midino>0x65</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.FxEncoderTurn</key>
                 <description>R-EncoderD4</description>
                 <status>0xB4</status>
                 <midino>0x61</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderTurn</key>
                 <description>SHIFT+R-EncoderD4</description>
                 <status>0xB4</status>
                 <midino>0x71</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.FxEncoderPush</key>
                 <description>R-EncoderD4-PUSH</description>
                 <status>0x94</status>
                 <midino>0x25</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <control>
                 <group>[EffectRack1_EffectUnit2]</group>
                 <key>ReloopBeatmix24.ShiftFxEncoderPush</key>
                 <description>SHIFT+R-EncoderD4-PUSH</description>
                 <status>0x94</status>
                 <midino>0x65</midino>
                 <options>
                     <Script-binding/>
                 </options>
             </control>
             <!--
             ******************
             * Master Section *
             ******************
             -->
            <control>
                <group>[Sampler1]</group>
                <key>ReloopBeatmix24.SamplerVol</key>
                <description>Sampler Volume Slider</description>
                <status>0xB5</status>
                <midino>0x03</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <description>CROSSFADER</description>
                <status>0xB5</status>
                <midino>0x2F</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>volume</key>
                <description>MASTERVOL</description>
                <status>0xB5</status>
                <midino>0x00</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <description>CUE MIX</description>
                <status>0xB5</status>
                <midino>0x02</midino>
                <options>
                    <Normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatmix24.TraxTurn</key>
                <description>TRAX</description>
                <status>0xB5</status>
                <midino>0x60</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatmix24.TraxPush</key>
                <description>TRAX PUSH</description>
                <status>0x95</status>
                <midino>0x09</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatmix24.ShiftTraxTurn</key>
                <description>SHIFT+TRAX</description>
                <status>0xB5</status>
                <midino>0x70</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatmix24.TraxPush</key>
                <description>SHIFT+TRAX PUSH</description>
                <status>0x95</status>
                <midino>0x49</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatmix24.BackButton</key>
                <description>BACK</description>
                <status>0x95</status>
                <midino>0x08</midino>
                <options>
                    <Script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
			<output>
				<group>[Channel1]</group>
				<key>sync_enabled</key>
				<status>0x91</status>
				<midino>0x20</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>sync_enabled</key>
				<status>0x91</status>
				<midino>0x60</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>cue_indicator</key>
				<status>0x91</status>
				<midino>0x22</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>cue_indicator</key>
				<status>0x91</status>
				<midino>0x62</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>play_indicator</key>
				<status>0x91</status>
				<midino>0x23</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>play_indicator</key>
				<status>0x91</status>
				<midino>0x63</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>pfl</key>
				<status>0x91</status>
				<midino>0x52</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>pfl</key>
				<status>0x91</status>
				<midino>0x72</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<status>0x91</status>
				<midino>0x00</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<status>0x91</status>
				<midino>0x01</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<status>0x91</status>
				<midino>0x02</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<status>0x91</status>
				<midino>0x03</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatloop_1_enabled</key>
				<status>0x91</status>
				<midino>0x04</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatloop_2_enabled</key>
				<status>0x91</status>
				<midino>0x05</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatloop_4_enabled</key>
				<status>0x91</status>
				<midino>0x06</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatloop_8_enabled</key>
				<status>0x91</status>
				<midino>0x07</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<status>0x91</status>
				<midino>0x10</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<status>0x91</status>
				<midino>0x11</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<status>0x91</status>
				<midino>0x12</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<status>0x91</status>
				<midino>0x13</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<status>0x91</status>
				<midino>0x40</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<status>0x91</status>
				<midino>0x41</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<status>0x91</status>
				<midino>0x42</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<status>0x91</status>
				<midino>0x43</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_enabled</key>
				<status>0x91</status>
				<midino>0x47</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<status>0x91</status>
				<midino>0x18</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<status>0x91</status>
				<midino>0x19</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<status>0x91</status>
				<midino>0x1A</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<status>0x91</status>
				<midino>0x1B</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel1]_enable</key>
				<status>0x91</status>
				<midino>0x25</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel1]_enable</key>
				<status>0x91</status>
				<midino>0x24</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel1]_enable</key>
				<status>0x91</status>
				<midino>0x65</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel1]_enable</key>
				<status>0x91</status>
				<midino>0x64</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>sync_enabled</key>
				<status>0x92</status>
				<midino>0x20</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>sync_enabled</key>
				<status>0x92</status>
				<midino>0x60</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>cue_indicator</key>
				<status>0x92</status>
				<midino>0x22</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>cue_indicator</key>
				<status>0x92</status>
				<midino>0x62</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>play_indicator</key>
				<status>0x92</status>
				<midino>0x23</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>play_indicator</key>
				<status>0x92</status>
				<midino>0x63</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>pfl</key>
				<status>0x92</status>
				<midino>0x52</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>pfl</key>
				<status>0x92</status>
				<midino>0x72</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<status>0x92</status>
				<midino>0x00</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<status>0x92</status>
				<midino>0x01</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<status>0x92</status>
				<midino>0x02</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<status>0x92</status>
				<midino>0x03</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatloop_1_enabled</key>
				<status>0x92</status>
				<midino>0x04</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatloop_2_enabled</key>
				<status>0x92</status>
				<midino>0x05</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatloop_4_enabled</key>
				<status>0x92</status>
				<midino>0x06</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatloop_8_enabled</key>
				<status>0x92</status>
				<midino>0x07</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<status>0x92</status>
				<midino>0x10</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<status>0x92</status>
				<midino>0x11</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<status>0x92</status>
				<midino>0x12</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<status>0x92</status>
				<midino>0x13</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<status>0x92</status>
				<midino>0x40</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<status>0x92</status>
				<midino>0x41</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<status>0x92</status>
				<midino>0x42</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<status>0x92</status>
				<midino>0x43</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_enabled</key>
				<status>0x92</status>
				<midino>0x47</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<status>0x92</status>
				<midino>0x18</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<status>0x92</status>
				<midino>0x19</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<status>0x92</status>
				<midino>0x1A</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<status>0x92</status>
				<midino>0x1B</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel2]_enable</key>
				<status>0x92</status>
				<midino>0x25</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel2]_enable</key>
				<status>0x92</status>
				<midino>0x24</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel2]_enable</key>
				<status>0x92</status>
				<midino>0x65</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel2]_enable</key>
				<status>0x92</status>
				<midino>0x64</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>sync_enabled</key>
				<status>0x93</status>
				<midino>0x20</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>sync_enabled</key>
				<status>0x93</status>
				<midino>0x60</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>cue_indicator</key>
				<status>0x93</status>
				<midino>0x22</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>cue_indicator</key>
				<status>0x93</status>
				<midino>0x62</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>play_indicator</key>
				<status>0x93</status>
				<midino>0x23</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>play_indicator</key>
				<status>0x93</status>
				<midino>0x63</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>pfl</key>
				<status>0x93</status>
				<midino>0x52</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>pfl</key>
				<status>0x93</status>
				<midino>0x72</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_1_status</key>
				<status>0x93</status>
				<midino>0x00</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_2_status</key>
				<status>0x93</status>
				<midino>0x01</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_3_status</key>
				<status>0x93</status>
				<midino>0x02</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_4_status</key>
				<status>0x93</status>
				<midino>0x03</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>beatloop_1_enabled</key>
				<status>0x93</status>
				<midino>0x04</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>beatloop_2_enabled</key>
				<status>0x93</status>
				<midino>0x05</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>beatloop_4_enabled</key>
				<status>0x93</status>
				<midino>0x06</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>beatloop_8_enabled</key>
				<status>0x93</status>
				<midino>0x07</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_1_status</key>
				<status>0x93</status>
				<midino>0x10</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_2_status</key>
				<status>0x93</status>
				<midino>0x11</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_3_status</key>
				<status>0x93</status>
				<midino>0x12</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_4_status</key>
				<status>0x93</status>
				<midino>0x13</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_1_status</key>
				<status>0x93</status>
				<midino>0x40</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_2_status</key>
				<status>0x93</status>
				<midino>0x41</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_3_status</key>
				<status>0x93</status>
				<midino>0x42</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_4_status</key>
				<status>0x93</status>
				<midino>0x43</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>loop_enabled</key>
				<status>0x93</status>
				<midino>0x47</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_1_status</key>
				<status>0x93</status>
				<midino>0x18</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_2_status</key>
				<status>0x93</status>
				<midino>0x19</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_3_status</key>
				<status>0x93</status>
				<midino>0x1A</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>hotcue_4_status</key>
				<status>0x93</status>
				<midino>0x1B</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel3]_enable</key>
				<status>0x93</status>
				<midino>0x25</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel3]_enable</key>
				<status>0x93</status>
				<midino>0x24</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel3]_enable</key>
				<status>0x93</status>
				<midino>0x65</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel3]_enable</key>
				<status>0x93</status>
				<midino>0x64</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>sync_enabled</key>
				<status>0x94</status>
				<midino>0x20</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>sync_enabled</key>
				<status>0x94</status>
				<midino>0x60</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>cue_indicator</key>
				<status>0x94</status>
				<midino>0x22</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>cue_indicator</key>
				<status>0x94</status>
				<midino>0x62</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>play_indicator</key>
				<status>0x94</status>
				<midino>0x23</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>play_indicator</key>
				<status>0x94</status>
				<midino>0x63</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>pfl</key>
				<status>0x94</status>
				<midino>0x52</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>pfl</key>
				<status>0x94</status>
				<midino>0x72</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_1_status</key>
				<status>0x94</status>
				<midino>0x00</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_2_status</key>
				<status>0x94</status>
				<midino>0x01</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_3_status</key>
				<status>0x94</status>
				<midino>0x02</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_4_status</key>
				<status>0x94</status>
				<midino>0x03</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>beatloop_1_enabled</key>
				<status>0x94</status>
				<midino>0x04</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>beatloop_2_enabled</key>
				<status>0x94</status>
				<midino>0x05</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>beatloop_4_enabled</key>
				<status>0x94</status>
				<midino>0x06</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>beatloop_8_enabled</key>
				<status>0x94</status>
				<midino>0x07</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_1_status</key>
				<status>0x94</status>
				<midino>0x10</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_2_status</key>
				<status>0x94</status>
				<midino>0x11</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_3_status</key>
				<status>0x94</status>
				<midino>0x12</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_4_status</key>
				<status>0x94</status>
				<midino>0x13</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_1_status</key>
				<status>0x94</status>
				<midino>0x40</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_2_status</key>
				<status>0x94</status>
				<midino>0x41</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_3_status</key>
				<status>0x94</status>
				<midino>0x42</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_4_status</key>
				<status>0x94</status>
				<midino>0x43</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>loop_enabled</key>
				<status>0x94</status>
				<midino>0x47</midino>
				<on>0x2a</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_1_status</key>
				<status>0x94</status>
				<midino>0x18</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_2_status</key>
				<status>0x94</status>
				<midino>0x19</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_3_status</key>
				<status>0x94</status>
				<midino>0x1A</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>hotcue_4_status</key>
				<status>0x94</status>
				<midino>0x1B</midino>
				<on>0x55</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel4]_enable</key>
				<status>0x94</status>
				<midino>0x25</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel4]_enable</key>
				<status>0x94</status>
				<midino>0x24</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel4]_enable</key>
				<status>0x94</status>
				<midino>0x65</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel4]_enable</key>
				<status>0x94</status>
				<midino>0x64</midino>
				<on>0x7f</on>
				<minimum>0.1</minimum>
			</output>
        </outputs>
    </controller>
</MixxxControllerPreset>
