<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
<info>
  <name>Novation Dicer</name>
  <author><PERSON> (<EMAIL>)</author>
  <description>Implements cuepoints, looping, loop rolls, basic effects, and navigation.</description>
  <forums>https://mixxx.discourse.group/t/novation-dicer-mapping/12329</forums>
  <manual>novation_dicer</manual>
</info>
    <controller id="Dicer Novation Dicer">
        <scriptfiles>
            <file functionprefix="NovationDicer" filename="Novation-Dicer-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x9c</status>
                <midino>0x42</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x44</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x45</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x3c</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x3d</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x3e</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x3f</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x41</midino>
                <group>[Channel1]</group>
                <key>beatloop_1_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x42</midino>
                <group>[Channel1]</group>
                <key>beatloop_2_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>beatloop_4_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x44</midino>
                <group>[Channel1]</group>
                <key>beatloop_8_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9b</status>
                <midino>0x45</midino>
                <group>[Channel1]</group>
                <key>beatloop_16_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x3c</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x3d</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x3e</midino>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x3f</midino>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>hotcue_5_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x41</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x42</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x44</midino>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.cueButton</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9a</status>
                <midino>0x45</midino>
                <group>[Channel1]</group>
                <key>hotcue_5_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x3e</midino>
                <group>[QuickEffectRack1_[Channel2]_Effect1]</group>
                <key>NovationDicer.toggleQuickEffect</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x3f</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.transformer</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x41</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x42</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x43</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x44</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9f</status>
                <midino>0x45</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x3f</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x41</midino>
                <group>[Channel2]</group>
                <key>beatloop_1_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>beatloop_2_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x43</midino>
                <group>[Channel2]</group>
                <key>beatloop_4_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x44</midino>
                <group>[Channel2]</group>
                <key>beatloop_8_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9e</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>beatloop_16_toggle</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x3f</midino>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>hotcue_5_activate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x41</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x43</midino>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x44</midino>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9d</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>hotcue_5_clear</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x3c</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x3d</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.cueButton</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x3e</midino>
                <group>[QuickEffectRack1_[Channel1]_Effect1]</group>
                <key>NovationDicer.toggleQuickEffect</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x3f</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.transformer</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>NovationDicer.effectThenContinue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x9c</status>
                <midino>0x41</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x44</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x3f</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_5_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x45</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_5_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x40</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9e</status>
                <midino>0x43</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9e</status>
                <midino>0x42</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x41</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x3c</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_5_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x45</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_5_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x40</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x42</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x3d</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9e</status>
                <midino>0x41</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x41</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x3c</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[QuickEffectRack1_[Channel1]_Effect1]</group>
                <key>enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9c</status>
                <midino>0x3e</midino>
                <on>0x4f</on>
                <off>0x41</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9b</status>
                <midino>0x43</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9b</status>
                <midino>0x42</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x43</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x3e</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x42</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x3d</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[QuickEffectRack1_[Channel2]_Effect1]</group>
                <key>enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9f</status>
                <midino>0x3e</midino>
                <on>0x4f</on>
                <off>0x41</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x43</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9d</status>
                <midino>0x3e</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_16_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9b</status>
                <midino>0x45</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_8_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9e</status>
                <midino>0x44</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_1_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9b</status>
                <midino>0x41</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x44</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9a</status>
                <midino>0x3f</midino>
                <on>0xe</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_8_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9b</status>
                <midino>0x44</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_16_enabled</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x9e</status>
                <midino>0x45</midino>
                <on>0x7f</on>
                <off>0x71</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
