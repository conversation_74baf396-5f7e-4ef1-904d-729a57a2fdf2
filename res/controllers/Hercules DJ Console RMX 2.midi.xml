<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.0.0+"
    schemaVersion="1">
    <info>
        <!-- In Geany, go to Document and then Fold All for easier format. -->
        <name>Hercules DJ Console RMX2</name>
        <author>Circuitfry</author>
        <description>This console's mapping comes with a script.</description>
        <manual>hercules_dj_console_rmx2</manual>
    </info>
    <controller id="DJConsole">
        <scriptfiles>
            <file functionprefix="DJCRMX2" filename="Hercules-DJ-Console-RMX-2-scripts.js"/>
        </scriptfiles>
        <controls>
            <!-- General Controls -->
            <control>
                <!-- Master Volume Pt 1 -->
                <group>[Master]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x44</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x45</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- Master HeadMix Pt 1 -->
                <group>[Master]</group>
                <key>headMix</key>
                <description>MIDI Learned from 462 messages.</description>
                <status>0xB0</status>
                <midino>0x46</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <description>MIDI Learned from 462 messages.</description>
                <status>0xB0</status>
                <midino>0x47</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- Master Crossfader Pt 1 -->
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x48</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x49</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- Microphone -->
                <status>0x90</status>
                <!-- ??? -->
                <midino>0x48</midino>
                <group>[Microphone]</group>
                <key>DJCRMX2.micSwitch</key>
                <description>Utilize Microphone</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Select UP -->
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Select DOWN -->
                <status>0x90</status>
                <midino>0x46</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Select LEFT -->
                <status>0x90</status>
                <midino>0x44</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Select RIGHT -->
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- General Controls -->
            <!-- Deck Controls -->
            <control>
                <!-- Left Load Song -->
                <status>0x90</status>
                <midino>0x24</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Load Song -->
                <status>0x90</status>
                <midino>0x35</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- L Play -->
                <status>0x90</status>
                <midino>0x21</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- R Play -->
                <status>0x90</status>
                <midino>0x32</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Headphones -->
                <status>0x90</status>
                <midino>0x2e</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Headphones -->
                <status>0x90</status>
                <midino>0x3F</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- L Cue -->
                <status>0x90</status>
                <midino>0x22</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- R Cue -->
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- L Sync -->
                <status>0x90</status>
                <midino>0x23</midino>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- R Sync -->
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- L Tempo Slider Pt 1 -->
                <group>[Channel1]</group>
                <key>rate</key>
                <description></description>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <description>MIDI Learned from 3206 messages.</description>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- R Tempo Slider Pt 1 -->
                <group>[Channel2]</group>
                <key>rate</key>
                <description>MIDI Learned from 2512 messages.</description>
                <status>0xB0</status>
                <midino>0x38</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <description>MIDI Learned from 2512 messages.</description>
                <status>0xB0</status>
                <midino>0x39</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- L Fader Pt 1 -->
                <group>[Channel1]</group>
                <key>volume</key>
                <description>MIDI Learned from 406 messages.</description>
                <status>0xB0</status>
                <midino>0x3A</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>MIDI Learned from 406 messages.</description>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- R Fader Pt 1 -->
                <group>[Channel2]</group>
                <key>volume</key>
                <description>MIDI Learned from 504 messages.</description>
                <status>0xB0</status>
                <midino>0x4A</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-msb/>
                </options>
            </control>
           <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>MIDI Learned from 504 messages.</description>
                <status>0xB0</status>
                <midino>0x4B</midino>
                <options>
                    <soft-takeover/>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <!-- L Gain -->
                <status>0xb0</status>
                <midino>0x42</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- R Gain -->
                <status>0xb0</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Left Fast Rewind -->
                <status>0x90</status>
                <midino>0x26</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Fast Rewind + Shift-->
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel1]</group>
                <key>start</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Fast Rewind -->
                <status>0x90</status>
                <midino>0x37</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Fast Rewind + Shift-->
                <status>0x90</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>start</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Fast Forward -->
                <status>0x90</status>
                <midino>0x27</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Fast Forward + Shift-->
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>end</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Fast Forward -->
                <status>0x90</status>
                <midino>0x38</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Fast Forward + Shift -->
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel2]</group>
                <key>end</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Pitch Down -->
                <status>0x90</status>
                <midino>0x2C</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down_small</key>
                <description>L Pitch Down</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Pitch Up -->
                <status>0x90</status>
                <midino>0x2D</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up_small</key>
                <description>L Pitch Down</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Pitch Down -->
                <status>0x90</status>
                <midino>0x3D</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down_small</key>
                <description>R Pitch Down</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Pitch Up -->
                <status>0x90</status>
                <midino>0x3E</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up_small</key>
                <description>R Pitch Down</description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Deck Controls -->
            <!-- Jog Wheels -->
            <control>
                <!-- Left Press Down -->
                <status>0x90</status>
                <midino>0x2F</midino>
                <group>[Channel1]</group>
                <key>DJCRMX2.wheelPress</key>
                <description>Jog Wheel L Pressed Down</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Right Press Down -->
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>DJCRMX2.wheelPress</key>
                <description>Jog Wheel R Pressed Down</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Left Spin -->
                <status>0xB0</status>
                <midino>0x30</midino>
                <group>[Channel1]</group>
                <key>DJCRMX2.wheelTurn</key>
                <description>Wheel Turning (regular)</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Right Spin -->
                <status>0xB0</status>
                <midino>0x31</midino>
                <group>[Channel2]</group>
                <key>DJCRMX2.wheelTurn</key>
                <description>Wheel Turning (regular)</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Left Scratch -->
                <status>0xB0</status>
                <midino>0x32</midino>
                <group>[Channel1]</group>
                <key>DJCRMX2.wheelTurn</key>
                <description>Wheel Turning (scratching)</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <!-- Right Scratch -->
                <status>0xB0</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>DJCRMX2.wheelTurn</key>
                <description>Wheel Turning (scratching)</description>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <!-- Jog Wheels -->
            <!-- EQ Controls -->
            <control>
                <!-- Left Knob High 1 -->
                <status>0xb0</status>
                <midino>0x3c</midino>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Left Knob Mid 1 -->
                <status>0xb0</status>
                <midino>0x3e</midino>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Left Knob Low 1 -->
                <status>0xb0</status>
                <midino>0x40</midino>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Left Kill High -->
                <status>0x90</status>
                <midino>0x28</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description>High Left Kill</description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Left Kill Mid -->
                <status>0x90</status>
                <midino>0x29</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description>Mid Left Kill</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Left Kill Low -->
                <status>0x90</status>
                <midino>0x2A</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description>Low Left Kill</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Knob High 1 -->
                <status>0xb0</status>
                <midino>0x4c</midino>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Knob Mid 1 -->
                <status>0xb0</status>
                <midino>0x4e</midino>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- Right Knob Low 1 -->
                <status>0xb0</status>
                <midino>0x50</midino>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <description>
                </description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Right Kill High -->
                <status>0x90</status>
                <midino>0x39</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description>High Right Kill</description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Right Kill Mid -->
                <status>0x90</status>
                <midino>0x3A</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description>Mid Right Kill</description>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control>
                <!-- Right Kill Low -->
                <status>0x90</status>
                <midino>0x3B</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description>Low Right Kill</description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- EQ Controls -->
            <!-- Samplers Controls [Sample Mode] 25% -->
            <control>
                <!-- S1 Play -->
                <status>0x90</status>
                <midino>0x05</midino>
                <group>[Sampler1]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S2 Play -->
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Sampler2]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S3 Play -->
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Sampler3]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S4 Play -->
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Sampler4]</group>
                <key>play</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S1 Keylock -->
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Sampler1]</group>
                <key>keylock</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S2 Keylock -->
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Sampler2]</group>
                <key>keylock</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S3 Keylock -->
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Sampler3]</group>
                <key>keylock</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S4 Keylock -->
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Sampler4]</group>
                <key>keylock</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S1 Tempo Tap [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Sampler1]</group>
                <key>bpm</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S2 Tempo Tap [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Sampler2]</group>
                <key>bpm</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S3 Tempo Tap [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Sampler3]</group>
                <key>bpm</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S4 Tempo Tap [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Sampler4]</group>
                <key>bpm</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S1 Headphones [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Sampler1]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S2 Headphones [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Sampler2]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S3 Headphones [UNIMPLEMENTED] -->
                <status>0xb0</status>
                <midino>0x7</midino>
                <group>[Sampler3]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- S4 Headphones [UNIMPLEMENTED] -->
                <status>0x80</status>
                <midino>0x30</midino>
                <group>[Sampler4]</group>
                <key>pfl</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Samplers Controls [Sample Mode] -->
            <!-- Hotcue Controls [Cue Mode] 0% -->
            <control>
                <!-- LC1 Activate -->
                <status>0x90</status>
                <midino>0x9</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC2 Activate -->
                <status>0x90</status>
                <midino>0x0A</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC3 Activate -->
                <status>0x90</status>
                <midino>0x0b</midino>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC4 Activate -->
                <status>0x90</status>
                <midino>0x0c</midino>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC1 Clear -->
                <status>0xB0</status>
                <midino>0x09</midino>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC2 Clear -->
                <status>0xB0</status>
                <midino>0x0a</midino>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC3 Clear -->
                <status>0xB0</status>
                <midino>0x0b</midino>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LC4 Clear -->
                <status>0xB0</status>
                <midino>0x0C</midino>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC1 Activate -->
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC2 Activate -->
                <status>0x90</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC3 Activate -->
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC4 Activate -->
                <status>0x90</status>
                <midino>0x1c</midino>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC1 Clear [Should be Shift+RC1, v. 2.0] -->
                <status>0xB0</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC2 Clear -->
                <status>0xB0</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC3 Clear -->
                <status>0xB0</status>
                <midino>0x1b</midino>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RC4 Clear -->
                <status>0xB0</status>
                <midino>0x1c</midino>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hotcue Controls [Cue Mode] -->
            <!-- Effects Controls [Effect Mode] 70% -->
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect4]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect4]</group>
                <key>enabled</key>
                <description></description>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x54</midino>
                <options>
                    <hercjogfast/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x55</midino>
                <options>
                    <hercjogfast/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>mix</key>
                <status>0xB0</status>
                <midino>0x5C</midino>
                <options>
                    <hercjogfast/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>mix</key>
                <status>0xB0</status>
                <midino>0x5D</midino>
                <options>
                    <hercjogfast/>
                </options>
            </control>
            <!-- Effects Controls [Effect Mode] -->
            <!-- Looping Controls [Loop Mode] 0% -->
            <control>
                <!-- RLoop 4 -->
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[Channel2]</group>
                <key>beatloop_4_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RLoop 2 -->
                <status>0x90</status>
                <midino>0x1e</midino>
                <group>[Channel2]</group>
                <key>beatloop_2_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RLoop 1 -->
                <status>0x90</status>
                <midino>0x1f</midino>
                <group>[Channel2]</group>
                <key>beatloop_1_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- RLoop 1/2 -->
                <status>0x90</status>
                <midino>0x20</midino>
                <group>[Channel2]</group>
                <key>beatloop_0.5_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LLoop 4 -->
                <status>0x90</status>
                <midino>0x0D</midino>
                <group>[Channel1]</group>
                <key>beatloop_4_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LLoop 2 -->
                <status>0x90</status>
                <midino>0x0e</midino>
                <group>[Channel1]</group>
                <key>beatloop_2_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LLoop 1 -->
                <status>0x90</status>
                <midino>0x0f</midino>
                <group>[Channel1]</group>
                <key>beatloop_1_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <!-- LLoop 1/2 -->
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>beatloop_0.5_toggle</key>
                <description>
                </description>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Looping Controls [Loop Mode] -->
            <!-- End of Controls -->
        </controls>
        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x09</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0A</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0B</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0C</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_4_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0D</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_2_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0E</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_1_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x0F</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatloop_0.5_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x10</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x19</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1A</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1B</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1C</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_4_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1D</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_2_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1E</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_1_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1F</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_0.5_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x20</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x21</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x22</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x23</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x28</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x29</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x2a</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x2E</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x32</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x34</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x39</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3a</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3b</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3F</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x49</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.3</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4A</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4B</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.7</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4C</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.9</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4D</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>peak_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4E</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4F</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.3</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x50</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x51</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.7</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x52</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <description>
                </description>
                <minimum>0.9</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x53</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>peak_indicator</key>
                <description>
                </description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x54</midino>
                <on>0x06</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect4]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x11</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect4]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x09</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x0A</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
