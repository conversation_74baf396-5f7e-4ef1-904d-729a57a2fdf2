<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="1.11.0+" schemaVersion="1">
    <info>
        <name>Korg nanoKONTROL2</name>
        <author><PERSON></author>
        <description>Controller mapping for the Korg Nanokontrol 2.  Intended to supplement a standard DJ controller setup.  Dedicated volume faders, and a
        large variety of controls for eight decks.  Gives access to a lot of controls that wouldn't normally have room to be mapped onto a standard controller.
        Created Feb 2013 for Mixxx 1.11.</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=4759</forums>
        <manual>korg_nanokontrol2</manual>
    </info>
    <controller id="NK2">
        <scriptfiles>
            <file functionprefix="NK2" filename="Korg-nanoKONTROL-2-scripts.js"/>
        </scriptfiles>
        <controls>

<!-- DEDICATED FADERS-->
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>


<!--KNOBS-->
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.knob</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!--S Buttons-->
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!--M Buttons-->
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!--R Buttons-->
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>


<!--Left Buttons-->
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x3E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NK2.button</key>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
