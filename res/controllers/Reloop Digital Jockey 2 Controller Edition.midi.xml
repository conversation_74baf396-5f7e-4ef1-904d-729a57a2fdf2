<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
	<info>
        <name>Reloop Digital Jockey 2 Controller</name>
        <author><PERSON> and <PERSON><PERSON></author>
        <description>This is a complete mapping for a Reloop Digital Jockey 2 Controller Edition or Reloop Digital Jockey 2 Interface Editiion </description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=1226</forums>
        <manual>reloop_digital_jockey_2_controller_edition</manual>
    </info>
   <controller id="DIGITAL JOCKEY">
        <scriptfiles>
            <file functionprefix="DigitalJockey2Controller" filename="Reloop-Digital-Jockey2-Controller-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x41</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.EnableHeadPhone2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x03</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x04</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3F</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.EnableHeadPhone1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.LoopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4c</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.LoopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4e</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.ReloopExit</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Beat Loops -->
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.BeatLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4D</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.BeatLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
             <!-- pitch and tempo controls -->
            <control>
                <status>0xe0</status>
                <midino>0x20</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xe1</status>
                <midino>0x10</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x50</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.HighKillChannel2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3f</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x51</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.MidKillChannel2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x40</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.BassKillChannel2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.LoopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x41</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.CuePlayButton2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.LoopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x54</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.CueButton2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x55</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.playButton2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.ReloopExit</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.Scratch2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.HighKillChannel1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.MidKillChannel1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.JogWheel2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x59</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.JogWheel2_Hold</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.BassKillChannel1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.CuePlayButton1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.CueButton1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.playButton1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.Scratch1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.JogWheel1_Hold</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.JogWheel1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xf</midino>
                <group>[Master]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Playlist]</group>
                <key>DigitalJockey2Controller.SelectNextTrack_or_prevTrack</key>
                <options>
                    <script-binding/>
                </options>
            </control>
          <control>
            <status>0x90</status>
            <midino>0x1a</midino>
            <group>[Channel1]</group>
            <key>DigitalJockey2Controller.Search1</key>
            <options>
              <script-binding/>
            </options>
          </control>
          <control>
            <status>0x90</status>
            <midino>0x56</midino>
            <group>[Channel2]</group>
            <key>DigitalJockey2Controller.Search2</key>
            <options>
              <script-binding/>
            </options>
          </control>
          <control>
            <status>0x90</status>
            <midino>0xe</midino>
            <group>[Channel1]</group>
            <key>DigitalJockey2Controller.Flanger1</key>
            <options>
              <script-binding/>
            </options>
          </control>
          <control>
            <status>0x90</status>
            <midino>0x45</midino>
            <group>[Channel2]</group>
            <key>DigitalJockey2Controller.Flanger2</key>
            <options>
              <script-binding/>
            </options>
          </control>
	  <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.LoadSelectedTrack</key>
                <options>
                    <script-binding/>
                </options>
          </control>
	  <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.LoadSelectedTrack</key>
                <options>
                    <script-binding/>
                </options>
          </control>
	  <control>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.JogWheelHelper</key>
                <options>
                    <script-binding/>
                </options>
          </control>
	  <control>
                <status>0x90</status>
                <midino>0x59</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.JogWheelHelper</key>
                <options>
                    <script-binding/>
                </options>
          </control>
	  <control>
	  	<status>0xe0</status>
	  	<midino>0x20</midino>
	  	<group>[Channel1]</group>
		<key>DigitalJockey2Controller.PitchControl</key>
		<options>
		    <script-binding/>
		</options>
	  </control>
	  <control>
		<status>0xe1</status>
		<midino>0x10</midino>
		<group>[Channel2]</group>
		<key>DigitalJockey2Controller.PitchControl</key>
		<options>
		    <script-binding/>
		</options>
          </control>
	    <!-- Key Lock channel 1 -->
	    <control>
                <status>0x90</status>
                <midino>0x02</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.KeyLock1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
	    <!-- Key Lock channel 2 -->
	    <control>
                <status>0x90</status>
                <midino>0x3E</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.KeyLock2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
		<!-- Loop +/- channel 1 -->
	    	<control>
                <status>0xb0</status>
                <midino>0x0a</midino>
                <group>[Channel1]</group>
                <key>DigitalJockey2Controller.LoopPlusMinusChannel1</key>
                <options>
                    <script-binding/>
                </options>
            </control>
		<!-- Loop +/- channel 2 -->
	    	<control>
                <status>0xb0</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>DigitalJockey2Controller.LoopPlusMinusChannel2</key>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
