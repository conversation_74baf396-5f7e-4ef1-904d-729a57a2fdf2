<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="1.10.0" schemaVersion="1">
	<info>
		<name>Numark Total Control</name>
		<author>Smashuu / <PERSON></author>
		<description>Optimized for Traktor label card</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=1252</forums>
        <manual>numark_total_control</manual>
	</info>
	<controller id="TotalTrack Control">
		<scriptfiles>
			<file filename="Numark-Total-Control-scripts.js" functionprefix="NumarkTotalControl"/>
		</scriptfiles>
		<controls>
<!-- Master and <PERSON><PERSON> controls -->
			<control>
				<group>[Master]</group>
				<key>headVolume</key>
				<status>0xb0</status>
				<midino>0x0f</midino>
			</control>
			<control>
				<group>[Master]</group>
				<key>headMix</key>
				<status>0xb0</status>
				<midino>0x16</midino>
			</control>
			<control>
				<group>[Master]</group>
				<key>volume</key>
				<status>0xb0</status>
				<midino>0x17</midino>
			</control>
			<control>
				<group>[Master]</group>
				<key>crossfader</key>
				<status>0xb0</status>
				<midino>0x0a</midino>
				<options>
					<invert/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x30</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x37</midino>
			</control>
<!-- Flanger -->
			<control>
				<group>[Channel1]</group>
				<key>flanger</key>
				<status>0x90</status>
				<midino>0x39</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>flanger</key>
				<status>0x90</status>
				<midino>0x3d</midino>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoDepth</key>
				<status>0xb0</status>
				<midino>0x00</midino>
				<options>
					<diff/>
				</options>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoDelay</key>
				<status>0xb0</status>
				<midino>0x02</midino>
				<options>
					<diff/>
				</options>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoPeriod</key>
				<status>0xb0</status>
				<midino>0x01</midino>
				<options>
					<diff/>
				</options>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoDepth</key>
				<status>0xb0</status>
				<midino>0x04</midino>
				<options>
					<diff/>
				</options>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoDelay</key>
				<status>0xb0</status>
				<midino>0x06</midino>
				<options>
					<diff/>
				</options>
			</control>
			<control>
				<group>[Flanger]</group>
				<key>lfoPeriod</key>
				<status>0xb0</status>
				<midino>0x05</midino>
				<options>
					<diff/>
				</options>
			</control>
<!-- Playlist and loading -->
			<control>
				<group>[Playlist]</group>
				<key>NumarkTotalControl.toggleDirectoryMode</key>
				<status>0x90</status>
				<midino>0x48</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Playlist]</group>
				<key>NumarkTotalControl.selectKnob</key>
				<status>0xb0</status>
				<midino>0x1a</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Playlist]</group>
				<key>LoadSelectedIntoFirstStopped</key>
				<status>0x90</status>
				<midino>0x4f</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>LoadSelectedTrack</key>
				<status>0x90</status>
				<midino>0x4b</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>LoadSelectedTrack</key>
				<status>0x90</status>
				<midino>0x34</midino>
			</control>
<!-- Playback and cueing -->
			<control>
				<group>[Channel1]</group>
				<key>play</key>
				<status>0x90</status>
				<midino>0x43</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>play</key>
				<status>0x90</status>
				<midino>0x4c</midino>
			</control>
			<!-- Keylock -->
			<control>
				<group>[Channel1]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x38</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x3f</midino>
			</control>
			<!-- Select -->
			<control>
				<group>[Master]</group>
				<key>NumarkTotalControl.toggleSimpleCue</key>
				<status>0x90</status>
				<midino>0x31</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.setCue</key>
				<status>0x90</status>
				<midino>0x3b</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.setCue</key>
				<status>0x90</status>
				<midino>0x44</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.playFromCue</key>
				<status>0x90</status>
				<midino>0x33</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.playFromCue</key>
				<status>0x90</status>
				<midino>0x3c</midino>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Jog and pitch controls -->
			<control>
				<group>[Channel1]</group>
				<key>beatsync</key>
				<status>0x90</status>
				<midino>0x40</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatsync</key>
				<status>0x90</status>
				<midino>0x47</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.leftFunction</key>
				<status>0x90</status>
				<midino>0x41</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.rightFunction</key>
				<status>0x90</status>
				<midino>0x42</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.leftFunction</key>
				<status>0x90</status>
				<midino>0x45</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.rightFunction</key>
				<status>0x90</status>
				<midino>0x46</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.finePitch</key>
				<status>0xb0</status>
				<midino>0x03</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.finePitch</key>
				<status>0xb0</status>
				<midino>0x07</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.tap</key>
				<status>0x90</status>
				<midino>0x3a</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.tap</key>
				<status>0x90</status>
				<midino>0x3e</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<!-- Filter On/Off -->
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.toggleScratchMode</key>
				<status>0x90</status>
				<midino>0x32</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.jogWheel</key>
				<status>0xb0</status>
				<midino>0x19</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.jogWheel</key>
				<status>0xb0</status>
				<midino>0x18</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>rate</key>
				<status>0xb0</status>
				<midino>0x0b</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>rate</key>
				<status>0xb0</status>
				<midino>0x0c</midino>
			</control>
<!-- Levels and Equalizer -->
			<control>
				<group>[Channel1]</group>
				<key>volume</key>
				<status>0xb0</status>
				<midino>0x08</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>volume</key>
				<status>0xb0</status>
				<midino>0x09</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterHighKill</key>
				<status>0x90</status>
				<midino>0x50</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterMidKill</key>
				<status>0x90</status>
				<midino>0x55</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterLowKill</key>
				<status>0x90</status>
				<midino>0x53</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterHighKill</key>
				<status>0x90</status>
				<midino>0x52</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterMidKill</key>
				<status>0x90</status>
				<midino>0x51</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterLowKill</key>
				<status>0x90</status>
				<midino>0x54</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>pregain</key>
				<status>0xb0</status>
				<midino>0x0d</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>pregain</key>
				<status>0xb0</status>
				<midino>0x0e</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterHigh</key>
				<status>0xb0</status>
				<midino>0x10</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterMid</key>
				<status>0xb0</status>
				<midino>0x12</midino>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>filterLow</key>
				<status>0xb0</status>
				<midino>0x14</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterHigh</key>
				<status>0xb0</status>
				<midino>0x11</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterMid</key>
				<status>0xb0</status>
				<midino>0x13</midino>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>filterLow</key>
				<status>0xb0</status>
				<midino>0x15</midino>
			</control>
<!-- Loops -->
			<!-- Select -->
			<control>
				<group>[Master]</group>
				<key>NumarkTotalControl.toggleExtendedLooping</key>
				<status>0x90</status>
				<midino>0x35</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<!-- Filter On/Off -->
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.toggleQuantize</key>
				<status>0x90</status>
				<midino>0x36</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.loopIn</key>
				<status>0x90</status>
				<midino>0x49</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>NumarkTotalControl.loopOut</key>
				<status>0x90</status>
				<midino>0x4a</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.loopIn</key>
				<status>0x90</status>
				<midino>0x4d</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>NumarkTotalControl.loopOut</key>
				<status>0x90</status>
				<midino>0x4e</midino>
				<options>
					<script-binding/>
				</options>
			</control>
		</controls>

		<outputs>
<!-- Master and PFL controls -->
			<output>
				<group>[Channel1]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x35</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x40</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
<!-- Flanger -->
			<output>
				<group>[Channel1]</group>
				<key>flanger</key>
				<status>0x90</status>
				<midino>0x32</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>flanger</key>
				<status>0x90</status>
				<midino>0x46</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
<!-- Playlist and loading -->
			<output>
				<group>[Channel1]</group>
				<key>playposition</key>
				<status>0x90</status>
				<midino>0x3f</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.75</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>playposition</key>
				<status>0x90</status>
				<midino>0x4f</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.75</minimum>
			</output>
<!-- Cuing and playback -->
			<output>
				<group>[Channel1]</group>
				<key>play</key>
				<status>0x90</status>
				<midino>0x3e</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>play</key>
				<status>0x90</status>
				<midino>0x4e</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>cue_point</key>
				<status>0x90</status>
				<midino>0x3c</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>1.0</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>cue_point</key>
				<status>0x90</status>
				<midino>0x4c</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>1.0</maximum>
				<minimum>0.0</minimum>
			</output>
			<!-- Keylock -->
			<output>
				<group>[Channel1]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x36</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x41</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
<!-- Jog and pitch controls -->
			<output>
				<group>[Channel1]</group>
				<key>rate</key>
				<status>0x90</status>
				<midino>0x34</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>0.05</maximum>
				<minimum>-0.05</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>rate</key>
				<status>0x90</status>
				<midino>0x43</midino>
				<on>0x64</on>
				<off>0x00</off>
				<maximum>0.05</maximum>
				<minimum>-0.05</minimum>
			</output>
<!-- Levels and Equalizer -->
			<!-- Sync -->
			<output>
				<group>[Channel1]</group>
				<key>beat_active</key>
				<status>0x90</status>
				<midino>0x37</midino>
				<off>0x00</off>
				<on>0x64</on>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beat_active</key>
				<status>0x90</status>
				<midino>0x42</midino>
				<off>0x00</off>
				<on>0x64</on>
				<maximum>1.0</maximum>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>filterLowKill</key>
				<status>0x90</status>
				<midino>0x52</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>filterMidKill</key>
				<status>0x90</status>
				<midino>0x51</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>filterHighKill</key>
				<status>0x90</status>
				<midino>0x50</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>filterLowKill</key>
				<status>0x90</status>
				<midino>0x55</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>filterMidKill</key>
				<status>0x90</status>
				<midino>0x54</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>filterHighKill</key>
				<status>0x90</status>
				<midino>0x53</midino>
				<on>0x00</on>
				<off>0x64</off>
				<maximum>0.1</maximum>
				<minimum>0.0</minimum>
			</output>
		</outputs>
	</controller>
</MixxxMIDIPreset>
