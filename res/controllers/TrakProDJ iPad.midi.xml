<?xml version='1.0' encoding='utf-8'?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.10.0+">
  <info>
    <name>TrakProDJ iPad</name>
    <author><PERSON>kka Tu<PERSON>ela &lt;<EMAIL>&gt;</author>
    <description>TrakProDJ iPad application midi mapping</description>
    <manual>trakprodj</manual>
  </info>
  <controller id="Network" port="">
    <scriptfiles>
      <file functionprefix="TrakProDJ" filename="TrakProDJ-iPad-scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <!--play-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--play-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TrakProDJ.LoadSelectedTrack</key>
        <status>0XB7</status>
        <midino>0X1F</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TrakProDJ.LoadSelectedTrack</key>
        <status>0XB7</status>
        <midino>0X20</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--cue_default-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X02</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--cue_default-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X02</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_1_activate-->
        <group>[Channel1]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB0</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_2_activate-->
        <group>[Channel1]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB1</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_3_activate-->
        <group>[Channel1]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB2</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_4_activate-->
        <group>[Channel1]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB3</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_1_activate-->
        <group>[Channel2]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB4</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_2_activate-->
        <group>[Channel2]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB5</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_3_activate-->
        <group>[Channel2]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB6</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--hotcue_4_activate-->
        <group>[Channel2]</group>
        <key>TrakProDJ.hotcue</key>
        <status>0XB7</status>
        <midino>0X1E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TrakProDJ.jog</key>
        <status>0XE0</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TrakProDJ.jog</key>
        <status>0XE1</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0XB0</status>
        <midino>0X10</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0XB1</status>
        <midino>0X10</midino>
      </control>
      <control>
        <!--pfl-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X11</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--pfl-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X11</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0XB0</status>
        <midino>0X06</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0XB1</status>
        <midino>0X06</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0XB0</status>
        <midino>0X0B</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0XB1</status>
        <midino>0X0B</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <!--filterHighKill-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X19</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--filterHighKill-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X19</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0XB0</status>
        <midino>0X09</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0XB1</status>
        <midino>0X09</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <!--filterLowKill-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X17</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--filterLowKill-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X17</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0XB0</status>
        <midino>0X0A</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0XB1</status>
        <midino>0X0A</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <!--filterMidKill-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X18</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--filterMidKill-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X18</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--beatsync-->
        <group>[Channel1]</group>
        <key>TrakProDJ.button</key>
        <status>0XB0</status>
        <midino>0X03</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <!--beatsync-->
        <group>[Channel2]</group>
        <key>TrakProDJ.button</key>
        <status>0XB1</status>
        <midino>0X03</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0XB0</status>
        <midino>0X05</midino>
        <options>
          <Invert/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0XB1</status>
        <midino>0X05</midino>
        <options>
          <Invert/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>TrakProDJ.browse</key>
        <status>0XE2</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>TrakProDJ.browse</key>
        <status>0XE4</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>TrakProDJ.browse</key>
        <status>0XE3</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>TrakProDJ.browse</key>
        <status>0XE5</status>
        <midino>0X00</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0XB0</status>
        <midino>0X07</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0XB0</status>
        <midino>0X1B</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headVolume</key>
        <status>0XB1</status>
        <midino>0X1B</midino>
      </control>
      <control>
        <group>[Microphone]</group>
        <key>volume</key>
        <status>0XB0</status>
        <midino>0X0C</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0XB0</status>
        <midino>0X0F</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0XB1</status>
        <midino>0X0D</midino>
        <options>
          <Normal/>
        </options>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0XB0</status>
        <midino>0X0D</midino>
        <options>
          <Normal/>
        </options>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
