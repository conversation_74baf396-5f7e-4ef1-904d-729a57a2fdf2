<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.8.0">
    <info>
        <name>Hercules DJ Control Steel</name>
        <author><PERSON><PERSON> List, from the work of <PERSON></author>
        <description>Hercules DJ Control Steel controller mapping, v1.8.1. Requires script v1.8.1</description>
    </info>
    <controller id="Hercules DJ Control Steel MIDI">
        <scriptfiles>
            <file filename="Hercules-DJ-Control-Steel-scripts.js" functionprefix="HerculesSteel"/>
        </scriptfiles>
        <controls>
<!-- Headphone Cue/Mix POTS Control -->
               <control>
                   <status>0xB0</status>
                   <midino>0x3A</midino>
                   <group>[Master]</group>
                   <key>HerculesSteel.headPhoneMix</key>
                   <options>
                       <Script-Binding/>
                   </options>
               </control>

<!-- Jog Wheels -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x2F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- Play and CuePlay -->
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x24</midino>
                <options>
                    <Button/>
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <Button />
                </options>
            </control>


<!-- Load Deck A/B -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.load</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.load</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- Stop and Reset button -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.stop</key>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.stop</key>
                <status>0xB0</status>
                <midino>0x25</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- keypad channel 1 -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad1</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad2</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad3</key>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad4</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad5</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad6</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

              <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad7</key>
                <status>0xB0</status>
                <midino>0x64</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad8</key>
                <status>0xB0</status>
                <midino>0x65</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad9</key>
                <status>0xB0</status>
                <midino>0x66</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad10</key>
                <status>0xB0</status>
                <midino>0x67</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad11</key>
                <status>0xB0</status>
                <midino>0x68</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.keypad12</key>
                <status>0xB0</status>
                <midino>0x69</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- end of keypad channel 1 -->

<!-- keypad channel 2 -->
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad1</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad2</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad3</key>
                <status>0xB0</status>
                <midino>0x1B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad4</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad5</key>
                <status>0xB0</status>
                <midino>0x1D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

             <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad6</key>
                <status>0xB0</status>
                <midino>0x1E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

              <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad7</key>
                <status>0xB0</status>
                <midino>0x6A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad8</key>
                <status>0xB0</status>
                <midino>0x6B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad9</key>
                <status>0xB0</status>
                <midino>0x6C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad10</key>
                <status>0xB0</status>
                <midino>0x6D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad11</key>
                <status>0xB0</status>
                <midino>0x6E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

               <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.keypad12</key>
                <status>0xB0</status>
                <midino>0x6F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- end of keypad channel 2-->

    <control>
                <group>[Master]</group>
                <key>HerculesSteel.crossFader</key>
                <status>0xB0</status>
                <midino>0x39</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesSteel.balance</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.previous</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.next</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.deckVolume</key>
                <status>0xB0</status>
                <midino>0x32</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.rate</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesSteel.volume</key>
                <status>0xB0</status>
                <midino>0x38</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.cueSelect</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.bass</key>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.medium</key>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.treble</key>
                <status>0xB0</status>
                <midino>0x34</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.killLow</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.killMid</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.killHigh</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.previous</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.next</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.deckVolume</key>
                <status>0xB0</status>
                <midino>0x3C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.rate</key>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.cueSelect</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.bass</key>
                <status>0xB0</status>
                <midino>0x40</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.medium</key>
                <status>0xB0</status>
                <midino>0x3F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.treble</key>
                <status>0xB0</status>
                <midino>0x3E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.killLow</key>
                <status>0xB0</status>
                <midino>0x28</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.killMid</key>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.killHigh</key>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- A/B Gain  -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.gain</key>
                <status>0xB0</status>
                <midino>0x33</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.gain</key>
                <status>0xB0</status>
                <midino>0x3D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- Sync Buttons -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.beatSync</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.beatSync</key>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesSteel.rateReset</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesSteel.rateReset</key>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesSteel.scratch</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesSteel.up</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesSteel.down</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesSteel.left</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesSteel.right</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options>
                   <Script-Binding/>
                </options>
            </control>
    </controls>
    <outputs>
        <output>
            <group>[Channel1]</group>
            <key>play</key>
            <status>0xB0</status>
            <midino>0x0B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>play</key>
            <status>0xB0</status>
            <midino>0x23</midino>
            <minimum>0.5</minimum>
        </output>
            <output>
          <group>[Channel1]</group>
          <key>cue_default</key>
          <status>0xB0</status>
          <midino>0x0C</midino>
          <minimum>0.5</minimum>
      </output>
      <output>
          <group>[Channel2]</group>
          <key>cue_default</key>
          <status>0xB0</status>
          <midino>0x24</midino>
          <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x3B</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x53</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
