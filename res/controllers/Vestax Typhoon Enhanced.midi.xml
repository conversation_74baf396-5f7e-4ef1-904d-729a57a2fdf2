<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="2.0.0+">
  <info>
    <name>Vestax Typhoon Enhanced</name>
    <author>best<PERSON><PERSON></author>
    <description><PERSON><PERSON><PERSON>'s customized controller mapping for Vestax Typhoon in Mixxx 2.0+ and script. The controller actions differ from the description on the console. Therefore it enables more controls such as hotcues, relative pitch movements, slip mode and much more. Some elements are taken from <PERSON>'s original mapping.</description>
    <forums>https://mixxx.discourse.group/t/vestax-typhoon-mapping-for-mixxx-2-0/12551/1#p14494</forums>
    <manual>vestax_typhoon</manual>
  </info>
  <controller id="Vestax Typhoon">
    <scriptfiles>
      <file filename="Vestax-Typhoon-scripts.js" functionprefix="VestaxTyphoon"/>
    </scriptfiles>
    <controls>
		<!--Effect-Controls / Songlist-Controls-->
		<control>
			<!--Dry/Wet (Fx1)-->
			<group>[EffectRack1_EffectUnit1]</group>
			<key>mix</key>
			<status>0xB2</status>
			<midino>0x51</midino>
		</control>
		<control>
			<!--Dry/Wet (Fx2)-->
			<group>[EffectRack1_EffectUnit2]</group>
			<key>mix</key>
			<status>0xB2</status>
			<midino>0x52</midino>
		</control>
		<control>
			<!--Fx1-->
			<group>[EffectRack1_EffectUnit1]</group>
			<key>enabled</key>
			<status>0x92</status>
			<midino>0x29</midino>
		</control>
		<control>
			<!--Fx2-->
			<group>[EffectRack1_EffectUnit2]</group>
			<key>enabled</key>
			<status>0x92</status>
			<midino>0x28</midino>
		</control>
		<!--Deck 1 and 2 Slip mode / second Shift button-->
		<control>
			<!--SongList-->
			<group>[Playlist]</group>
			<key>VestaxTyphoon.slipShift</key>
			<status>0x92</status>
			<midino>0x26</midino>
			<options><Script-Binding/></options>
		</control>
		<!--Transport / Hot-Cue Controls Deck 1-->
		<control>
			<!-- play / hot cue 1 -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.playh1</key>
			<status>0x90</status>
			<midino>0x32</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- cue / hot cue 2 -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.cueh2</key>
			<status>0x90</status>
			<midino>0x35</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- cup / hot cue 3 -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.cuph3</key>
			<status>0x90</status>
			<midino>0x33</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- beginning (shift+play) -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.begd1</key>
			<status>0x90</status>
			<midino>0x36</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- rewind (shift+cue) -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.rrd2</key>
			<status>0x90</status>
			<midino>0x37</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- forward (shift+cup) -->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.ffd3</key>
			<status>0x90</status>
			<midino>0x38</midino>
			<options><Script-Binding/></options>
		</control>

		<!-- Transport / Hot-Cue Controls for Deck2 -->
		<control>
			<!-- play -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.playh1</key>
			<status>0x91</status>
			<midino>0x32</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- cue -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.cueh2</key>
			<status>0x91</status>
			<midino>0x35</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- cup -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.cuph3</key>
			<status>0x91</status>
			<midino>0x33</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- beginning (shift+play) -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.begd1</key>
			<status>0x91</status>
			<midino>0x36</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- rewind (shift+cue) -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.rrd2</key>
			<status>0x91</status>
			<midino>0x37</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!-- forward (shift+cup) -->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.ffd3</key>
			<status>0x91</status>
			<midino>0x38</midino>
			<options><Script-Binding/></options>
		</control>

		<!-- filter / mode change -->
		<control>
			<group>[Channel1]</group>
			<key>VestaxTyphoon.changeMode</key>
			<status>0x90</status>
			<midino>0x24</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel2]</group>
			<key>VestaxTyphoon.changeMode</key>
			<status>0x91</status>
			<midino>0x24</midino>
			<options><Script-Binding/></options>
		</control>

		<!-- pitch controls -->
		<control>
			<group>[Channel1]</group>
			<key>VestaxTyphoon.setPitch</key>
			<status>0xB0</status>
			<midino>0x0e</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel1]</group>
			<key>VestaxTyphoon.relPitch</key>
			<status>0x90</status>
			<midino>0x22</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel1]</group>
			<key>VestaxTyphoon.relPitch</key>
			<status>0x90</status>
			<midino>0x23</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel2]</group>
			<key>VestaxTyphoon.setPitch</key>
			<status>0xB1</status>
			<midino>0x0e</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel2]</group>
			<key>VestaxTyphoon.relPitch</key>
			<status>0x91</status>
			<midino>0x22</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<group>[Channel2]</group>
			<key>VestaxTyphoon.relPitch</key>
			<status>0x91</status>
			<midino>0x23</midino>
			<options><Script-Binding/></options>
		</control>

		<!--jogwheels-->
      <control>
        <!--touch-->
        <group>[Channel1]</group>
        <key>VestaxTyphoon.wheelTouch</key>
        <status>0x90</status>
        <midino>0x2e</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!--touch-->
        <group>[Channel1]</group>
        <key>VestaxTyphoon.wheelTouch</key>
        <status>0x90</status>
        <midino>0x2f</midino>
        <options><Script-Binding/></options>
      </control>
		<control>
			<!--jog-->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.wheelTurn</key>
			<status>0xB0</status>
			<midino>0x10</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!--jog-->
			<group>[Channel1]</group>
			<key>VestaxTyphoon.wheelTurn</key>
			<status>0xB0</status>
			<midino>0x11</midino>
			<options><Script-Binding/></options>
		</control>
      <control>
        <!--touch-->
        <group>[Channel2]</group>
        <key>VestaxTyphoon.wheelTouch</key>
        <status>0x91</status>
        <midino>0x2e</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!--touch-->
        <group>[Channel2]</group>
        <key>VestaxTyphoon.wheelTouch</key>
        <status>0x91</status>
        <midino>0x2f</midino>
        <options><Script-Binding/></options>
      </control>
		<control>
			<!--jog-->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.wheelTurn</key>
			<status>0xB1</status>
			<midino>0x10</midino>
			<options><Script-Binding/></options>
		</control>
		<control>
			<!--jog-->
			<group>[Channel2]</group>
			<key>VestaxTyphoon.wheelTurn</key>
			<status>0xB1</status>
			<midino>0x11</midino>
			<options><Script-Binding/></options>
		</control>

	<!--Loop controls-->
	<control>
		<group>[Channel1]</group>
		<key>VestaxTyphoon.loops</key>
		<status>0x90</status>
		<midino>0x20</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<!-- loop- -->
		<group>[Channel1]</group>
		<key>VestaxTyphoon.loopInMinus</key>
		<status>0x90</status>
		<midino>0x21</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<!-- loop+ -->
		<group>[Channel1]</group>
		<key>VestaxTyphoon.loopOutPlus</key>
		<status>0x90</status>
		<midino>0x42</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<group>[Channel2]</group>
		<key>VestaxTyphoon.loops</key>
		<status>0x91</status>
		<midino>0x20</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<!-- loop- -->
		<group>[Channel2]</group>
		<key>VestaxTyphoon.loopInMinus</key>
		<status>0x91</status>
		<midino>0x21</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<!-- loop+ -->
		<group>[Channel2]</group>
		<key>VestaxTyphoon.loopOutPlus</key>
		<status>0x91</status>
		<midino>0x42</midino>
		<options><Script-Binding/></options>
	</control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxTyphoon.DeckLoad</key>
        <status>0x92</status>
        <midino>0x60</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxTyphoon.DeckLoad</key>
        <status>0x92</status>
        <midino>0x61</midino>
        <options><Script-Binding/></options>
      </control>
	<control>
		<group>[Channel1]</group>
		<key>VestaxTyphoon.eqLow</key>
		<status>0xB0</status>
		<midino>0x17</midino>
		<options><Script-Binding/></options>
	</control>
	<control>
		<group>[Channel1]</group>
		<key>VestaxTyphoon.eqMid</key>
		<status>0xB0</status>
		<midino>0x16</midino>
        <options><Script-Binding/></options>
	</control>
		<control>
		<group>[Channel1]</group>
		<key>VestaxTyphoon.eqHigh</key>
		<status>0xB0</status>
		<midino>0x15</midino>
		<options><Script-Binding/></options>
	</control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options/>
      </control>
	<control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <options/>
    </control>
	<control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0c</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxTyphoon.setSync</key>
        <status>0x90</status>
        <midino>0x46</midino>
        <options><Script-Binding/></options>
      </control>
	  <control>
        <group>[Channel2]</group>
        <key>VestaxTyphoon.eqLow</key>
        <status>0xB1</status>
        <midino>0x17</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxTyphoon.eqMid</key>
        <status>0xB1</status>
        <midino>0x16</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxTyphoon.eqHigh</key>
        <status>0xB1</status>
        <midino>0x15</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB1</status>
        <midino>0x14</midino>
        <options/>
      </control>
	   <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB1</status>
        <midino>0x0c</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxTyphoon.setSync</key>
        <status>0x91</status>
        <midino>0x46</midino>
        <options><Script-Binding/></options>
      </control>
	<control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x92</status>
        <midino>0x5c</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x92</status>
        <midino>0x5d</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB2</status>
        <midino>0x08</midino>
      </control>
	</controls>


    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>loop_enabled</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <!--<output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x29</midino>
        <on>0x7f</on>
        <minimum>0.2</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2a</midino>
        <on>0x7f</on>
        <minimum>0.4</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2b</midino>
        <on>0x7f</on>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2c</midino>
        <on>0x7f</on>
        <minimum>0.8</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2d</midino>
        <on>0x7f</on>
        <minimum>1.0</minimum>
      </output>-->
      <output>
        <group>[Channel2]</group>
        <key>loop_enabled</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <!--<output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x29</midino>
        <on>0x7f</on>
        <minimum>0.2</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2a</midino>
        <on>0x7f</on>
        <minimum>0.4</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2b</midino>
        <on>0x7f</on>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2c</midino>
        <on>0x7f</on>
        <minimum>0.8</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2d</midino>
        <on>0x7f</on>
        <minimum>1.0</minimum>
      </output>-->
	  <output>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x29</midino>
        <minimum>0.1</minimum>
      </output>
	  <output>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x28</midino>
        <minimum>0.1</minimum>
      </output>
	</outputs>
  </controller>
</MixxxMIDIPreset>
