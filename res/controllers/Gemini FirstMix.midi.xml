<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
    <info>
      <name>Gemini FirstMix</name>
      <author>mcsquared88</author>
      <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=3884</forums>
      <manual>gemini_firstmix</manual>
    </info>
    <controller id=" firstmix  ">
        <scriptfiles>
            <file functionprefix="firstmix" filename="Gemini-FirstMix-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x80</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>firstmix.pfl</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x4c</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x48</midino>
                <group>[Channel1]</group>
                <key>firstmix.scratch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4c</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4d</midino>
                <group>[Channel1]</group>
                <key>firstmix.wheelTouch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4e</midino>
                <group>[Channel2]</group>
                <key>firstmix.wheelTouch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xd</midino>
                <group>[Sample1]</group>
                <key>firstmix.preview</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xa</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <description></description>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>firstmix.jogWheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>firstmix.jogWheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Playlist]</group>
                <key>SelectTrackKnob</key>
                <description></description>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1c</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x35</midino>
                <group>[Channel2]</group>
                <key>firstmix.scratch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>reverse</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
