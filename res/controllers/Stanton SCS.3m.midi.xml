<?xml version="1.0" encoding="utf-8"?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
  <info>
    <name>Stanton SCS.3m</name>
    <author>sbalmer</author>
    <description>Mapping for the Stanton SCS.3m controller. Supports four decks.</description>
    <manual>stanton_scs3m</manual>
  </info>
  <controller id="SCS.3m">
    <scriptfiles>
      <file functionprefix="SCS3M" filename="Stanton-SCS3m-scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>

      <!-- pitch sliders -->
      <control><key>SCS3M.receive</key><status>0xB0</status><midino>0x00</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x51</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x51</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x52</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x52</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x53</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x53</midino><options><script-binding/></options></control>

      <control><key>SCS3M.receive</key><status>0xB0</status><midino>0x01</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x54</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x54</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x55</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x55</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x56</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x56</midino><options><script-binding/></options></control>

      <!-- volume sliders -->
      <control><key>SCS3M.receive</key><status>0xB0</status><midino>0x08</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0xB0</status><midino>0x09</midino><options><script-binding/></options></control>

      <!-- soft buttons -->
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x00</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x00</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x90</status><midino>0x01</midino><options><script-binding/></options></control>
      <control><key>SCS3M.receive</key><status>0x80</status><midino>0x01</midino><options><script-binding/></options></control>

      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x90</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0xB0</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>SCS3M.receive</key>
        <status>0x80</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
    </controls>
    <outputs/>
  </controller>
</MixxxControllerPreset>
