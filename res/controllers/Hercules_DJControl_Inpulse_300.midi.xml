<?xml version="1.0" encoding="UTF-8"?>
<MixxxMIDIPreset mixxxVersion="2.4" schemaVersion="1">
	<info>
		<name>Hercules DJControl Inpulse 300</name>
			<author>DJ Phatso for Hercules Technical Support</author>
			<description>MIDI Preset for Hercules DJControl Inpulse 300</description>
			<wiki>https://www.mixxx.org/wiki/doku.php/hercules_djcontrol_inpulse_300</wiki>
			<forums>https://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=12599</forums>
	</info>
	<controller id="DJControl Inpulse 300">
		<scriptfiles>
			<file filename="lodash.mixxx.js" />
			<file filename="midi-components-0.0.js" />
			<file functionprefix="DJCi300" filename="Hercules-DJControl-Inpulse-300-script.js" />
		</scriptfiles>
		<controls>
			<!--Note Number (NN) - Buttons/switches/Encoders -->
			<!-- NN's MIDI Channel 1 (0x90)-->

			<!--Browser section (Encoder button)-->
			<control>
				<group>[Library]</group>
				<key>MoveFocus</key>
				<description>Browser button</description>
				<status>0x90</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Assistant-->
			<control>
				<group>[AutoDJ]</group>
				<key>enabled</key>
				<description>AutoDJ On/Off</description>
				<status>0x90</status>
				<midino>0x03</midino>
				<normal/>
			</control>

			<!-- NN's MIDI Channel 2 (0x91 Deck A - Standard MODE)-->

			<!--Play-->
			<control>
				<group>[Channel1]</group>
				<key>play</key>
				<description>Play button</description>
				<status>0x91</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--CUE-->
			<control>
				<group>[Channel1]</group>
				<key>cue_default</key>
				<description>Cue button</description>
				<status>0x91</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sync-->
			<control>
				<group>[Channel1]</group>
				<key>sync_enabled</key>
				<description>Sync button</description>
				<status>0x91</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!-- PFL-->
			<control>
				<group>[Channel1]</group>
				<key>pfl</key>
				<description>PFL button</description>
				<status>0x91</status>
				<midino>0x0C</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--LOAD A-->
			<control>
				<group>[Channel1]</group>
				<key>LoadSelectedTrack</key>
				<description>LOAD A button</description>
				<status>0x91</status>
				<midino>0x0D</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--SLIP A-->
			<control>
				<group>[Channel1]</group>
				<key>slip_enabled</key>
				<description>SLIP button</description>
				<status>0x91</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Q A-->
			<control>
				<group>[Channel1]</group>
				<key>quantize</key>
				<description>Q button</description>
				<status>0x91</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beats_translate_curpos</key>
				<description>Shift + Q button</description>
				<status>0x94</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Vinyl button-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.vinylButton</key>
				<description>Vinyl Deck A</description>
				<status>0x91</status>
				<midino>0x03</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Jog Touch A-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.wheelTouch</key>
				<description>Jog Wheel Touch Deck A</description>
				<status>0x91</status>
				<midino>0x08</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Loop In/Out-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.loopInButton</key>
				<description>Loop In button</description>
				<status>0x91</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.loopOutButton</key>
				<description>Loop Out button</description>
				<status>0x91</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--FX section button-->
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<description>FX Rack 1 - Slot 3 On/Off</description>
				<status>0x91</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Mode buttons-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Hot cue mode</description>
				<status>0x91</status>
				<midino>0x0F</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Roll mode</description>
				<status>0x91</status>
				<midino>0x10</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Slicer mode</description>
				<status>0x91</status>
				<midino>0x11</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Sampler mode</description>
				<status>0x91</status>
				<midino>0x12</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Toneplay mode</description>
				<status>0x91</status>
				<midino>0x13</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>FX mode</description>
				<status>0x91</status>
				<midino>0x14</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Slicer loop mode</description>
				<status>0x91</status>
				<midino>0x15</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.changeMode</key>
				<description>Beatjump mode</description>
				<status>0x91</status>
				<midino>0x16</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!-- NN's MIDI Channel 3 (0x92 Deck B - Standard MODE)-->

			<!--Play-->
			<control>
				<group>[Channel2]</group>
				<key>play</key>
				<description>Play button</description>
				<status>0x92</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--CUE-->
			<control>
				<group>[Channel2]</group>
				<key>cue_default</key>
				<description>Cue button</description>
				<status>0x92</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sync-->
			<control>
				<group>[Channel2]</group>
				<key>sync_enabled</key>
				<description>Sync button</description>
				<status>0x92</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!-- PFL-->
			<control>
				<group>[Channel2]</group>
				<key>pfl</key>
				<description>PFL button</description>
				<status>0x92</status>
				<midino>0x0C</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--LOAD B-->
			<control>
				<group>[Channel2]</group>
				<key>LoadSelectedTrack</key>
				<description>LOAD B button</description>
				<status>0x92</status>
				<midino>0x0D</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--SLIP B-->
			<control>
				<group>[Channel2]</group>
				<key>slip_enabled</key>
				<description>SLIP button</description>
				<status>0x92</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Q B-->
			<control>
				<group>[Channel2]</group>
				<key>quantize</key>
				<description>Q button</description>
				<status>0x92</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beats_translate_curpos</key>
				<description>Shift + Q button</description>
				<status>0x95</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Vinyl button-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.vinylButton</key>
				<description>Vinyl Deck B</description>
				<status>0x92</status>
				<midino>0x03</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Jog Touch B -->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.wheelTouch</key>
				<description>Jog Wheel Touch Deck B</description>
				<status>0x92</status>
				<midino>0x08</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Loop In/Out-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.loopInButton</key>
				<description>Loop In button</description>
				<status>0x92</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.loopOutButton</key>
				<description>Loop Out button</description>
				<status>0x92</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--FX Section Button-->
			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<description>FX Rack 2 - Slot 3 On/Off</description>
				<status>0x92</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Mode buttons-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Hot cue mode</description>
				<status>0x92</status>
				<midino>0x0F</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Roll mode</description>
				<status>0x92</status>
				<midino>0x10</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Slicer mode</description>
				<status>0x92</status>
				<midino>0x11</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Sampler mode</description>
				<status>0x92</status>
				<midino>0x12</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Toneplay mode</description>
				<status>0x92</status>
				<midino>0x13</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>FX mode</description>
				<status>0x92</status>
				<midino>0x14</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Slicer loop mode</description>
				<status>0x92</status>
				<midino>0x15</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.changeMode</key>
				<description>Beatjump mode</description>
				<status>0x92</status>
				<midino>0x16</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!-- NN's MIDI Channel 4 (0x93 - SHIFT MODE)-->

			<!--Browser section (Encoder button)-->
			<control>
				<group>[Skin]</group>
				<key>show_maximized_library</key>
				<description>Browser button - Maximize Library view</description>
				<status>0x93</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- NN's MIDI Channel 5 (0x94 Deck A - SHIFT MODE)-->
			<!--Jog Touch A-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.wheelTouchShift</key>
				<description>Jog Wheel Shift Touch Deck A</description>
				<status>0x94</status>
				<midino>0x08</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Play-->
			<control>
				<group>[Channel1]</group>
				<key>play_stutter</key>
				<description>SHIFT + Play: Play Stutter</description>
				<status>0x94</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--CUE-->
			<control>
				<group>[Channel1]</group>
				<key>start_play</key>
				<description>SHIFT + Cue: REWIND to beginning</description>
				<status>0x94</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sync-->
			<control>
				<group>[Channel1]</group>
				<key>sync_leader</key>
				<description>SHIFT + Sync: Sync Master</description>
				<status>0x94</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Loop In/Out-->
			<control>
				<group>[Channel1]</group>
				<key>loop_halve</key>
				<description>SHIFT + Loop In: Loop Half</description>
				<status>0x94</status>
				<midino>0x09</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>loop_double</key>
				<description>SHIFT + Loop Out: Loop Double</description>
				<status>0x94</status>
				<midino>0x0A</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Effect-->
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>next_effect</key>
				<description>SHIFT + Effect ON: Next effect</description>
				<status>0x94</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- NN's MIDI Channel 6 (0x95 Deck B - SHIFT MODE)-->
			<!--Jog Touch B-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.wheelTouchShift</key>
				<description>Jog Wheel Shift Touch Deck B</description>
				<status>0x95</status>
				<midino>0x08</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!--Play-->
			<control>
				<group>[Channel2]</group>
				<key>play_stutter</key>
				<description>SHIFT + Play: Play Stutter</description>
				<status>0x95</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--CUE-->
			<control>
				<group>[Channel2]</group>
				<key>start_play</key>
				<description>SHIFT + Cue: REWIND to beginning</description>
				<status>0x95</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sync-->
			<control>
				<group>[Channel2]</group>
				<key>sync_leader</key>
				<description>SHIFT + Sync: Sync Master</description>
				<status>0x95</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Loop In/Out-->
			<control>
				<group>[Channel2]</group>
				<key>loop_halve</key>
				<description>SHIFT + Loop In: Loop Half</description>
				<status>0x95</status>
				<midino>0x09</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>loop_double</key>
				<description>SHIFT + Loop Out: Loop Double</description>
				<status>0x95</status>
				<midino>0x0A</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Effect-->
			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>next_effect</key>
				<description>SHIFT + Effect ON: Next effect</description>
				<status>0x95</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- NN's MIDI Channel 7 (0x96  Deck A - Pads)-->

			<!--Hot Cues (SET)-->
			<control>
				<group>[Channel1]</group>
				<key>hotcue_1_activate</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_2_activate</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_3_activate</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_4_activate</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x03</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_5_activate</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x04</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_6_activate</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_7_activate</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_8_activate</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Hot-Cue buttons (SHIFT mode)-->
			<control>
				<group>[Channel1]</group>
				<key>hotcue_1_clear</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x08</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_2_clear</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x09</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_3_clear</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x0A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_4_clear</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x0B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_5_clear</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x0C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_6_clear</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x0D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_7_clear</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x0E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>hotcue_8_clear</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x0F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--FX Pads -->
			<control>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>enabled</key>
				<description>FX Unit 1 - Slot 1 On/Off</description>
				<status>0x96</status>
				<midino>0x50</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>enabled</key>
				<description>FX Unit 1 - Slot 2 On/Off</description>
				<status>0x96</status>
				<midino>0x51</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<description>FX Unit 1 - Slot 3 On/Off</description>
				<status>0x96</status>
				<midino>0x52</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX Unit 1 On/Off - Deck A</description>
				<status>0x96</status>
				<midino>0x53</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>next_effect</key>
				<description>FX Unit 1 - Slot 1 next effect</description>
				<status>0x96</status>
				<midino>0x54</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>next_effect</key>
				<description>FX Unit 1 - Slot 2 next effect</description>
				<status>0x96</status>
				<midino>0x55</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>next_effect</key>
				<description>FX Unit 1 - Slot 3 next effect</description>
				<status>0x96</status>
				<midino>0x56</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX Unit 2 On/Off - Deck A</description>
				<status>0x96</status>
				<midino>0x57</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--FX Pads (SHIFT mode) 0x58-0x5F -->
			<control>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>enabled</key>
				<description>FX Unit 3 - Slot 1 On/Off</description>
				<status>0x96</status>
				<midino>0x58</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>enabled</key>
				<description>FX Unit 3 - Slot 2 On/Off</description>
				<status>0x96</status>
				<midino>0x59</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>enabled</key>
				<description>FX Unit 3 - Slot 3 On/Off</description>
				<status>0x96</status>
				<midino>0x5A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX Unit 3 On/Off - Deck A</description>
				<status>0x96</status>
				<midino>0x5B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>next_effect</key>
				<description>FX Unit 3 - Slot 1 next effect</description>
				<status>0x96</status>
				<midino>0x5C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>next_effect</key>
				<description>FX Unit 3 - Slot 2 next effect</description>
				<status>0x96</status>
				<midino>0x5D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>next_effect</key>
				<description>FX Unit 3 - Slot 3 next effect</description>
				<status>0x96</status>
				<midino>0x5E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX Unit 4 On/Off - Deck A</description>
				<status>0x96</status>
				<midino>0x5F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Roll-->
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_0.125_activate</key>
				<description>Loop 1/8 Beat (Pad 1)</description>
				<status>0x96</status>
				<midino>0x10</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_0.25_activate</key>
				<description>Loop 1/4 Beat (Pad 2)</description>
				<status>0x96</status>
				<midino>0x11</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_0.5_activate</key>
				<description>Loop 1/2 Beat (Pad 3)</description>
				<status>0x96</status>
				<midino>0x12</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_1_activate</key>
				<description>Loop 1 Beat (Pad 4)</description>
				<status>0x96</status>
				<midino>0x13</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_2_activate</key>
				<description>Loop 2 Beat (Pad 5)</description>
				<status>0x96</status>
				<midino>0x14</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_4_activate</key>
				<description>Loop 4 Beat (Pad 6)</description>
				<status>0x96</status>
				<midino>0x15</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_8_activate</key>
				<description>Loop 8 Beat (Pad 7)</description>
				<status>0x96</status>
				<midino>0x16</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatlooproll_16_activate</key>
				<description>Loop 16 Beat (Pad 8)</description>
				<status>0x96</status>
				<midino>0x17</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sampler-->
			<control>
				<group>[Sampler1]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x30</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler2]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x31</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler3]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x32</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler4]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x33</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler5]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x34</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler6]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x35</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler7]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x36</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler8]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x37</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sampler (SHIFT mode)-->
			<control>
				<group>[Sampler1]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 1</description>
				<status>0x96</status>
				<midino>0x38</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler2]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 2</description>
				<status>0x96</status>
				<midino>0x39</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler3]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 3</description>
				<status>0x96</status>
				<midino>0x3A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler4]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 4</description>
				<status>0x96</status>
				<midino>0x3B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler5]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 5</description>
				<status>0x96</status>
				<midino>0x3C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler6]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 6</description>
				<status>0x96</status>
				<midino>0x3D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler7]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 7</description>
				<status>0x96</status>
				<midino>0x3E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler8]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 8</description>
				<status>0x96</status>
				<midino>0x3F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Slicer-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[0].input</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x20</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[1].input</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x21</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[2].input</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x22</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[3].input</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x23</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[4].input</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x24</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[5].input</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x25</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[6].input</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x26</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[7].input</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x27</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<!--Toneplay-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x40</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x41</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x42</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x43</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x44</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x45</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x46</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x47</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 1</description>
				<status>0x96</status>
				<midino>0x48</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 2</description>
				<status>0x96</status>
				<midino>0x49</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 3</description>
				<status>0x96</status>
				<midino>0x4A</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 4</description>
				<status>0x96</status>
				<midino>0x4B</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 5</description>
				<status>0x96</status>
				<midino>0x4C</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 6</description>
				<status>0x96</status>
				<midino>0x4D</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 7</description>
				<status>0x96</status>
				<midino>0x4E</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 8</description>
				<status>0x96</status>
				<midino>0x4F</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>

			<!--Slicer LOOP-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[0].input</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x60</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[1].input</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x61</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[2].input</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x62</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[3].input</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x63</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[4].input</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x64</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[5].input</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x65</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[6].input</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x66</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.deck[0].slicerPad[7].input</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x67</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<!--Beatjump-->
			<control>
				<group>[Channel1]</group>
				<key>beatjump_1_backward</key>
				<description>PAD 1</description>
				<status>0x96</status>
				<midino>0x70</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_1_forward</key>
				<description>PAD 2</description>
				<status>0x96</status>
				<midino>0x71</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_2_backward</key>
				<description>PAD 3</description>
				<status>0x96</status>
				<midino>0x72</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_2_forward</key>
				<description>PAD 4</description>
				<status>0x96</status>
				<midino>0x73</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_4_backward</key>
				<description>PAD 5</description>
				<status>0x96</status>
				<midino>0x74</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_4_forward</key>
				<description>PAD 6</description>
				<status>0x96</status>
				<midino>0x75</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_8_backward</key>
				<description>PAD 7</description>
				<status>0x96</status>
				<midino>0x76</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_8_forward</key>
				<description>PAD 8</description>
				<status>0x96</status>
				<midino>0x77</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- NN's MIDI Channel 8 (0x97 Deck B - Pads)-->


			<!--Hot Cues (SET)-->
			<control>
				<group>[Channel2]</group>
				<key>hotcue_1_activate</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_2_activate</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_3_activate</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_4_activate</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x03</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_5_activate</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x04</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_6_activate</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_7_activate</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_8_activate</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Hot-Cue buttons (SHIFT mode)-->
			<control>
				<group>[Channel2]</group>
				<key>hotcue_1_clear</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x08</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_2_clear</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x09</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_3_clear</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x0A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_4_clear</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x0B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_5_clear</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x0C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_6_clear</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x0D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_7_clear</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x0E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>hotcue_8_clear</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x0F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--FX Pads -->
			<control>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>enabled</key>
				<description>FX Unit 2 - Slot 1 On/Off</description>
				<status>0x97</status>
				<midino>0x50</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>enabled</key>
				<description>FX Unit 2 - Slot 2 On/Off</description>
				<status>0x97</status>
				<midino>0x51</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<description>FX Unit 2 - Slot 3 On/Off</description>
				<status>0x97</status>
				<midino>0x52</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX Unit 1 On/Off - Deck B</description>
				<status>0x97</status>
				<midino>0x53</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>next_effect</key>
				<description>FX Unit 2 - Slot 1 next effect</description>
				<status>0x97</status>
				<midino>0x54</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>next_effect</key>
				<description>FX Unit 2 - Slot 2 next effect</description>
				<status>0x97</status>
				<midino>0x55</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>next_effect</key>
				<description>FX Unit 2 - Slot 3 next effect</description>
				<status>0x97</status>
				<midino>0x56</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX Unit 2 On/Off - Deck B</description>
				<status>0x97</status>
				<midino>0x57</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--FX Pads (SHIFT mode) 0x58-0x5F -->
			<control>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>enabled</key>
				<description>FX Unit 4 - Slot 1 On/Off</description>
				<status>0x97</status>
				<midino>0x58</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>enabled</key>
				<description>FX Unit 4 - Slot 2 On/Off</description>
				<status>0x97</status>
				<midino>0x59</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>enabled</key>
				<description>FX Unit 4 - Slot 3 On/Off</description>
				<status>0x97</status>
				<midino>0x5A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX Unit 3 On/Off - Deck B</description>
				<status>0x97</status>
				<midino>0x5B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>next_effect</key>
				<description>FX Unit 4 - Slot 1 next effect</description>
				<status>0x97</status>
				<midino>0x5C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>next_effect</key>
				<description>FX Unit 4 - Slot 2 next effect</description>
				<status>0x97</status>
				<midino>0x5D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>next_effect</key>
				<description>FX Unit 4 - Slot 3 next effect</description>
				<status>0x97</status>
				<midino>0x5E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX Unit 4 On/Off - Deck B</description>
				<status>0x97</status>
				<midino>0x5F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Roll-->
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_0.125_activate</key>
				<description>Loop 1/8 Beat (Pad 1)</description>
				<status>0x97</status>
				<midino>0x10</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_0.25_activate</key>
				<description>Loop 1/4 Beat (Pad 2)</description>
				<status>0x97</status>
				<midino>0x11</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_0.5_activate</key>
				<description>Loop 1/2 Beat (Pad 3)</description>
				<status>0x97</status>
				<midino>0x12</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_1_activate</key>
				<description>Loop 1 Beat (Pad 4)</description>
				<status>0x97</status>
				<midino>0x13</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_2_activate</key>
				<description>Loop 2 Beat (Pad 5)</description>
				<status>0x97</status>
				<midino>0x14</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_4_activate</key>
				<description>Loop 4 Beat (Pad 6)</description>
				<status>0x97</status>
				<midino>0x15</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_8_activate</key>
				<description>Loop 8 Beat (Pad 7)</description>
				<status>0x97</status>
				<midino>0x16</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatlooproll_16_activate</key>
				<description>Loop 16 Beat (Pad 8)</description>
				<status>0x97</status>
				<midino>0x17</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sampler-->
			<control>
				<group>[Sampler9]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x30</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler10]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x31</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler11]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x32</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler12]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x33</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler13]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x34</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler14]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x35</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler15]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x36</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler16]</group>
				<key>cue_gotoandplay</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x37</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Sampler (SHIFT mode)-->
			<control>
				<group>[Sampler9]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 1</description>
				<status>0x97</status>
				<midino>0x38</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler10]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 2</description>
				<status>0x97</status>
				<midino>0x39</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler11]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 3</description>
				<status>0x97</status>
				<midino>0x3A</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler12]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 4</description>
				<status>0x97</status>
				<midino>0x3B</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler13]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 5</description>
				<status>0x97</status>
				<midino>0x3C</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler14]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 6</description>
				<status>0x97</status>
				<midino>0x3D</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler15]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 7</description>
				<status>0x97</status>
				<midino>0x3E</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Sampler16]</group>
				<key>cue_gotoandstop</key>
				<description>SHIFT + PAD 8</description>
				<status>0x97</status>
				<midino>0x3F</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Slicer-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[0].input</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x20</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[1].input</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x21</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[2].input</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x22</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[3].input</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x23</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[4].input</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x24</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[5].input</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x25</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[6].input</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x26</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[7].input</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x27</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<!--Toneplay-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x40</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x41</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x42</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x43</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x44</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x45</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x46</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x47</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 1</description>
				<status>0x97</status>
				<midino>0x48</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 2</description>
				<status>0x97</status>
				<midino>0x49</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 3</description>
				<status>0x97</status>
				<midino>0x4A</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 4</description>
				<status>0x97</status>
				<midino>0x4B</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 5</description>
				<status>0x97</status>
				<midino>0x4C</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 6</description>
				<status>0x97</status>
				<midino>0x4D</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 7</description>
				<status>0x97</status>
				<midino>0x4E</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.toneplay</key>
				<description>SHIFT + PAD 8</description>
				<status>0x97</status>
				<midino>0x4F</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>

			<!--Slicer LOOP-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[0].input</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x60</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[1].input</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x61</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[2].input</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x62</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[3].input</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x63</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[4].input</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x64</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[5].input</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x65</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[6].input</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x66</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.deck[1].slicerPad[7].input</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x67</midino>
				<options>
					<Script-Binding/>
				</options>
			</control>
			<!--Beatjump-->
			<control>
				<group>[Channel2]</group>
				<key>beatjump_1_backward</key>
				<description>PAD 1</description>
				<status>0x97</status>
				<midino>0x70</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_1_forward</key>
				<description>PAD 2</description>
				<status>0x97</status>
				<midino>0x71</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_2_backward</key>
				<description>PAD 3</description>
				<status>0x97</status>
				<midino>0x72</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_2_forward</key>
				<description>PAD 4</description>
				<status>0x97</status>
				<midino>0x73</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_4_backward</key>
				<description>PAD 5</description>
				<status>0x97</status>
				<midino>0x74</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_4_forward</key>
				<description>PAD 6</description>
				<status>0x97</status>
				<midino>0x75</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_8_backward</key>
				<description>PAD 7</description>
				<status>0x97</status>
				<midino>0x76</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_8_forward</key>
				<description>PAD 8</description>
				<status>0x97</status>
				<midino>0x77</midino>
				<options>
					<normal />
				</options>
			</control>

			<!--Continuous Controllers (CC) - Faders/knobs -->
			<!-- CC's MIDI Channel 1 (0xB0 Standard mode)-->

			<!--Crossfader-->
			<control>
				<group>[Master]</group>
				<key>crossfader</key>
				<description>Crossfader</description>
				<status>0xB0</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Browser encoder-->
			<control>
				<group>[Library]</group>
				<key>MoveVertical</key>
				<description>Move Vertical (Browser Knob)</description>
				<status>0xB0</status>
				<midino>0x01</midino>
				<options>
					<selectknob />
				</options>
			</control>

			<!-- CC's MIDI Channel 4 (0xB3 Browser encoder SHIFT mode either one)-->
			<!--Browser encoder-->
			<control>
				<group>[Library]</group>
				<key>MoveHorizontal</key>
				<description>Move Horizontal (Browser Knob)</description>
				<status>0xB3</status>
				<midino>0x01</midino>
				<options>
					<selectknob />
				</options>
			</control>

			<!-- CC's MIDI Channel 2 (0xB1 Deck A - Standard mode)-->

			<!-- Volume-->
			<control>
				<group>[Channel1]</group>
				<key>volume</key>
				<description>Volume Deck A</description>
				<status>0xB1</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--EQ-->
			<control>
				<group>[EqualizerRack1_[Channel1]_Effect1]</group>
				<key>parameter1</key>
				<description>EQ LOW Deck A</description>
				<status>0xB1</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel1]_Effect1]</group>
				<key>parameter2</key>
				<description>EQ MID Deck A</description>
				<status>0xB1</status>
				<midino>0x03</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel1]_Effect1]</group>
				<key>parameter3</key>
				<description>EQ HIGH Deck A</description>
				<status>0xB1</status>
				<midino>0x04</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Gain-->
			<control>
				<group>[Channel1]</group>
				<key>pregain</key>
				<description>Gain Deck A</description>
				<status>0xB1</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Filter-->
			<control>
				<group>[QuickEffectRack1_[Channel1]]</group>
				<key>super1</key>
				<description>Filter Deck A</description>
				<status>0xB1</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Pitch sliders-->
			<control>
				<group>[Channel1]</group>
				<key>rate</key>
				<status>0xB1</status>
				<midino>0x08</midino>
				<options>
					<fourteen-bit-msb />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>rate</key>
				<status>0xB1</status>
				<midino>0x28</midino>
				<options>
					<fourteen-bit-lsb />
				</options>
			</control>
			<!--Jog wheel	-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.jogWheel</key>
				<description>Scratch Deck A (Jog-Wheel)</description>
				<status>0xB1</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.jogWheel</key>
				<description>Pitch Bend Deck A (Jog-Wheel)</description>
				<status>0xB1</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<!--FX section-->
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>meta</key>
				<description>Effect Rack 1 - Slot 3 Level</description>
				<status>0xB1</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>mix</key>
				<description>Effect Rack 1 - Dry/Wet Level</description>
				<status>0xB1</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- CC's MIDI Channel 3 (0xB2  Deck B - Standard mode)-->

			<!-- Volume-->
			<control>
				<group>[Channel2]</group>
				<key>volume</key>
				<description>Volume Deck A</description>
				<status>0xB2</status>
				<midino>0x00</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--EQ-->
			<control>
				<group>[EqualizerRack1_[Channel2]_Effect1]</group>
				<key>parameter1</key>
				<description>EQ LOW Deck A</description>
				<status>0xB2</status>
				<midino>0x02</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel2]_Effect1]</group>
				<key>parameter2</key>
				<description>EQ MID Deck A</description>
				<status>0xB2</status>
				<midino>0x03</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel2]_Effect1]</group>
				<key>parameter3</key>
				<description>EQ HIGH Deck A</description>
				<status>0xB2</status>
				<midino>0x04</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Gain-->
			<control>
				<group>[Channel2]</group>
				<key>pregain</key>
				<description>Gain Deck A</description>
				<status>0xB2</status>
				<midino>0x05</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Filter-->
			<control>
				<group>[QuickEffectRack1_[Channel2]]</group>
				<key>super1</key>
				<description>Filter Deck A</description>
				<status>0xB2</status>
				<midino>0x01</midino>
				<options>
					<normal />
				</options>
			</control>
			<!--Pitch sliders-->
			<control>
				<group>[Channel2]</group>
				<key>rate</key>
				<status>0xB2</status>
				<midino>0x08</midino>
				<options>
					<fourteen-bit-msb />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>rate</key>
				<status>0xB2</status>
				<midino>0x28</midino>
				<options>
					<fourteen-bit-lsb />
				</options>
			</control>
			<!--Jog wheel-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.jogWheel</key>
				<description>Scratch Deck B (Jog-Wheel)</description>
				<status>0xB2</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.jogWheel</key>
				<description>Pitch Bend Deck B (Jog-Wheel)</description>
				<status>0xB2</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<!--FX section-->

			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>meta</key>
				<description>Effect Rack 2 - Slot 3 Level</description>
				<status>0xB2</status>
				<midino>0x06</midino>
				<options>
					<normal />
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>mix</key>
				<description>Effect Rack 2 - Dry/Wet Level</description>
				<status>0xB2</status>
				<midino>0x07</midino>
				<options>
					<normal />
				</options>
			</control>

			<!-- CC's MIDI Channel 5 (0xB4  Deck A - SHIFT mode)-->
			<!--Jog wheel-->
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.jogWheel</key>
				<description>SHIFT + Bend Deck A (Jog-Wheel)</description>
				<status>0xB4</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>DJCi300.jogWheel</key>
				<description>SHIFT + Scratch Deck A (Jog-Wheel)</description>
				<status>0xB4</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>

			<!-- CC's MIDI Channel 6 (0xB5  Deck B - SHIFT mode)-->
			<!--Jog wheel-->
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.jogWheel</key>
				<description>SHIFT + Bend Deck B (Jog-Wheel)</description>
				<status>0xB5</status>
				<midino>0x09</midino>
				<options>
					<script-binding />
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>DJCi300.jogWheel</key>
				<description>SHIFT + Scratch Deck B (Jog-Wheel)</description>
				<status>0xB5</status>
				<midino>0x0A</midino>
				<options>
					<script-binding />
				</options>
			</control>
		</controls>

		<!--LED Outputs -->
		<outputs>
			<!--LED Transport-->
			<output>
				<group>[Channel1]</group>
				<key>play_indicator</key>
				<description>PLAY LED Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x07</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>cue_indicator</key>
				<description>CUE LED Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x06</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>start_play</key>
				<description>CUE LED Deck A(SHIFT MODE)</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x94</status>
				<midino>0x06</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>sync_enabled</key>
				<description>SYNC LED Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x05</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>sync_leader</key>
				<description>SYNC LED Deck A(SHIFT mode)</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x93</status>
				<midino>0x05</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>slip_enabled</key>
				<description>Slip button Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x01</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>quantize</key>
				<description>Q button Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x02</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>play_indicator</key>
				<description>PLAY LED Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x07</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>cue_indicator</key>
				<description>CUE LED Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x06</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>sync_enabled</key>
				<description>SYNC LED Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x05</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>sync_leader</key>
				<description>SYNC LED Deck B(SHIFT mode)</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x94</status>
				<midino>0x05</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>slip_enabled</key>
				<description>Slip button Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x01</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>quantize</key>
				<description>Q button Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x02</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<!-- LED LOOP-->
			<output>
				<group>[Channel1]</group>
				<key>beatloop_4_enabled</key>
				<description>Loop In LED DA</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x09</midino>
				<on>0x7f</on>
				<off>0x00</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatloop_4_enabled</key>
				<description>Loop In LED DB</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x09</midino>
				<on>0x7f</on>
				<off>0x00</off>
			</output>
			<!-- LED PFL buttons-->
			<output>
				<group>[Channel1]</group>
				<key>pfl</key>
				<description>PFL LED DECK A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x0C</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>pfl</key>
				<description>PFL LED DECK B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x0C</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<!-- LED Browser button-->
			<output>
				<group>[Library]</group>
				<key>MoveFocus</key>
				<description>Browser LED (BLUE)</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x90</status>
				<midino>0x04</midino>
				<on>0x05</on>
				<off>0x07</off>
			</output>
			<output>
				<group>[Skin]</group>
				<key>show_maximized_library</key>
				<description>Browser LED (GREEN)</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x90</status>
				<midino>0x04</midino>
				<on>0x07</on>
				<off>0x05</off>
			</output>
			<!-- LED Assistant button-->
			<output>
				<group>[AutoDJ]</group>
				<key>enabled</key>
				<description>Auto DJ On</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x90</status>
				<midino>0x03</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<!-- Effect Unit On-->
			<output>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<description>Effect On/Off button</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x91</status>
				<midino>0x00</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<description>Effect On/Off button</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x92</status>
				<midino>0x00</midino>
				<on>0x7f</on>
				<off>0x0</off>
			</output>
			<!--LED HOT CUE (Normal Mode)-->
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<description>Hotcue 1 (Pad 1)</description>
				<status>0x96</status>
				<midino>0x00</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<description>Hotcue 2 (Pad 2)</description>
				<status>0x96</status>
				<midino>0x01</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<description>Hotcue 3 (Pad 3)</description>
				<status>0x96</status>
				<midino>0x02</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<description>Hotcue 4 (Pad 4)</description>
				<status>0x96</status>
				<midino>0x03</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_5_status</key>
				<description>Hotcue 5 (Pad 5)</description>
				<status>0x96</status>
				<midino>0x04</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_6_status</key>
				<description>Hotcue 6 (Pad 6)</description>
				<status>0x96</status>
				<midino>0x05</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_7_status</key>
				<description>Hotcue 7 (Pad 7)</description>
				<status>0x96</status>
				<midino>0x06</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_8_status</key>
				<description>Hotcue 8 (Pad 8)</description>
				<status>0x96</status>
				<midino>0x07</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<description>Hotcue 1 (Pad 1)</description>
				<status>0x97</status>
				<midino>0x00</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<description>Hotcue 2 (Pad 2)</description>
				<status>0x97</status>
				<midino>0x01</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<description>Hotcue 3 (Pad 3)</description>
				<status>0x97</status>
				<midino>0x02</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<description>Hotcue 4 (Pad 4)</description>
				<status>0x97</status>
				<midino>0x03</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_5_status</key>
				<description>Hotcue 5 (Pad 5)</description>
				<status>0x97</status>
				<midino>0x04</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_6_status</key>
				<description>Hotcue 6 (Pad 6)</description>
				<status>0x97</status>
				<midino>0x05</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_7_status</key>
				<description>Hotcue 7 (Pad 7)</description>
				<status>0x97</status>
				<midino>0x06</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_8_status</key>
				<description>Hotcue 8 (Pad 8)</description>
				<status>0x97</status>
				<midino>0x07</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<!--LED HOT CUE (SHIFT Mode)-->
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_status</key>
				<description>Hotcue 1 (Pad 1)</description>
				<status>0x96</status>
				<midino>0x08</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_status</key>
				<description>Hotcue 2 (Pad 2)</description>
				<status>0x96</status>
				<midino>0x09</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_status</key>
				<description>Hotcue 3 (Pad 3)</description>
				<status>0x96</status>
				<midino>0x0A</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_4_status</key>
				<description>Hotcue 4 (Pad 4)</description>
				<status>0x96</status>
				<midino>0x0B</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_5_status</key>
				<description>Hotcue 5 (Pad 5)</description>
				<status>0x96</status>
				<midino>0x0C</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_6_status</key>
				<description>Hotcue 6 (Pad 6)</description>
				<status>0x96</status>
				<midino>0x0D</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_7_status</key>
				<description>Hotcue 7 (Pad 7)</description>
				<status>0x96</status>
				<midino>0x0E</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_8_status</key>
				<description>Hotcue 8 (Pad 8)</description>
				<status>0x96</status>
				<midino>0x0F</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_status</key>
				<description>Hotcue 1 (Pad 1)</description>
				<status>0x97</status>
				<midino>0x08</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_status</key>
				<description>Hotcue 2 (Pad 2)</description>
				<status>0x97</status>
				<midino>0x09</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_status</key>
				<description>Hotcue 3 (Pad 3)</description>
				<status>0x97</status>
				<midino>0x0A</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_4_status</key>
				<description>Hotcue 4 (Pad 4)</description>
				<status>0x97</status>
				<midino>0x0B</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_5_status</key>
				<description>Hotcue 5 (Pad 5)</description>
				<status>0x97</status>
				<midino>0x0C</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_6_status</key>
				<description>Hotcue 6 (Pad 6)</description>
				<status>0x97</status>
				<midino>0x0D</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_7_status</key>
				<description>Hotcue 7 (Pad 7)</description>
				<status>0x97</status>
				<midino>0x0E</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_8_status</key>
				<description>Hotcue 8 (Pad 8)</description>
				<status>0x97</status>
				<midino>0x0F</midino>
				<on>0x7E</on>
				<minimum>0.5</minimum>
				<maximum>2</maximum>
			</output>
			<!--LED Roll-->
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_0.125_activate</key>
				<description>Loop 1/8 Beat (Pad 1)</description>
				<status>0x96</status>
				<midino>0x10</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_0.25_activate</key>
				<description>Loop 1/4 Beat (Pad 2)</description>
				<status>0x96</status>
				<midino>0x11</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_0.5_activate</key>
				<description>Loop 1/2 Beat (Pad 3)</description>
				<status>0x96</status>
				<midino>0x12</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_1_activate</key>
				<description>Loop 1 Beat (Pad 4)</description>
				<status>0x96</status>
				<midino>0x13</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_2_activate</key>
				<description>Loop 2 Beat (Pad 5)</description>
				<status>0x96</status>
				<midino>0x14</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_4_activate</key>
				<description>Loop 4 Beat (Pad 6)</description>
				<status>0x96</status>
				<midino>0x15</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_8_activate</key>
				<description>Loop 8 Beat (Pad 7)</description>
				<status>0x96</status>
				<midino>0x16</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatlooproll_16_activate</key>
				<description>Loop 16 Beat (Pad 8)</description>
				<status>0x96</status>
				<midino>0x17</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_0.125_activate</key>
				<description>Loop 1/8 Beat (Pad 1)</description>
				<status>0x97</status>
				<midino>0x10</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_0.25_activate</key>
				<description>Loop 1/4 Beat (Pad 2)</description>
				<status>0x97</status>
				<midino>0x11</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_0.5_activate</key>
				<description>Loop 1/2 Beat (Pad 3)</description>
				<status>0x97</status>
				<midino>0x12</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_1_activate</key>
				<description>Loop 1 Beat (Pad 4)</description>
				<status>0x97</status>
				<midino>0x13</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_2_activate</key>
				<description>Loop 2 Beat (Pad 5)</description>
				<status>0x97</status>
				<midino>0x14</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_4_activate</key>
				<description>Loop 4 Beat (Pad 6)</description>
				<status>0x97</status>
				<midino>0x15</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_8_activate</key>
				<description>Loop 8 Beat (Pad 7)</description>
				<status>0x97</status>
				<midino>0x16</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatlooproll_16_activate</key>
				<description>Loop 16 Beat (Pad 8)</description>
				<status>0x97</status>
				<midino>0x17</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<!--LED FX Channel1 -->
			<output>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>enabled</key>
				<description>FX1 Effect 1 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x50</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>enabled</key>
				<description>FX1 Effect 2 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x51</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<description>FX1 Effect 3 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x52</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX1 is active on Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x53</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>next_effect</key>
				<description>FX1 Effect 1 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x54</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>next_effect</key>
				<description>FX1 Effect 2 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x55</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>next_effect</key>
				<description>FX1 Effect 1 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x56</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX2 is active on Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x57</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>enabled</key>
				<description>FX3 Effect 1 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x58</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>enabled</key>
				<description>FX3 Effect 2 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x59</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>enabled</key>
				<description>FX3 Effect 3 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5A</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX3 is active on Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5B</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>next_effect</key>
				<description>FX3 Effect 1 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5C</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>next_effect</key>
				<description>FX3 Effect 2 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5D</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>next_effect</key>
				<description>FX3 Effect 3 next_effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5E</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>group_[Channel1]_enable</key>
				<description>FX4 is active on Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x96</status>
				<midino>0x5F</midino>
				<on>0x7f</on>
			</output>
			<!--LED FX Channel2 -->
			<output>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>enabled</key>
				<description>FX2 Effect 1 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x50</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>enabled</key>
				<description>FX2 Effect 2 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x51</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<description>FX2 Effect 3 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x52</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX1 is active on Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x53</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>next_effect</key>
				<description>FX2 Effect 1 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x54</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>next_effect</key>
				<description>FX2 Effect 2 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x55</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>next_effect</key>
				<description>FX2 Effect 3 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x56</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX2 is active on Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x57</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>enabled</key>
				<description>FX4 Effect 1 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x58</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>enabled</key>
				<description>FX4 Effect 2 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x59</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>enabled</key>
				<description>FX4 Effect 3 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5A</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX3 is active on Deck A</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5B</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>next_effect</key>
				<description>FX4 Effect 1 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5C</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>next_effect</key>
				<description>FX4 Effect 2 activate</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5D</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>next_effect</key>
				<description>FX4 Effect 3 next effect</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5E</midino>
				<on>0x7f</on>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>group_[Channel2]_enable</key>
				<description>FX4 is active on Deck B</description>
				<minimum>0.5</minimum>
				<maximum>1</maximum>
				<status>0x97</status>
				<midino>0x5F</midino>
				<on>0x7f</on>
			</output>
			<!--LED SAMPLE-->
			<output>
				<group>[Sampler1]</group>
				<key>play_indicator</key>
				<description>(Pad 1 DECK A)</description>
				<status>0x96</status>
				<midino>0x30</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler9]</group>
				<key>play_indicator</key>
				<description>(Pad 1 DECK B)</description>
				<status>0x97</status>
				<midino>0x30</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler2]</group>
				<key>play_indicator</key>
				<description>(Pad 2 DECK A)</description>
				<status>0x96</status>
				<midino>0x31</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler10]</group>
				<key>play_indicator</key>
				<description>(Pad 2 DECK B)</description>
				<status>0x97</status>
				<midino>0x31</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler3]</group>
				<key>play_indicator</key>
				<description>(Pad 3 DECK A)</description>
				<status>0x96</status>
				<midino>0x32</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler11]</group>
				<key>play_indicator</key>
				<description>(Pad 3 DECK B)</description>
				<status>0x97</status>
				<midino>0x32</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler4]</group>
				<key>play_indicator</key>
				<description>(Pad 4 DECK A)</description>
				<status>0x96</status>
				<midino>0x33</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler12]</group>
				<key>play_indicator</key>
				<description>(Pad 4 DECK B)</description>
				<status>0x97</status>
				<midino>0x33</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler5]</group>
				<key>play_indicator</key>
				<description>(Pad 5 DECK A)</description>
				<status>0x96</status>
				<midino>0x34</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler13]</group>
				<key>play_indicator</key>
				<description>(Pad 5 DECK B)</description>
				<status>0x97</status>
				<midino>0x34</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler6]</group>
				<key>play_indicator</key>
				<description>(Pad 6 DECK A)</description>
				<status>0x96</status>
				<midino>0x35</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler14]</group>
				<key>play_indicator</key>
				<description>(Pad 6 DECK B)</description>
				<status>0x97</status>
				<midino>0x35</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler7]</group>
				<key>play_indicator</key>
				<description>(Pad 7 DECK A)</description>
				<status>0x96</status>
				<midino>0x36</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler15]</group>
				<key>play_indicator</key>
				<description>(Pad 7 DECK B)</description>
				<status>0x97</status>
				<midino>0x36</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler8]</group>
				<key>play_indicator</key>
				<description>(Pad 8 DECK A)</description>
				<status>0x96</status>
				<midino>0x37</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler16]</group>
				<key>play_indicator</key>
				<description>(Pad 4 DECK B)</description>
				<status>0x97</status>
				<midino>0x37</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
						<output>
				<group>[Sampler1]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 1 DECK A)</description>
				<status>0x96</status>
				<midino>0x38</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler9]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 1 DECK B)</description>
				<status>0x97</status>
				<midino>0x38</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler2]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 2 DECK A)</description>
				<status>0x96</status>
				<midino>0x39</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler10]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 2 DECK B)</description>
				<status>0x97</status>
				<midino>0x39</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler3]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 3 DECK A)</description>
				<status>0x96</status>
				<midino>0x3A</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler11]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 3 DECK B)</description>
				<status>0x97</status>
				<midino>0x3A</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler4]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 4 DECK A)</description>
				<status>0x96</status>
				<midino>0x3B</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler12]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 4 DECK B)</description>
				<status>0x97</status>
				<midino>0x3B</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler5]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 5 DECK A)</description>
				<status>0x96</status>
				<midino>0x3C</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler13]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 5 DECK B)</description>
				<status>0x97</status>
				<midino>0x3C</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler6]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 6 DECK A)</description>
				<status>0x96</status>
				<midino>0x3D</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler14]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 6 DECK B)</description>
				<status>0x97</status>
				<midino>0x3D</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler7]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 7 DECK A)</description>
				<status>0x96</status>
				<midino>0x3E</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler15]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 7 DECK B)</description>
				<status>0x97</status>
				<midino>0x3E</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler8]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 8 DECK A)</description>
				<status>0x96</status>
				<midino>0x3F</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Sampler16]</group>
				<key>play_indicator</key>
				<description>(SHIFT + Pad 4 DECK B)</description>
				<status>0x97</status>
				<midino>0x3F</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<!--LED BEATJUMP-->
			<output>
				<group>[Channel1]</group>
				<key>beatjump_1_backward</key>
				<description>(Beatjump 1 backward DECK A)</description>
				<status>0x96</status>
				<midino>0x70</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_1_backward</key>
				<description>(Beatjump 1 backward DECK B)</description>
				<status>0x97</status>
				<midino>0x70</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_1_forward</key>
				<description>(Beatjump 1 forward DECK A)</description>
				<status>0x96</status>
				<midino>0x71</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_1_forward</key>
				<description>(Beatjump 1 forward DECK B)</description>
				<status>0x97</status>
				<midino>0x71</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_2_backward</key>
				<description>(Beatjump 2 backward DECK A)</description>
				<status>0x96</status>
				<midino>0x72</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_2_backward</key>
				<description>(Beatjump 2 backward DECK B)</description>
				<status>0x97</status>
				<midino>0x72</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_2_forward</key>
				<description>(Beatjump 2 forward DECK A)</description>
				<status>0x96</status>
				<midino>0x73</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_2_forward</key>
				<description>(Beatjump 2 forward DECK B)</description>
				<status>0x97</status>
				<midino>0x73</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_4_backward</key>
				<description>(Beatjump 4 backward DECK A)</description>
				<status>0x96</status>
				<midino>0x74</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_4_backward</key>
				<description>(Beatjump 4 backward DECK B)</description>
				<status>0x97</status>
				<midino>0x74</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_4_forward</key>
				<description>(Beatjump 4 forward DECK A)</description>
				<status>0x96</status>
				<midino>0x75</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_4_forward</key>
				<description>(Beatjump 4 forward DECK B)</description>
				<status>0x97</status>
				<midino>0x75</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_8_backward</key>
				<description>(Beatjump 8 backward DECK A)</description>
				<status>0x96</status>
				<midino>0x76</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_8_backward</key>
				<description>(Beatjump 8 backward DECK B)</description>
				<status>0x97</status>
				<midino>0x76</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>beatjump_8_forward</key>
				<description>(Beatjump 8 forward DECK A)</description>
				<status>0x96</status>
				<midino>0x77</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>beatjump_8_forward</key>
				<description>(Beatjump 8 forward Deck B)</description>
				<status>0x97</status>
				<midino>0x77</midino>
				<on>0x7F</on>
				<minimum>0.5</minimum>
			</output>
		</outputs>
	</controller>
</MixxxMIDIPreset>
