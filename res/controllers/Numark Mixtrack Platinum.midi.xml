<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Numark Mixtrack Platinum</name>
        <author><PERSON></author>
        <description>Numark Mixtrack Platinum mapping with support for LCD screens and 4 deck playback.</description>
        <forums>https://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=8863</forums>
        <manual>numark_mixtrack_platinum</manual>
    </info>
    <controller id="MixTrack">
        <scriptfiles>
            <file filename="lodash.mixxx.js"/>
            <file filename="midi-components-0.0.js"/>
            <file functionprefix="MixtrackPlatinum" filename="Numark-Mixtrack-Platinum-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pitch.inputLSB</key>
                <description>pitch fader lsb</description>
                <status>0xB0</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pitch.inputLSB</key>
                <description>pitch fader lsb</description>
                <status>0xB1</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pitch.inputLSB</key>
                <description>pitch fader lsb</description>
                <status>0xB2</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pitch.inputLSB</key>
                <description>pitch fader lsb</description>
                <status>0xB3</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.elapsedToggle</key>
                <description>shift-wheel press to toggle time elapsed/remaining</description>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.elapsedToggle</key>
                <description>shift-wheel press to toggle time elapsed/remaining</description>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.elapsedToggle</key>
                <description>shift-wheel press to toggle time elapsed/remaining</description>
                <status>0x92</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.elapsedToggle</key>
                <description>shift-wheel press to toggle time elapsed/remaining</description>
                <status>0x93</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].manloop.loop_out.input</key>
                <status>0x94</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].manloop.loop_out.input</key>
                <status>0x95</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].manloop.loop_in.input</key>
                <status>0x94</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].manloop.loop_out.input</key>
                <status>0x96</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].manloop.loop_in.input</key>
                <status>0x95</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].manloop.loop_out.input</key>
                <status>0x97</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].manloop.loop_in.input</key>
                <status>0x96</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].manloop.loop_in.input</key>
                <status>0x97</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].manloop.loop_double.input</key>
                <status>0x94</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].manloop.loop_double.input</key>
                <status>0x95</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].manloop.loop_halve.input</key>
                <status>0x94</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].manloop.loop_double.input</key>
                <status>0x96</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].manloop.loop_halve.input</key>
                <status>0x95</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].manloop.loop_double.input</key>
                <status>0x97</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].manloop.loop_halve.input</key>
                <status>0x96</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].manloop.loop_toggle.input</key>
                <status>0x94</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].manloop.loop_halve.input</key>
                <status>0x97</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].manloop.loop_toggle.input</key>
                <status>0x95</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].manloop.loop_toggle.input</key>
                <status>0x96</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].manloop.loop_toggle.input</key>
                <status>0x97</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Shift]</group>
                <key>MixtrackPlatinum.shiftToggle</key>
                <description>The shift key.</description>
                <status>0x9F</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[4].input</key>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[4].input</key>
                <status>0x95</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[3].input</key>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[4].input</key>
                <status>0x96</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[3].input</key>
                <status>0x95</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[2].input</key>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>MixtrackPlatinum.sampler[4].input</key>
                <status>0x9F</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[4].input</key>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[3].input</key>
                <status>0x96</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[2].input</key>
                <status>0x95</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[1].input</key>
                <status>0x94</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>MixtrackPlatinum.sampler[3].input</key>
                <status>0x9F</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[3].input</key>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[2].input</key>
                <status>0x96</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[1].input</key>
                <status>0x95</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.roll4.input</key>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pfl_button.input</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>MixtrackPlatinum.sampler[2].input</key>
                <status>0x9F</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[2].input</key>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[1].input</key>
                <status>0x96</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.roll4.input</key>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.roll3.input</key>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pfl_button.input</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>MixtrackPlatinum.sampler[1].input</key>
                <status>0x9F</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[1].input</key>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.roll4.input</key>
                <status>0x96</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.roll3.input</key>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.roll2.input</key>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pfl_button.input</key>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is disabled.</description>
                <status>0x80</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.roll4.input</key>
                <status>0x97</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.roll3.input</key>
                <status>0x96</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.roll2.input</key>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.roll1.input</key>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pfl_button.input</key>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is disabled.</description>
                <status>0x81</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.roll3.input</key>
                <status>0x97</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.roll2.input</key>
                <status>0x96</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.roll1.input</key>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[4].input</key>
                <status>0x94</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is disabled.</description>
                <status>0x82</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.roll2.input</key>
                <status>0x97</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.roll1.input</key>
                <status>0x96</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[4].input</key>
                <status>0x95</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[3].input</key>
                <status>0x94</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>MixtrackPlatinum.sampler[4].input</key>
                <status>0x9F</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is disabled.</description>
                <status>0x83</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.roll1.input</key>
                <status>0x97</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[4].input</key>
                <status>0x96</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[3].input</key>
                <status>0x95</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[2].input</key>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].hotcues[1].input</key>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>MixtrackPlatinum.sampler[3].input</key>
                <status>0x9F</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[4].input</key>
                <status>0x97</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[3].input</key>
                <status>0x96</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[2].input</key>
                <status>0x95</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[2].input</key>
                <status>0x96</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].hotcues[1].input</key>
                <status>0x95</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.auto4.input</key>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>MixtrackPlatinum.sampler[2].input</key>
                <status>0x9F</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[3].input</key>
                <status>0x97</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[2].input</key>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].hotcues[1].input</key>
                <status>0x96</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.auto4.input</key>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.auto3.input</key>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>MixtrackPlatinum.sampler[1].input</key>
                <status>0x9F</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].hotcues[1].input</key>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.auto4.input</key>
                <status>0x96</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.auto3.input</key>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.auto2.input</key>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.auto4.input</key>
                <status>0x97</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.auto3.input</key>
                <status>0x96</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.auto2.input</key>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].autoloop.auto1.input</key>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].load.input</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.auto3.input</key>
                <status>0x97</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.auto2.input</key>
                <status>0x96</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].autoloop.auto1.input</key>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].load.input</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.auto2.input</key>
                <status>0x97</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].autoloop.auto1.input</key>
                <status>0x96</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].load.input</key>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].autoloop.auto1.input</key>
                <status>0x97</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>playposition</key>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].load.input</key>
                <status>0x93</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>playposition</key>
                <status>0xB1</status>
                <midino>0x2D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pitch_bend_down.input</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>playposition</key>
                <status>0xB2</status>
                <midino>0x2D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pitch_bend_down.input</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pitch_bend_up.input</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>playposition</key>
                <status>0xB3</status>
                <midino>0x2D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pitch_bend_down.input</key>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pitch_bend_up.input</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pitch_bend_down.input</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pitch_bend_up.input</key>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pitch_bend_up.input</key>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].key_down.input</key>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].key_down.input</key>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].key_down.input</key>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].key_down.input</key>
                <status>0x93</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].key_up.input</key>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].key_up.input</key>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].key_up.input</key>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].key_up.input</key>
                <status>0x93</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is activated.</description>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.wheelToggle</key>
                <description>wheel button to toggle vinyl mode</description>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is activated.</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.wheelToggle</key>
                <description>wheel button to toggle vinyl mode</description>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is activated.</description>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.wheelTouch</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.wheelToggle</key>
                <description>wheel button to toggle vinyl mode</description>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.deckSwitch</key>
                <description>Fires when a deck is activated.</description>
                <status>0x93</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.wheelTouch</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].cue_button.input</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[3].input</key>
                <description>shift fx3</description>
                <status>0x98</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.wheelToggle</key>
                <description>wheel button to toggle vinyl mode</description>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.wheelTouch</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].cue_button.input</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].play_button.input</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[2].input</key>
                <description>shift fx2</description>
                <status>0x98</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[3].input</key>
                <description>shift fx3</description>
                <status>0x99</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.wheelTouch</key>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].cue_button.input</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].play_button.input</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[1].input</key>
                <description>shift fx1</description>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[2].input</key>
                <description>shift fx2</description>
                <status>0x99</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].sync_button.input</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].sync_button.input</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].cue_button.input</key>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].play_button.input</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[1].input</key>
                <description>shift fx1</description>
                <status>0x99</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].sync_button.input</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].sync_button.input</key>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].cue_button.input</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].play_button.input</key>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].sync_button.input</key>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].sync_button.input</key>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].cue_button.input</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].play_button.input</key>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].sync_button.input</key>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].sync_button.input</key>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].cue_button.input</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].play_button.input</key>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].cue_button.input</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].play_button.input</key>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].play_button.input</key>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.effects[1].bpmTap.input</key>
                <status>0x98</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.effects[2].bpmTap.input</key>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>volume</key>
                <status>0xB2</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>MixtrackPlatinum.decks[1].filter.input</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[3].input</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>volume</key>
                <status>0xB3</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>MixtrackPlatinum.decks[2].filter.input</key>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinum.decks[1].low_eq.input</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[2].input</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[3].input</key>
                <status>0x99</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>MixtrackPlatinum.decks[3].filter.input</key>
                <status>0xB2</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinum.decks[2].low_eq.input</key>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinum.decks[1].mid_eq.input</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>MixtrackPlatinum.effects[1].enableButtons[1].input</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[2].input</key>
                <status>0x99</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>MixtrackPlatinum.decks[4].filter.input</key>
                <status>0xB3</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinum.decks[3].low_eq.input</key>
                <status>0xB2</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinum.decks[2].mid_eq.input</key>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>MixtrackPlatinum.decks[1].high_eq.input</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>MixtrackPlatinum.effects[2].enableButtons[1].input</key>
                <status>0x99</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinum.decks[4].low_eq.input</key>
                <status>0xB3</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinum.decks[3].mid_eq.input</key>
                <status>0xB2</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>MixtrackPlatinum.decks[2].high_eq.input</key>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].gain.input</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinum.decks[4].mid_eq.input</key>
                <status>0xB3</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>MixtrackPlatinum.decks[3].high_eq.input</key>
                <status>0xB2</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].gain.input</key>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>MixtrackPlatinum.decks[4].high_eq.input</key>
                <status>0xB3</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].gain.input</key>
                <status>0xB2</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].gain.input</key>
                <status>0xB3</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pitch.inputMSB</key>
                <description>pitch fader msb</description>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pitch.inputMSB</key>
                <description>pitch fader msb</description>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pitch.inputMSB</key>
                <description>pitch fader msb</description>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pitch.inputMSB</key>
                <description>pitch fader msb</description>
                <status>0xB3</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.wheelTurn</key>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.wheelTurn</key>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.wheelTurn</key>
                <status>0xB3</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xBF</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>MixtrackPlatinum.head_gain.input</key>
                <status>0xBF</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>volume</key>
                <status>0xBF</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>MixtrackPlatinum.effects[1].touch_strip.input</key>
                <status>0xB8</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>MixtrackPlatinum.effects[2].touch_strip.input</key>
                <status>0xB9</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinum.browse.knob.input</key>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MixtrackPlatinum.browse.button.input</key>
                <status>0x9F</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MixtrackPlatinum.effects[1].dryWetKnob.input</key>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MixtrackPlatinum.effects[2].dryWetKnob.input</key>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pad_mode.input</key>
                <status>0x94</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pad_mode.input</key>
                <status>0x94</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MixtrackPlatinum.decks[1].pad_mode.input</key>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pad_mode.input</key>
                <status>0x95</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pad_mode.input</key>
                <status>0x95</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MixtrackPlatinum.decks[2].pad_mode.input</key>
                <status>0x95</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pad_mode.input</key>
                <status>0x96</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pad_mode.input</key>
                <status>0x96</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MixtrackPlatinum.decks[3].pad_mode.input</key>
                <status>0x96</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pad_mode.input</key>
                <status>0x97</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pad_mode.input</key>
                <status>0x97</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MixtrackPlatinum.decks[4].pad_mode.input</key>
                <status>0x97</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>slip_enabled</key>
                <status>0x81</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>slip_enabled</key>
                <status>0x91</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x83</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x93</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>slip_enabled</key>
                <status>0x80</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>slip_enabled</key>
                <status>0x90</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x82</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x92</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>slip_enabled</key>
                <status>0x83</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>slip_enabled</key>
                <status>0x93</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x81</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>slip_enabled</key>
                <status>0x82</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>slip_enabled</key>
                <status>0x92</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x80</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
