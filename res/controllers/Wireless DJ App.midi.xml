<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="1.8.0+" schemaVersion="1">
    <info>
		<name>Wireless DJ App</name>
		<author>E<PERSON><PERSON> / <PERSON><PERSON></author>
		<description>iPad wireless midi controller application (http://wirelessdjapp.com)</description>
                <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=2767</forums>
	</info>
    <controller id="Network Wireless DJ">
        <scriptfiles>
            <file filename="Wireless-DJ-scripts.js" functionprefix="WirelessDJ"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>play</key>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1c</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1d</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1e</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.seek_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.seek_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.jog_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.jog_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.tempo_tuning</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.magic_stripe_msb</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x36</midino>
                <group>[Channel1]</group>
                <key>WirelessDJ.magic_stripe_lsb</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- Channel 2 -->
            <control>
                <status>0x91</status>
                <midino>0x1</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x81</status>
                <midino>0x1</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x91</status>
                <midino>0x2</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x81</status>
                <midino>0x2</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x91</status>
                <midino>0x3</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x14</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x17</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x1c</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x1d</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x1e</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x91</status>
                <midino>0x4</midino>
                <group>[Channel2]</group>
                <key>WirelessDJ.seek_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x81</status>
                <midino>0x4</midino>
                <group>[Channel2]</group>
                <key>WirelessDJ.seek_on</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0x91</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
            </control>
            <control>
                <status>0x81</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x16</midino>
                <group>[Channel2]</group>
                <key>WirelessDJ.magic_stripe_msb</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <status>0xb1</status>
                <midino>0x36</midino>
                <group>[Channel2]</group>
                <key>WirelessDJ.magic_stripe_lsb</key>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- Master -->
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1b</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
     </controller>
</MixxxMIDIPreset>
