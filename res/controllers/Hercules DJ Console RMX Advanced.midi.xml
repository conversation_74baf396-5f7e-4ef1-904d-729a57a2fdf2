<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.9.0">
    <info>
        <name>Hercules DJ Console RMX Advanced</name>
        <author><PERSON></author>
        <description>Hercules DJ Console RMX controller mapping with hotcues and loops, v1.9.0. Requires script v1.9.0</description>
        <manual>hercules_dj_console_rmx</manual>
    </info>
    <controller id="Hercules DJ Console RMX MIDI">
        <scriptfiles>
            <file filename="Hercules-DJ-Console-RMX-scripts.js" functionprefix="HerculesRMX"/>
        </scriptfiles>
        <controls>
<!-- TODO: Organization and cleanup -->
<!-- Headphone Cue/Mix POTS Control -->
               <control>
                   <status>0xB0</status>
                   <midino>0x3A</midino>
                   <group>[Master]</group>
                   <key>HerculesRMX.headPhoneMix</key>
                   <options>
                       <Script-Binding/>
                   </options>
               </control>

<!-- Jog Wheels -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x2F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- Play and CuePlay -->
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x24</midino>
                <options>
                    <Button/>
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <Button />
                </options>
            </control>


<!-- Load Deck A/B -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.load</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.load</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

<!-- Stop and Reset button -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.shift</key>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.shift</key>
                <status>0xB0</status>
                <midino>0x25</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesRMX.crossFader</key>
                <status>0xB0</status>
                <midino>0x39</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesRMX.balance</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.previous</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.next</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.deckVolume</key>
                <status>0xB0</status>
                <midino>0x32</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.rate</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesRMX.volume</key>
                <status>0xB0</status>
                <midino>0x38</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.cueSelect</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.bass</key>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.medium</key>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.treble</key>
                <status>0xB0</status>
                <midino>0x34</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.killLow</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.killMid</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.killHigh</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.previous</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.next</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.deckVolume</key>
                <status>0xB0</status>
                <midino>0x3C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.rate</key>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.cueSelect</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.bass</key>
                <status>0xB0</status>
                <midino>0x40</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.medium</key>
                <status>0xB0</status>
                <midino>0x3F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.treble</key>
                <status>0xB0</status>
                <midino>0x3E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.killLow</key>
                <status>0xB0</status>
                <midino>0x28</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.killMid</key>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.killHigh</key>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- A/B Gain  -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.gain</key>
                <status>0xB0</status>
                <midino>0x33</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.gain</key>
                <status>0xB0</status>
                <midino>0x3D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!-- Sync Buttons -->
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.beatSync</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.beatSync</key>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.rateReset</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.rateReset</key>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <Button />
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>HerculesRMX.scratch</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.up</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.down</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.left</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.right</key>
                <group>[Playlist]</group>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options>
                   <Script-Binding/>
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.keypad1</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.keypad1</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.keypad2</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.keypad2</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.keypad3</key>
                <group>[Channel1]</group>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <key>HerculesRMX.keypad3</key>
                <group>[Channel2]</group>
                <status>0xB0</status>
                <midino>0x1B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.keypad4</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.keypad4</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.keypad5</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.keypad5</key>
                <status>0xB0</status>
                <midino>0x1D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>HerculesRMX.keypad6</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>HerculesRMX.keypad6</key>
                <status>0xB0</status>
                <midino>0x1E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
    </controls>
    <outputs>
        <output>
            <group>[Channel1]</group>
            <key>play</key>
            <status>0xB0</status>
            <midino>0x0B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>play</key>
            <status>0xB0</status>
            <midino>0x23</midino>
            <minimum>0.5</minimum>
        </output>
            <output>
          <group>[Channel1]</group>
          <key>cue_default</key>
          <status>0xB0</status>
          <midino>0x0C</midino>
          <minimum>0.5</minimum>
      </output>
      <output>
          <group>[Channel2]</group>
          <key>cue_default</key>
          <status>0xB0</status>
          <midino>0x24</midino>
          <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x3B</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x53</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>

<!-- Headphone Cue -->
        <output>
            <group>[Channel1]</group>
            <key>pfl</key>
            <status>0xB0</status>
            <midino>0x14</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>pfl</key>
            <status>0xB0</status>
            <midino>0x18</midino>
            <minimum>0.5</minimum>
        </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
