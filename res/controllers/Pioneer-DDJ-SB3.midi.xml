<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<MixxxControllerPreset schemaVersion="1" mixxxVersion="2.3+">
  <info>
    <name>Pioneer DDJ-SB3</name>
    <author>Dancephy.com</author>
    <description>Mapping file for the Pioneer DDJ-SB3 controller. Version 1.1.0.</description>
    <wiki>https://github.com/mixxxdj/mixxx/wiki/Pioneer%20DDJ-SB3</wiki>
    <manual>pioneer_ddj_sb3</manual>
    <forums>https://mixxx.discourse.group/t/pioneer-ddj-sb3-mapping-v1-0-now-available/22186</forums>
  </info>
  <controller>
    <scriptfiles>
      <file filename="lodash.mixxx.js"/>
      <file filename="midi-components-0.0.js"/>
      <file functionprefix="PioneerDDJSB3" filename="Pioneer-DDJ-SB3-scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].playButton.input</key>
        <description>Toggles Play/Pause Deck 1, Button: left PLAY (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].playButton.input</key>
        <description>Toggles Play/Pause Deck 2, Button: right PLAY (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].playButton.input</key>
        <description>Toggles Play/Pause Deck 3, Button: left PLAY (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].playButton.input</key>
        <description>Toggles Play/Pause Deck 4, Button: right PLAY (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].playButton.input</key>
        <description>Reverse play Deck 1, Button: SHIFT left PLAY (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].playButton.input</key>
        <description>Reverse play Deck 2, Button: SHIFT right PLAY (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].playButton.input</key>
        <description>Reverse play Deck 3, Button: SHIFT left PLAY (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].playButton.input</key>
        <description>Reverse play Deck 4, Button: SHIFT right PLAY (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].cueButton.input</key>
        <description>Cue Deck 1, Button: left CUE (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].cueButton.input</key>
        <description>Cue Deck 2, Button: right CUE (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].cueButton.input</key>
        <description>Cue Deck 3, Button: left CUE (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].cueButton.input</key>
        <description>Cue Deck 4, Button: right CUE (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].cueButton.input</key>
        <description>Break Deck 1, Button: left SHIFT CUE (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].cueButton.input</key>
        <description>Break Deck 2, Button: right SHIFT CUE (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].cueButton.input</key>
        <description>Break Deck 3, Button: left SHIFT CUE (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].cueButton.input</key>
        <description>Break Deck 4, Button: right SHIFT CUE (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].syncButton.input</key>
        <description>Sync Deck 1, Button: left SYNC (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x58</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].syncButton.input</key>
        <description>Sync Deck 2, Button: right SYNC (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x58</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].syncButton.input</key>
        <description>Sync Deck 3, Button: left SYNC (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x58</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].syncButton.input</key>
        <description>Sync Deck 4, Button: right SYNC (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x58</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].syncButton.input</key>
        <description>Quantize Deck 1, Button: left SHIFT SYNC (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x5C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].syncButton.input</key>
        <description>Quantize Deck 2, Button: right SHIFT SYNC (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x5C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].syncButton.input</key>
        <description>Quantize Deck 3, Button: left SHIFT SYNC (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x5C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].syncButton.input</key>
        <description>Quantize Deck 4, Button: right SHIFT SYNC (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x5C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (Vinyl Mode) Deck 1, left JOGDIAL (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (Vinyl Mode) Deck 2, right JOGDIAL (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (Vinyl Mode) Deck 3, left JOGDIAL (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (Vinyl Mode) Deck 4, right JOGDIAL (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (no Vinyl Mode) Deck 1, left JOGDIAL (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (no Vinyl Mode) Deck 2, right JOGDIAL (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (no Vinyl Mode) Deck 3, left JOGDIAL (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogPlatterTick</key>
        <description>Jog (no Vinyl Mode) Deck 4, right JOGDIAL (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogPlatterTickShift</key>
        <description>Jog fast Deck 1, SHIFT left JOGDIAL (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogPlatterTickShift</key>
        <description>Jog fast Deck 2, SHIFT right JOGDIAL (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogPlatterTickShift</key>
        <description>Jog fast Deck 3, SHIFT left JOGDIAL (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogPlatterTickShift</key>
        <description>Jog fast Deck 4, SHIFT right JOGDIAL (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (Vinyl Mode) Deck 1, left JOGDIAL (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (Vinyl Mode) Deck 2, right JOGDIAL (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (Vinyl Mode) Deck 3, left JOGDIAL (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (Vinyl Mode) Deck 4, right JOGDIAL (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (No Vinyl Mode) Deck 1, left JOGDIAL (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (No Vinyl Mode) Deck 2, right JOGDIAL (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (No Vinyl Mode) Deck 3, left JOGDIAL (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog touch (No Vinyl Mode) Deck 4, right JOGDIAL (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog shift touch Deck 4, SHIFT right JOGDIAL (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog shift touch Deck 2, SHIFT right JOGDIAL (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog shift touch Deck 3, SHIFT left JOGDIAL (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogTouch</key>
        <description>Jog shift touch Deck 4, SHIFT right JOGDIAL (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogRingTick</key>
        <description>Jog ring Deck 1, left JOGDIALSIDE (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogRingTick</key>
        <description>Jog ring Deck 2, right JOGDIALSIDE (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogRingTick</key>
        <description>Jog ring Deck 3, left JOGDIALSIDE (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogRingTick</key>
        <description>Jog ring Deck 4, right JOGDIALSIDE (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.jogRingTickShift</key>
        <description>Jog ring shift Deck 1, SHIFT left JOGDIALSIDE (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.jogRingTickShift</key>
        <description>Jog ring shift Deck 2, SHIFT right JOGDIALSIDE (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.jogRingTickShift</key>
        <description>Jog ring shift Deck 3, SHIFT left JOGDIALSIDE (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.jogRingTickShift</key>
        <description>Jog ring shift Deck 4, SHIFT right JOGDIALSIDE (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 1 (MSB), Slider: left TEMPO (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 1 (LSB), Slider: left TEMPO (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 2 (MSB), Slider: right TEMPO (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 2 (LSB), Slider: right TEMPO (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 3 (MSB), Slider: left TEMPO (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 3 (LSB), Slider: left TEMPO (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 4 (MSB), Slider: right TEMPO (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 4 (LSB), Slider: right TEMPO (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 1 (MSB), Slider: SHIFT left TEMPO (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 1 (LSB), Slider: SHIFT left TEMPO (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x25</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 2 (MSB), Slider: SHIFT right TEMPO (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 2 (LSB), Slider: SHIFT right TEMPO (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x25</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 3 (MSB), Slider: SHIFT left TEMPO (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 3 (LSB), Slider: SHIFT left TEMPO (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x25</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].tempoFader.inputMSB</key>
        <description>Tempo slider Deck 4 (MSB), Slider: SHIFT right TEMPO (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].tempoFader.inputLSB</key>
        <description>Tempo slider Deck 4 (LSB), Slider: SHIFT right TEMPO (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x25</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.vinylButton</key>
        <description>Vinyl Deck 1, Button: left VINYL (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.vinylButton</key>
        <description>Vinyl Deck 2, Button: right VINYL (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.vinylButton</key>
        <description>Vinyl Deck 3, Button: left VINYL (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.vinylButton</key>
        <description>Vinyl Deck 4, Button: right VINYL (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.slipButton</key>
        <description>Slip Deck 1, Button: SHIFT left VINYL (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.slipButton</key>
        <description>Slip Deck 2, Button: SHIFT right VINYL (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.slipButton</key>
        <description>Slip Deck 3, Button: SHIFT left VINYL (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.slipButton</key>
        <description>Slip Deck 4, Button: SHIFT right VINYL (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.keyLockButton</key>
        <description>Key lock Deck 1, Button: left KEYLOCK (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.keyLockButton</key>
        <description>Key lock Deck 2, Button: right KEYLOCK (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.keyLockButton</key>
        <description>Key lock Deck 3, Button: left KEYLOCK (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.keyLockButton</key>
        <description>Key lock Deck 4, Button: right KEYLOCK (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.shiftKeyLockButton</key>
        <description>Shift KeyLock, Button: left SHIFT &amp; KeyLock(Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.shiftKeyLockButton</key>
        <description>Shift KeyLock, Button: right SHIFT &amp; KeyLock(Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.shiftKeyLockButton</key>
        <description>Shift KeyLock, Button: left SHIFT &amp; KeyLock(Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.shiftKeyLockButton</key>
        <description>Shift KeyLock, Button: right SHIFT &amp; KeyLock(Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].shiftButton</key>
        <description>Shift Deck 1, Button: left SHIFT (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].shiftButton</key>
        <description>Shift Deck 2, Button: right SHIFT (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].shiftButton</key>
        <description>Shift Deck 3, Button: left SHIFT (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].shiftButton</key>
        <description>Shift Deck 4, Button: right SHIFT (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x3F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck1Button</key>
        <description>Toggles Deck 1 Button</description>
        <status>0x90</status>
        <midino>0x72</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck3Button</key>
        <description>Toggles Deck 3 Button</description>
        <status>0x92</status>
        <midino>0x72</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck2Button</key>
        <description>Toggles Deck 2 Button</description>
        <status>0x91</status>
        <midino>0x72</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck4Button</key>
        <description>Toggles Deck 4 Button</description>
        <status>0x93</status>
        <midino>0x72</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[AutoDJ]</group>
        <key>PioneerDDJSB3.autodjSkipNext</key>
        <description>AutoDJ skip next, Button: SHIFT &amp; DECK3</description>
        <status>0x90</status>
        <midino>0x73</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[AutoDJ]</group>
        <key>PioneerDDJSB3.autodjToggle</key>
        <description>Toggle AutoDJ On/Off, Button: SHIFT DECK4</description>
        <status>0x91</status>
        <midino>0x73</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[AutoDJ]</group>
        <key>PioneerDDJSB3.autodjSkipNext</key>
        <description>AutoDJ skip next, Button: SHIFT DECK3</description>
        <status>0x92</status>
        <midino>0x73</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[AutoDJ]</group>
        <key>PioneerDDJSB3.autodjToggle</key>
        <description>Toggle AutoDJ On/Off, Button: SHIFT DECK4</description>
        <status>0x93</status>
        <midino>0x73</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deckFaderMSB</key>
        <description>Channel fader Deck 1 (MSB), Slider: left CHANNELFADER (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deckFaderLSB</key>
        <description>Channel fader Deck 1 (LSB), Slider: left CHANNELFADER (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deckFaderMSB</key>
        <description>Channel fader Deck 2 (MSB), Slider: right CHANNELFADER (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deckFaderLSB</key>
        <description>Channel fader Deck 2 (LSB), Slider: right CHANNELFADER (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deckFaderMSB</key>
        <description>Channel fader Deck 3 (MSB), Slider: left CHANNELFADER (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deckFaderLSB</key>
        <description>Channel fader Deck 3 (LSB), Slider: left CHANNELFADER (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deckFaderMSB</key>
        <description>Channel fader Deck 4 (MSB), Slider: right CHANNELFADER (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deckFaderLSB</key>
        <description>Channel fader Deck 4 (LSB), Slider: right CHANNELFADER (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].gainKnob.inputMSB</key>
        <description>Gain Deck 1 (MSB), Knob: left TRIM (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].gainKnob.inputLSB</key>
        <description>Gain Deck 1 (LSB), Knob: left TRIM (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].gainKnob.inputMSB</key>
        <description>Gain Deck 2 (MSB), Knob: right TRIM (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].gainKnob.inputLSB</key>
        <description>Gain Deck 2 (LSB), Knob: right TRIM (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].gainKnob.inputMSB</key>
        <description>Gain Deck 3 (MSB), Knob: left TRIM (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].gainKnob.inputLSB</key>
        <description>Gain Deck 3 (LSB), Knob: left TRIM (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].gainKnob.inputMSB</key>
        <description>Gain Deck 4 (MSB), Knob: right TRIM (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].gainKnob.inputLSB</key>
        <description>Gain Deck 4 (LSB), Knob: right TRIM (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].quickEffectKnob.inputMSB</key>
        <description>Filter Deck 1 (MSB), Knob: left FILTER (Deck 1 active)</description>
        <status>0xB6</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].quickEffectKnob.inputLSB</key>
        <description>Filter Deck 1 (LSB), Knob: left FILTER (Deck 1 active)</description>
        <status>0xB6</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].quickEffectKnob.inputMSB</key>
        <description>Filter Deck 2 (MSB), Knob: right FILTER (Deck 2 active)</description>
        <status>0xB6</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].quickEffectKnob.inputLSB</key>
        <description>Filter Deck 2 (LSB), Knob: right FILTER (Deck 2 active)</description>
        <status>0xB6</status>
        <midino>0x38</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].quickEffectKnob.inputMSB</key>
        <description>Filter Deck 3 (MSB), Knob: left FILTER (Deck 3 active)</description>
        <status>0xB6</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].quickEffectKnob.inputLSB</key>
        <description>Filter Deck 3 (LSB), Knob: left FILTER (Deck 3 active)</description>
        <status>0xB6</status>
        <midino>0x39</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].quickEffectKnob.inputMSB</key>
        <description>Filter Deck 4 (MSB), Knob: right FILTER (Deck 4 active)</description>
        <status>0xB6</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].quickEffectKnob.inputLSB</key>
        <description>Filter Deck 4 (LSB), Knob: right FILTER (Deck 4 active)</description>
        <status>0xB6</status>
        <midino>0x3A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>gain</key>
        <description>Master Level</description>
        <status>0xB6</status>
        <midino>0x08</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>gain</key>
        <description>Master Level</description>
        <status>0xB6</status>
        <midino>0x28</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headGain</key>
        <description>Headphones Level</description>
        <status>0xB6</status>
        <midino>0x0D</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headGain</key>
        <description>Headphones Level</description>
        <status>0xB6</status>
        <midino>0x2D</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>PioneerDDJSB3.rotarySelector</key>
        <description>Browser rotate, Knob: BROWSER</description>
        <status>0xB6</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>PioneerDDJSB3.shiftedRotarySelector</key>
        <description>Shift Browser rotate, Knob: BROWSER</description>
        <status>0xB6</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[PreviewDeck1]</group>
        <key>PioneerDDJSB3.rotarySelectorClick</key>
        <description>Preview of selected song, stop preview if no rotation between next press (Down: Load and start -- Up: Jump to position 30%), Knob: BROWSER</description>
        <status>0x96</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>PioneerDDJSB3.rotarySelectorShiftedClick</key>
        <description>Open/close selected left List, Knob: SHIFT &amp; BROWSER</description>
        <status>0x96</status>
        <midino>0x42</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.loadButton</key>
        <description>Load selected Deck 1, Button: left LOAD (Deck 1 active)</description>
        <status>0x96</status>
        <midino>0x46</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>show</key>
        <description>Toggle Effect view, Button: SHIFT &amp; left LOAD (Deck 1 active)</description>
        <status>0x96</status>
        <midino>0x58</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.loadButton</key>
        <description>Load selected Deck 3, Button: left LOAD (Deck 3 active)</description>
        <status>0x96</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>show</key>
        <description>Toggle Effect view, Button: SHIFT &amp; left LOAD (Deck 3 active)</description>
        <status>0x96</status>
        <midino>0x60</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.loadButton</key>
        <description>Load selected Deck 2, Button: right LOAD (Deck 2 active)</description>
        <status>0x96</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Samplers]</group>
        <key>show_samplers</key>
        <description>Toggle Sampler view, Button: SHIFT &amp; right LOAD (Deck 2 active)</description>
        <status>0x96</status>
        <midino>0x59</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.loadButton</key>
        <description>Load selected Deck 4, Button: right LOAD (Deck 4 active)</description>
        <status>0x96</status>
        <midino>0x49</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Samplers]</group>
        <key>show_samplers</key>
        <description>Toggle Sampler view, Button: SHIFT &amp; right LOAD (Deck 4 active)</description>
        <status>0x96</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[1].input</key>
        <description>FX1-1 (Deck1), Button: left FX1-1</description>
        <status>0x94</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[4].input</key>
        <description>Shift FX1-1, Button: left SHIFT &amp; FX1-1</description>
        <status>0x94</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[2].input</key>
        <description>FX1-2 (Head), Button: left FX1-2</description>
        <status>0x94</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[5].input</key>
        <description>Shift FX1-2 (Head), Button: left SHIFT &amp; FX1-2</description>
        <status>0x94</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[3].input</key>
        <description>FX1-3  (Deck2), Button: left FX1-3</description>
        <status>0x94</status>
        <midino>0x49</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].button[6].input</key>
        <description>Shift FX1-3  (Deck2), Button: left SHIFT &amp; FX1-3</description>
        <status>0x94</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].knob.inputMSB</key>
        <description>FX1 (MSB), Knob: left FX1</description>
        <status>0xB4</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].knob.inputLSB</key>
        <description>FX1 (LSB), Knob: left FX1</description>
        <status>0xB4</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].shiftKnob.inputMSB</key>
        <description>Shift FX1 (MSB), Knob: left SHIFT &amp; FX1</description>
        <status>0xB4</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>PioneerDDJSB3.effectUnit[1].shiftKnob.inputLSB</key>
        <description>Shift FX1 (LSB), Knob: left SHIFT &amp; FX1</description>
        <status>0xB4</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[1].input</key>
        <description>FX2-1 (Deck1), Button: right FX2-1</description>
        <status>0x95</status>
        <midino>0x47</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[4].input</key>
        <description>Shift FX2-1 (Deck1), Button: right SHIFT &amp; FX2-1</description>
        <status>0x95</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[2].input</key>
        <description>FX2-2 (Head), Button: right FX2-2</description>
        <status>0x95</status>
        <midino>0x48</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[5].input</key>
        <description>Shift FX2-2 (Head), Button: right SHIFT &amp; FX2-2</description>
        <status>0x95</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[3].input</key>
        <description>FX2-3 (Deck2), Button: right FX2-3</description>
        <status>0x95</status>
        <midino>0x49</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].button[6].input</key>
        <description>Shift FX2-3 (Deck2), Button: right SHIFT &amp; FX2-3</description>
        <status>0x95</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].knob.inputMSB</key>
        <description>FX2 (MSB), Knob: right FX2</description>
        <status>0xB5</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].knob.inputLSB</key>
        <description>FX2 (LSB), Knob: right FX2</description>
        <status>0xB5</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].shiftKnob.inputMSB</key>
        <description>Shift FX2 (MSB), Knob: right SHIFT &amp; FX1</description>
        <status>0xB5</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>PioneerDDJSB3.effectUnit[2].shiftKnob.inputLSB</key>
        <description>Shift FX2 (LSB), Knob: right SHIFT &amp; FX2</description>
        <status>0xB5</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[3].inputMSB</key>
        <description>High level Deck 1 (MSB), Knob: left HI (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[3].inputLSB</key>
        <description>High level Deck 1 (LSB), Knob: left HI (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x27</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[3].inputMSB</key>
        <description>High level Deck 2 (MSB), Knob: right HI (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[3].inputLSB</key>
        <description>High level Deck 2 (LSB), Knob: right HI (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x27</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[3].inputMSB</key>
        <description>High level Deck 3 (MSB), Knob: left HI (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[3].inputLSB</key>
        <description>High level Deck 3 (LSB), Knob: left HI (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x27</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[3].inputMSB</key>
        <description>High level Deck 4 (MSB), Knob: right HI (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[3].inputLSB</key>
        <description>High level Deck 4 (LSB), Knob: right HI (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x27</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[2].inputMSB</key>
        <description>Mid level Deck 1 (MSB), Knob: left MID (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[2].inputLSB</key>
        <description>Mid level Deck 1 (LSB), Knob: left MID (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[2].inputMSB</key>
        <description>Mid level Deck 2 (MSB), Knob: right HI (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[2].inputLSB</key>
        <description>Mid level Deck 2 (LSB), Knob: right HI (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[2].inputMSB</key>
        <description>Mid level Deck 3 (MSB), Knob: left MID (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[2].inputLSB</key>
        <description>Mid level Deck 3 (LSB), Knob: left MID (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[2].inputMSB</key>
        <description>Mid level Deck 4 (MSB), Knob: right MID (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[2].inputLSB</key>
        <description>Mid level Deck 4 (LSB), Knob: right MID (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[1].inputMSB</key>
        <description>Low level Deck 1 (MSB), Knob: left LOW (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.deck[1].eqKnob[1].inputLSB</key>
        <description>Low level Deck 1 (LSB), Knob: left LOW (Deck 1 active)</description>
        <status>0xB0</status>
        <midino>0x2F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[1].inputMSB</key>
        <description>Low level Deck 2 (MSB), Knob: right LOW (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.deck[2].eqKnob[1].inputLSB</key>
        <description>Low level Deck 2 (LSB), Knob: right LOW (Deck 2 active)</description>
        <status>0xB1</status>
        <midino>0x2F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[1].inputMSB</key>
        <description>Low level Deck 3 (MSB), Knob: left LOW (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.deck[3].eqKnob[1].inputLSB</key>
        <description>Low level Deck 3 (LSB), Knob: left LOW (Deck 3 active)</description>
        <status>0xB2</status>
        <midino>0x2F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[1].inputMSB</key>
        <description>Low level Deck 4 (MSB), Knob: right LOW (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.deck[4].eqKnob[1].inputLSB</key>
        <description>Low level Deck 4 (LSB), Knob: right LOW (Deck 4 active)</description>
        <status>0xB3</status>
        <midino>0x2F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>PioneerDDJSB3.masterCueButton</key>
        <description>Toggles headphone master cue, Button: Master</description>
        <status>0x96</status>
        <midino>0x5B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>PioneerDDJSB3.masterCueButton</key>
        <description>Toggles headphone master cue, Button: SHIFT + Master</description>
        <status>0x96</status>
        <midino>0x78</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.headphoneCueButton</key>
        <description>Toggles headphone cueing Deck 1, Button: left CUE (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.headphoneCueButton</key>
        <description>Toggles headphone cueing Deck 2, Button: right CUE (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.headphoneCueButton</key>
        <description>Toggles headphone cueing Deck 3, Button: left CUE (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.headphoneCueButton</key>
        <description>Toggles headphone cueing Deck 4, Button: right CUE (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.headphoneShiftCueButton</key>
        <description>Toggles headphone cueing Deck 3, Button: SHIFT &amp; left CUE (Deck 1 active)</description>
        <status>0x90</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.headphoneShiftCueButton</key>
        <description>Toggles headphone cueing Deck 4, Button: SHIFT &amp; right CUE (Deck 2 active)</description>
        <status>0x91</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.headphoneShiftCueButton</key>
        <description>Toggles headphone cueing Deck 1, Button: SHIFT &amp; left CUE (Deck 3 active)</description>
        <status>0x92</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.headphoneShiftCueButton</key>
        <description>Toggles headphone cueing Deck 2, Button: SHIFT &amp; right CUE (Deck 4 active)</description>
        <status>0x93</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.autoLoopButton</key>
        <description>Deck 1 Auto Loop, Button: Auto Loop</description>
        <status>0x90</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.autoLoopButton</key>
        <description>Deck 2 Auto Loop, Button: Auto Loop</description>
        <status>0x91</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.autoLoopButton</key>
        <description>Deck 3 Auto Loop, Button: Auto Loop</description>
        <status>0x92</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.autoLoopButton</key>
        <description>Deck 4 Auto Loop, Button: Auto Loop</description>
        <status>0x93</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.reloopButton</key>
        <description>Deck 1 Reloop, Button: SHIFT + Auto Loop</description>
        <status>0x90</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.reloopButton</key>
        <description>Deck 2 Reloop, Button: SHIFT + Auto Loop</description>
        <status>0x91</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.reloopButton</key>
        <description>Deck 3 Reloop, Button: SHIFT + Auto Loop</description>
        <status>0x92</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.reloopButton</key>
        <description>Deck 4 Reloop, Button: SHIFT + Auto Loop</description>
        <status>0x93</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_halve</key>
        <description>Deck 1 Loop halve, Button: 1/2X</description>
        <status>0x90</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_halve</key>
        <description>Deck 2 Loop halve, Button: 1/2X</description>
        <status>0x91</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_halve</key>
        <description>Deck 4 Loop halve, Button: 1/2X</description>
        <status>0x92</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_halve</key>
        <description>Deck 4 Loop halve, Button: 1/2X</description>
        <status>0x93</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_in</key>
        <description>Deck 1 Loop in, Button: SHIFT + 1/2X</description>
        <status>0x90</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_in</key>
        <description>Deck 2 Loop in, Button: SHIFT + 1/2X</description>
        <status>0x91</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_in</key>
        <description>Deck 3 Loop in, Button: SHIFT + 1/2X</description>
        <status>0x92</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_in</key>
        <description>Deck 4 Loop in, Button: SHIFT + 1/2X</description>
        <status>0x93</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_double</key>
        <description>Deck 1 Loop Double, Button: 2X</description>
        <status>0x90</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_double</key>
        <description>Deck 2 Loop Double, Button: 2X</description>
        <status>0x91</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_double</key>
        <description>Deck 3 Loop Double, Button: 2X</description>
        <status>0x92</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_double</key>
        <description>Deck 4 Loop Double, Button: 2X</description>
        <status>0x93</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_out</key>
        <description>Deck 1 Loop Out, Button: SHIFT + 2X</description>
        <status>0x90</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_out</key>
        <description>Deck 2 Loop Out, Button: SHIFT + 2X</description>
        <status>0x91</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_out</key>
        <description>Deck 3 Loop Out, Button: SHIFT + 2X</description>
        <status>0x92</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_out</key>
        <description>Deck 4 Loop Out, Button: SHIFT + 2X</description>
        <status>0x93</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <description>Crossfader (MSB), Slider: FADE</description>
        <status>0xB6</status>
        <midino>0x1F</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <description>Crossfader (LSB), Slider: FADE</description>
        <status>0xB6</status>
        <midino>0x3F</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].beatJumpDivide</key>
        <description>BeatJump Size /2 Deck 1, Button: PAD1 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].beatJumpDivide</key>
        <description>BeatJump Size /2 Deck 2, Button: PAD1 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].beatJumpDivide</key>
        <description>BeatJump Size /2 Deck 3, Button: PAD1 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].beatJumpDivide</key>
        <description>BeatJump Size /2 Deck 4, Button: PAD1 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_4_backward</key>
        <description>BeatJump Backward 4 Deck 1, Button: SHIFT &amp; PAD1 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x48</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_4_backward</key>
        <description>BeatJump Backward 4 Deck 2, Button: SHIFT &amp; PAD1 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x48</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_4_backward</key>
        <description>BeatJump Backward 4 Deck 3, Button: SHIFT &amp; PAD1 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x48</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_4_backward</key>
        <description>BeatJump Backward 4 Deck 4, Button: SHIFT &amp; PAD1 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x48</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].beatJumpMultiply</key>
        <description>BeatJump Size *2 Deck 1, Button: PAD2 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].beatJumpMultiply</key>
        <description>BeatJump Size *2 Deck 2, Button: PAD2 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].beatJumpMultiply</key>
        <description>BeatJump Size *2 Deck 3, Button: PAD2 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].beatJumpMultiply</key>
        <description>BeatJump Size *2 Deck 4, Button: PAD2 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_8_backward</key>
        <description>BeatJump Backward 8 Deck 1, Button: SHIFT &amp; PAD2 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x49</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_8_backward</key>
        <description>BeatJump Backward 8 Deck 2, Button: SHIFT &amp; PAD2 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x49</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_8_backward</key>
        <description>BeatJump Backward 8 Deck 3, Button: SHIFT &amp; PAD2 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x49</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_8_backward</key>
        <description>BeatJump Backward 8 Deck 4, Button: SHIFT &amp; PAD2 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x49</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_backward</key>
        <description>BeatJump Move Back Deck 1, Button: PAD3 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x42</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_backward</key>
        <description>BeatJump Move Back Deck 2, Button: PAD3 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x42</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_backward</key>
        <description>BeatJump Move Back Deck 3, Button: PAD3 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x42</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_backward</key>
        <description>BeatJump Move Back Deck 4, Button: PAD3 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x42</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_16_backward</key>
        <description>BeatJump Backward 16 Deck 1, Button: SHIFT &amp; PAD3 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_16_backward</key>
        <description>BeatJump Backward 16 Deck 2, Button: SHIFT &amp; PAD3 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_16_backward</key>
        <description>BeatJump Backward 16 Deck 3, Button: SHIFT &amp; PAD3 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_16_backward</key>
        <description>BeatJump Backward 16 Deck 4, Button: SHIFT &amp; PAD3 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_forward</key>
        <description>BeatJump Move Next Deck 1, Button: PAD4 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x43</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_forward</key>
        <description>BeatJump Move Next Deck 2, Button: PAD4 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x43</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_forward</key>
        <description>BeatJump Move Next Deck 3, Button: PAD4 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x43</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_forward</key>
        <description>BeatJump Move Next Deck 4, Button: PAD4 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x43</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_32_backward</key>
        <description>BeatJump Backward 32 Deck 1, Button: SHIFT &amp; PAD4 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_32_backward</key>
        <description>BeatJump Backward 32 Deck 2, Button: SHIFT &amp; PAD4 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_32_backward</key>
        <description>BeatJump Backward 32 Deck 3, Button: SHIFT &amp; PAD4 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_32_backward</key>
        <description>BeatJump Backward 32 Deck 4, Button: SHIFT &amp; PAD4 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>start</key>
        <description>Start of track Deck 1, Button: PAD5 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x44</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>start</key>
        <description>Start of track Deck 2, Button: PAD5 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x44</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>start</key>
        <description>Start of track Deck 3, Button: PAD5 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x44</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>start</key>
        <description>Start of track Deck 4, Button: PAD5 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x44</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_4_forward</key>
        <description>Beatjump Forward 4 Deck 1, Button: SHIFT + PAD5 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_4_forward</key>
        <description>Beatjump Forward 4 Deck 2, Button: SHIFT + PAD5 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_4_forward</key>
        <description>Beatjump Forward 4 Deck 3, Button: SHIFT + PAD5 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_4_forward</key>
        <description>Beatjump Forward 4 Deck 4, Button: SHIFT + PAD5 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <description>Back for track Deck 1, Button: PAD6 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x45</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <description>Back for track Deck 2, Button: PAD6 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x45</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>back</key>
        <description>Back for track Deck 3, Button: PAD6 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x45</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>back</key>
        <description>Back for track Deck 4, Button: PAD6 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x45</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_8_forward</key>
        <description>Beatjump Forward 8 Deck 1, Button: SHIFT + PAD6 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_8_forward</key>
        <description>Beatjump Forward 8 Deck 2, Button: SHIFT + PAD6 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_8_forward</key>
        <description>Beatjump Forward 8 Deck 3, Button: SHIFT + PAD6 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_8_forward</key>
        <description>Beatjump Forward 8 Deck 4, Button: SHIFT + PAD6 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <description>Forward for track Deck 1, Button: PAD7 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x46</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <description>Forward for track Deck 2, Button: PAD7 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x46</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>fwd</key>
        <description>Forward for track Deck 3, Button: PAD7 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x46</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>fwd</key>
        <description>Forward for track Deck 4, Button: PAD7 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x46</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_16_forward</key>
        <description>Beatjump 16 forward Deck 1, Button: SHIFT + PAD7 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_16_forward</key>
        <description>Beatjump 16 forward Deck 2, Button: SHIFT + PAD7 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_16_forward</key>
        <description>Beatjump 16 forward Deck 3, Button: SHIFT + PAD7 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_16_forward</key>
        <description>Beatjump 16 forward Deck 4, Button: SHIFT + PAD7 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <description>Set track Deck 1 to reverse, Button: PAD8 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x47</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <description>Set track Deck 2 to reverse, Button: PAD8 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x47</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>reverse</key>
        <description>Set track Deck 3 to reverse, Button: PAD8 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x47</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>reverse</key>
        <description>Set track Deck 4 to reverse, Button: PAD8 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x47</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_32_forward</key>
        <description>Beatjump 32 forward Deck 1, Button: PAD8 (in BEATJUMP-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x4F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_32_forward</key>
        <description>Beatjump 32 forward Deck 2, Button: PAD8 (in BEATJUMP-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x4F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_32_forward</key>
        <description>Beatjump 32 forward Deck 3, Button: PAD8 (in BEATJUMP-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x4F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_32_forward</key>
        <description>Beatjump 32 forward Deck 4, Button: PAD8 (in BEATJUMP-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x4F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <description>Hot-Cue 1 Deck 1, Button: PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x00</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <description>Hot-Cue 1 Deck 2, Button: PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x00</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_activate</key>
        <description>Hot-Cue 1 Deck 3, Button: PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x00</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_activate</key>
        <description>Hot-Cue 1 Deck 4, Button: PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x00</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_clear</key>
        <description>Clear Hot-Cue 1 Deck 1, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_clear</key>
        <description>Clear Hot-Cue 1 Deck 2, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_clear</key>
        <description>Clear Hot-Cue 1 Deck 3, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_clear</key>
        <description>Clear Hot-Cue 1 Deck 4, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <description>Hot-Cue 2 Deck 1, Button: PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x01</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <description>Hot-Cue 2 Deck 2, Button: PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x01</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_activate</key>
        <description>Hot-Cue 2 Deck 3, Button: PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x01</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_activate</key>
        <description>Hot-Cue 2 Deck 4, Button: PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x01</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_clear</key>
        <description>Clear Hot-Cue 2 Deck 1, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x09</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_clear</key>
        <description>Clear Hot-Cue 2 Deck 2, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x09</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_clear</key>
        <description>Clear Hot-Cue 2 Deck 3, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x09</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_clear</key>
        <description>Clear Hot-Cue 2 Deck 4, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x09</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <description>Hot-Cue 3 Deck 1, Button: PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <description>Hot-Cue 3 Deck 2, Button: PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_activate</key>
        <description>Hot-Cue 3 Deck 3, Button: PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_activate</key>
        <description>Hot-Cue 3 Deck 4, Button: PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_clear</key>
        <description>Clear Hot-Cue 3 Deck 1, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_clear</key>
        <description>Clear Hot-Cue 3 Deck 2, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_clear</key>
        <description>Clear Hot-Cue 3 Deck 3, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_clear</key>
        <description>Clear Hot-Cue 3 Deck 4, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_activate</key>
        <description>Hot-Cue 4 Deck 1, Button: PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x03</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_activate</key>
        <description>Hot-Cue 4 Deck 2, Button: PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x03</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_activate</key>
        <description>Hot-Cue 4 Deck 3, Button: PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x03</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_activate</key>
        <description>Hot-Cue 4 Deck 4, Button: PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x03</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_clear</key>
        <description>Clear Hot-Cue 4 Deck 1, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_clear</key>
        <description>Clear Hot-Cue 4 Deck 2, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_clear</key>
        <description>Clear Hot-Cue 4 Deck 3, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_clear</key>
        <description>Clear Hot-Cue 4 Deck 4, Button: SHIFT &amp; PAD1 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_5_activate</key>
        <description>Hot-Cue 5 Deck 1, Button: PAD5 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x04</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_5_activate</key>
        <description>Hot-Cue 5 Deck 2, Button: PAD5 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x04</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_5_activate</key>
        <description>Hot-Cue 5 Deck 3, Button: PAD5 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x04</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_5_activate</key>
        <description>Hot-Cue 5 Deck 4, Button: PAD5 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x04</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_5_clear</key>
        <description>Hot-Cue 5 Deck 1, Button: SHIFT + PAD5 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_5_clear</key>
        <description>Hot-Cue 5 Deck 2, Button: SHIFT + PAD5 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_5_clear</key>
        <description>Hot-Cue 5 Deck 3, Button: SHIFT + PAD5 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_5_clear</key>
        <description>Hot-Cue 5 Deck 4, Button: SHIFT + PAD5 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_6_activate</key>
        <description>Hot-Cue 6 Deck 1, Button: PAD6 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x05</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_6_activate</key>
        <description>Hot-Cue 6 Deck 2, Button: PAD6 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x05</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_6_activate</key>
        <description>Hot-Cue 6 Deck 3, Button: PAD6 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x05</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_6_activate</key>
        <description>Hot-Cue 6 Deck 4, Button: SHIFT (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x05</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_6_clear</key>
        <description>Hot-Cue 6 Deck 1, Button: SHIFT + PAD6 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_6_clear</key>
        <description>Hot-Cue 6 Deck 2, Button: SHIFT + PAD6 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_6_clear</key>
        <description>Hot-Cue 6 Deck 3, Button: SHIFT + PAD6 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_6_clear</key>
        <description>Hot-Cue 6 Deck 4, Button: SHIFT + PAD6 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_7_activate</key>
        <description>Hot-Cue 7 Deck 1, Button: PAD7 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_7_activate</key>
        <description>Hot-Cue 7 Deck 2, Button: PAD7 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_7_activate</key>
        <description>Hot-Cue 7 Deck 3, Button: PAD7 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_7_activate</key>
        <description>Hot-Cue 7 Deck 4, Button: PAD7 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_7_clear</key>
        <description>Hot-Cue 7 Deck 1, Button: SHIFT + PAD7 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_7_clear</key>
        <description>Hot-Cue 7 Deck 2, Button: SHIFT + PAD7 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_7_clear</key>
        <description>Hot-Cue 7 Deck 3, Button: SHIFT + PAD7 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_7_clear</key>
        <description>Hot-Cue 7 Deck 4, Button: SHIFT + PAD7 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_8_activate</key>
        <description>Hot-Cue 8 Deck 1, Button: PAD8 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x07</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_8_activate</key>
        <description>Hot-Cue 8 Deck 2, Button: PAD8 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x07</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_8_activate</key>
        <description>Hot-Cue 8 Deck 3, Button: PAD8 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x07</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_8_activate</key>
        <description>Hot-Cue 8 Deck 4, Button: PAD8 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x07</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_8_clear</key>
        <description>Hot-Cue 8 Deck 1, Button: SHIFT + PAD8 (in HOT-CUE-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x0F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_8_clear</key>
        <description>Hot-Cue 8 Deck 2, Button: SHIFT + PAD8 (in HOT-CUE-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x0F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_8_clear</key>
        <description>Hot-Cue 8 Deck 3, Button: SHIFT + PAD8 (in HOT-CUE-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x0F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_8_clear</key>
        <description>Hot-Cue 8 Deck 4, Button: SHIFT + PAD8 (in HOT-CUE-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x0F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].hotcueMode</key>
        <description>Pad Hot-Cue button, activate HotCue mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].beatJumpMode</key>
        <description>SHIFT + Pad Hot-Cue button, activate BeatJump mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x69</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].hotcueMode</key>
        <description>Pad Hot-Cue button, activate HotCue mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].beatJumpMode</key>
        <description>SHIFT + Pad Hot-Cue button, activate BeatJump mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x69</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].hotcueMode</key>
        <description>Pad Hot-Cue button, activate HotCue mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].beatJumpMode</key>
        <description>SHIFT + Pad Hot-Cue button, activate BeatJump mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x69</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].hotcueMode</key>
        <description>Pad Hot-Cue button, activate HotCue mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].beatJumpMode</key>
        <description>SHIFT + Pad Hot-Cue button, activate BeatJump mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x69</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].fxFadeMode</key>
        <description>Pad FX FADE button, activate FX Fade mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].rollMode</key>
        <description>SHIFT + Pad FX FADE button, activate Roll mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].fxFadeMode</key>
        <description>Pad FX FADE button, activate FX Fade mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].rollMode</key>
        <description>SHIFT + Pad FX FADE button, activate Roll mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].fxFadeMode</key>
        <description>Pad FX FADE button, activate FX Fade mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].rollMode</key>
        <description>SHIFT + Pad FX FADE button, activate Roll mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].fxFadeMode</key>
        <description>Pad FX FADE button, activate FX Fade mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].rollMode</key>
        <description>SHIFT + Pad FX FADE button, activate Roll mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].padScratchMode</key>
        <description>Pad PAD SCRATCH button, activate Pad Scratch mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerMode</key>
        <description>SHIFT + Pad PAD SCRATCH button, activate Slicer mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x6D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].padScratchMode</key>
        <description>Pad PAD SCRATCH button, activate Pad Scratch mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerMode</key>
        <description>SHIFT + Pad PAD SCRATCH button, activate Slicer mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x6D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].padScratchMode</key>
        <description>Pad PAD SCRATCH button, activate Pad Scratch mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerMode</key>
        <description>SHIFT + Pad PAD SCRATCH button, activate Slicer mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x6D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].padScratchMode</key>
        <description>Pad PAD SCRATCH button, activate Pad Scratch mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerMode</key>
        <description>SHIFT + Pad PAD SCRATCH button, activate Slicer mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x6D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].samplerMode</key>
        <description>Pad SAMPLER button, activate Sampler mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].transMode</key>
        <description>SHIFT + Pad SAMPLER button, activate Trans mode (Deck 1)</description>
        <status>0x90</status>
        <midino>0x6E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].samplerMode</key>
        <description>Pad SAMPLER button, activate Sampler mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].transMode</key>
        <description>SHIFT + Pad SAMPLER button, activate Trans mode (Deck 2)</description>
        <status>0x91</status>
        <midino>0x6E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].samplerMode</key>
        <description>Pad SAMPLER button, activate Sampler mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].transMode</key>
        <description>SHIFT + Pad SAMPLER button, activate Trans mode (Deck 3)</description>
        <status>0x92</status>
        <midino>0x6E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].samplerMode</key>
        <description>Pad SAMPLER button, activate Sampler mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].transMode</key>
        <description>SHIFT + Pad SAMPLER button, activate Trans mode (Deck 4)</description>
        <status>0x93</status>
        <midino>0x6E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD1 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD1 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD1 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD1 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD2 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x51</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD2 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x51</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD2 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x51</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD2 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x51</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD3 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD3 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD3 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD3 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD4 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD4 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD4 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD4 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD5 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD5 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD5 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD5 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD6 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x55</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD6 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x55</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD6 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x55</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD6 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x55</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD7 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x56</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD7 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x56</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD7 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x56</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD7 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x56</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD8 (in ROLL-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x57</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD8 (in ROLL-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x57</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD8 (in ROLL-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x57</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.beatloopRollButtons</key>
        <description>Beatloop Roll, Button: PAD8 (in ROLL-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x57</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_play</key>
        <description>Sampler 1 play, Button: PAD1 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_play</key>
        <description>Sampler 5 play, Button: PAD1 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_play</key>
        <description>Sampler 1 play, Button: PAD1 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_play</key>
        <description>Sampler 5 play, Button: PAD1 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>stop</key>
        <description>Sampler 1 stop, Button: SHIFT &amp; PAD1 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x38</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>stop</key>
        <description>Sampler 5 stop, Button: SHIFT &amp; PAD1 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x38</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>stop</key>
        <description>Sampler 1 stop, Button: SHIFT &amp; PAD1 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x38</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>stop</key>
        <description>Sampler 5 stop, Button: SHIFT &amp; PAD1 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x38</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_play</key>
        <description>Sampler 2 play, Button: PAD2 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x31</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_play</key>
        <description>Sampler 6 play, Button: PAD2 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x31</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_play</key>
        <description>Sampler 2 play, Button: PAD2 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x31</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_play</key>
        <description>Sampler 6 play, Button: PAD2 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x31</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>stop</key>
        <description>Sampler 2 stop, Button: SHIFT &amp; PAD2 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x39</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>stop</key>
        <description>Sampler 6 stop, Button: SHIFT &amp; PAD2 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x39</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>stop</key>
        <description>Sampler 2 stop, Button: SHIFT &amp; PAD2 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x39</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>stop</key>
        <description>Sampler 6 stop, Button: SHIFT &amp; PAD2 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x39</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_play</key>
        <description>Sampler 3 play, Button: PAD3 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_play</key>
        <description>Sampler 7 play, Button: PAD3 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_play</key>
        <description>Sampler 3 play, Button: PAD3 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_play</key>
        <description>Sampler 7 play, Button: PAD3 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>stop</key>
        <description>Sampler 3 stop, Button: SHIFT &amp; PAD3 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x3A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>stop</key>
        <description>Sampler 7 stop, Button: SHIFT &amp; PAD3 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x3A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>stop</key>
        <description>Sampler 3 stop, Button: SHIFT &amp; PAD3 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x3A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>stop</key>
        <description>Sampler 7 stop, Button: SHIFT &amp; PAD3 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x3A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_play</key>
        <description>Sampler4 play, Button: PAD4 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x33</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_play</key>
        <description>Sampler8 play, Button: PAD4 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x33</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_play</key>
        <description>Sampler4 play, Button: PAD4 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x33</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_play</key>
        <description>Sampler8 play, Button: PAD4 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x33</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>stop</key>
        <description>Sampler4 stop, Button: SHIFT &amp; PAD4 (in SAMPLER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x3B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>stop</key>
        <description>Sampler8 stop, Button: SHIFT &amp; PAD4 (in SAMPLER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x3B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>stop</key>
        <description>Sampler4 stop, Button: SHIFT &amp; PAD4 (in SAMPLER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x3B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>stop</key>
        <description>Sampler8 stop, Button: SHIFT &amp; PAD4 (in SAMPLER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x3B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD5 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x34</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler5, Button: PAD5 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x34</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD5 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x34</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler5, Button: PAD5 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x34</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>eject</key>
        <description>Eject Sampler1, Button: SHIFT PAD5 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x3C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>eject</key>
        <description>Eject Sampler5, Button: SHIFT PAD5 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x3C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>eject</key>
        <description>Eject Sampler1, Button: SHIFT PAD5 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x3C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>eject</key>
        <description>Eject Sampler5, Button: SHIFT PAD5 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x3C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD6 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x35</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD6 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x35</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD6 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x35</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler1, Button: PAD6 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x35</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>eject</key>
        <description>Eject Sampler2, Button: SHIFT PAD6 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x3D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>eject</key>
        <description>Eject Sampler6, Button: SHIFT PAD6 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x3D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>eject</key>
        <description>Eject Sampler2, Button: SHIFT PAD6 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x3D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>eject</key>
        <description>Eject Sampler6, Button: SHIFT PAD6 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x3D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler3, Button: PAD7 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x36</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler7, Button: PAD7 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x36</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler3, Button: PAD7 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x36</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler7, Button: PAD7 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x36</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>eject</key>
        <description>Eject Sampler3, Button: SHIFT PAD7 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x3E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>eject</key>
        <description>Eject Sampler7, Button: SHIFT PAD7 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x3E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>eject</key>
        <description>Eject Sampler3, Button: SHIFT PAD7 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x3E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>eject</key>
        <description>Eject Sampler7, Button: SHIFT PAD7 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x3E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler4, Button: PAD8 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x37</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler8, Button: PAD8 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x37</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler4, Button: PAD8 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x37</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>LoadSelectedTrack</key>
        <description>Load selected track in Sampler8, Button: PAD8 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x37</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>eject</key>
        <description>Eject Sampler4, Button: SHIFT PAD8 (in SAMPLER-Mode, Deck 1)</description>
        <status>0x97</status>
        <midino>0x3F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>eject</key>
        <description>Eject Sampler8, Button: SHIFT PAD8 (in SAMPLER-Mode, Deck 2)</description>
        <status>0x98</status>
        <midino>0x3F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>eject</key>
        <description>Eject Sampler4, Button: SHIFT PAD8 (in SAMPLER-Mode, Deck 3)</description>
        <status>0x99</status>
        <midino>0x3F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>eject</key>
        <description>Eject Sampler8, Button: SHIFT PAD8 (in SAMPLER-Mode, Deck 4)</description>
        <status>0x9A</status>
        <midino>0x3F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[1]</key>
        <description>Slicer, Button: PAD1 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[1]</key>
        <description>Slicer, Button: PAD1 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[1]</key>
        <description>Slicer, Button: PAD1 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[1]</key>
        <description>Slicer, Button: PAD1 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x60</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[2]</key>
        <description>Slicer, Button: PAD2 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x61</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[2]</key>
        <description>Slicer, Button: PAD2 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x61</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[2]</key>
        <description>Slicer, Button: PAD2 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x61</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[2]</key>
        <description>Slicer, Button: PAD2 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x61</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[3]</key>
        <description>Slicer, Button: PAD3 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x62</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[3]</key>
        <description>Slicer, Button: PAD3 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x62</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[3]</key>
        <description>Slicer, Button: PAD3 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x62</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[3]</key>
        <description>Slicer, Button: PAD3 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x62</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[4]</key>
        <description>Slicer, Button: PAD4 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[4]</key>
        <description>Slicer, Button: PAD4 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[4]</key>
        <description>Slicer, Button: PAD4 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[4]</key>
        <description>Slicer, Button: PAD4 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x63</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[5]</key>
        <description>Slicer, Button: PAD5 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[5]</key>
        <description>Slicer, Button: PAD5 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[5]</key>
        <description>Slicer, Button: PAD5 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[5]</key>
        <description>Slicer, Button: PAD5 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x64</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[6]</key>
        <description>Slicer, Button: PAD6 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[6]</key>
        <description>Slicer, Button: PAD6 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[6]</key>
        <description>Slicer, Button: PAD6 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[6]</key>
        <description>Slicer, Button: PAD6 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x65</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[7]</key>
        <description>Slicer, Button: PAD7 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x66</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[7]</key>
        <description>Slicer, Button: PAD7 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x66</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[7]</key>
        <description>Slicer, Button: PAD7 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x66</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[7]</key>
        <description>Slicer, Button: PAD7 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x66</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PioneerDDJSB3.padForDeck[1].slicerButtons[8]</key>
        <description>Slicer, Button: PAD8 (in SLICER-Mode, Deck 1 active)</description>
        <status>0x97</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PioneerDDJSB3.padForDeck[2].slicerButtons[8]</key>
        <description>Slicer, Button: PAD8 (in SLICER-Mode, Deck 2 active)</description>
        <status>0x98</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>PioneerDDJSB3.padForDeck[3].slicerButtons[8]</key>
        <description>Slicer, Button: PAD8 (in SLICER-Mode, Deck 3 active)</description>
        <status>0x99</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>PioneerDDJSB3.padForDeck[4].slicerButtons[8]</key>
        <description>Slicer, Button: PAD8 (in SLICER-Mode, Deck 4 active)</description>
        <status>0x9A</status>
        <midino>0x67</midino>
        <options>
          <script-binding/>
        </options>
      </control>
    </controls>
    <outputs/>
  </controller>
</MixxxControllerPreset>
