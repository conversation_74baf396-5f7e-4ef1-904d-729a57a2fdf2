<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="2.1.0+" schemaVersion="1">
  <info>
    <name>Vestax VCI-100MKII</name>
    <author><PERSON><PERSON></author>
    <description>2018-4-15</description>
    <manual>vestax_vci_100mkii</manual>
  </info>
  <controller id="VCI-100MKII">
    <scriptfiles>
      <file functionprefix="VCI102" filename="Vestax-VCI-100MKII-scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB4</status>
        <midino>0x10</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB4</status>
        <midino>0x20</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headGain</key>
        <status>0xB4</status>
        <midino>0x12</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0xB4</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>gain</key>
        <status>0xB4</status>
        <midino>0x14</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.setShift</key>
        <status>0x94</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.setShift</key>
        <status>0x94</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>GoToItem</key>
        <status>0x94</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Library]</group>
        <key>VCI102.selectPrev</key>
        <status>0x94</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>VCI102.selectNext</key>
        <status>0x94</status>
        <midino>0x38</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.LoadSelectedTrack</key>
        <status>0x91</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.LoadSelectedTrack</key>
        <status>0x92</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.LoadSelectedTrack</key>
        <status>0x93</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.scratchEnable</key>
        <status>0x90</status>
        <midino>0x3B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.scratchEnable</key>
        <status>0x91</status>
        <midino>0x3B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.scratchEnable</key>
        <status>0x92</status>
        <midino>0x3B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.scratchEnable</key>
        <status>0x93</status>
        <midino>0x3B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.jog</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.jog</key>
        <status>0xB1</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.jog</key>
        <status>0xB2</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.jog</key>
        <status>0xB3</status>
        <midino>0x10</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.rateMSB</key>
        <status>0xB0</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.rateMSB</key>
        <status>0xB1</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.rateMSB</key>
        <status>0xB2</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.rateMSB</key>
        <status>0xB3</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.rateLSB</key>
        <status>0xB0</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.rateLSB</key>
        <status>0xB1</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.rateLSB</key>
        <status>0xB2</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.rateLSB</key>
        <status>0xB3</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.rateQuantizedMSB</key>
        <status>0xB0</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.rateQuantizedMSB</key>
        <status>0xB1</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.rateQuantizedMSB</key>
        <status>0xB2</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.rateQuantizedMSB</key>
        <status>0xB3</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.rateQuantizedLSB</key>
        <status>0xB0</status>
        <midino>0x2E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.rateQuantizedLSB</key>
        <status>0xB1</status>
        <midino>0x2E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.rateQuantizedLSB</key>
        <status>0xB2</status>
        <midino>0x2E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.rateQuantizedLSB</key>
        <status>0xB3</status>
        <midino>0x2E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x1C</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB1</status>
        <midino>0x16</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>volume</key>
        <status>0xB2</status>
        <midino>0x1C</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>volume</key>
        <status>0xB3</status>
        <midino>0x16</midino>
        <options>
          <fourteen-bit-msb/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x2C</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB1</status>
        <midino>0x26</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>volume</key>
        <status>0xB2</status>
        <midino>0x2C</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>volume</key>
        <status>0xB3</status>
        <midino>0x26</midino>
        <options>
          <fourteen-bit-lsb/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB0</status>
        <midino>0x11</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB1</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB2</status>
        <midino>0x11</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter3</key>
        <status>0xB3</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB0</status>
        <midino>0x12</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB1</status>
        <midino>0x1C</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB2</status>
        <midino>0x12</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter2</key>
        <status>0xB3</status>
        <midino>0x1C</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB0</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB1</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB2</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>parameter1</key>
        <status>0xB3</status>
        <midino>0x13</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.pitch</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.pitch</key>
        <status>0xB1</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.pitch</key>
        <status>0xB2</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.pitch</key>
        <status>0xB3</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x15</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB1</status>
        <midino>0x15</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pregain</key>
        <status>0xB2</status>
        <midino>0x15</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pregain</key>
        <status>0xB3</status>
        <midino>0x15</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.parameter1</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.parameter1</key>
        <status>0xB1</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.parameter1</key>
        <status>0xB2</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.parameter1</key>
        <status>0xB3</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.parameter2</key>
        <status>0xB0</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.parameter2</key>
        <status>0xB1</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.parameter2</key>
        <status>0xB2</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.parameter2</key>
        <status>0xB3</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.parameter3</key>
        <status>0xB0</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.parameter3</key>
        <status>0xB1</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.parameter3</key>
        <status>0xB2</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.parameter3</key>
        <status>0xB3</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.parameter4</key>
        <status>0xB0</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.parameter4</key>
        <status>0xB1</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.parameter4</key>
        <status>0xB2</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.parameter4</key>
        <status>0xB3</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel1]]</group>
        <key>VCI102.super1</key>
        <status>0xB0</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]]</group>
        <key>VCI102.super1</key>
        <status>0xB1</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel3]]</group>
        <key>VCI102.super1</key>
        <status>0xB2</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel4]]</group>
        <key>VCI102.super1</key>
        <status>0xB3</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.no_slot</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.no_slot</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.no_slot</key>
        <status>0x92</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.no_slot</key>
        <status>0x93</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.next_slot</key>
        <status>0x90</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.next_slot</key>
        <status>0x91</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.next_slot</key>
        <status>0x92</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.next_slot</key>
        <status>0x93</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x22</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x91</status>
        <midino>0x22</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>play</key>
        <status>0x92</status>
        <midino>0x22</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>play</key>
        <status>0x93</status>
        <midino>0x22</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x91</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>reverse</key>
        <status>0x92</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>reverse</key>
        <status>0x93</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0x90</status>
        <midino>0x23</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0x91</status>
        <midino>0x23</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_default</key>
        <status>0x92</status>
        <midino>0x23</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_default</key>
        <status>0x93</status>
        <midino>0x23</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <status>0x90</status>
        <midino>0x2A</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <status>0x91</status>
        <midino>0x2A</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_activate</key>
        <status>0x92</status>
        <midino>0x2A</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_activate</key>
        <status>0x93</status>
        <midino>0x2A</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_clear</key>
        <status>0x90</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_clear</key>
        <status>0x91</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_clear</key>
        <status>0x92</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_clear</key>
        <status>0x93</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <status>0x90</status>
        <midino>0x2B</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <status>0x91</status>
        <midino>0x2B</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_activate</key>
        <status>0x92</status>
        <midino>0x2B</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_activate</key>
        <status>0x93</status>
        <midino>0x2B</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_clear</key>
        <status>0x90</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_clear</key>
        <status>0x91</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_clear</key>
        <status>0x92</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_clear</key>
        <status>0x93</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>sync_enabled</key>
        <status>0x91</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>sync_enabled</key>
        <status>0x92</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>sync_enabled</key>
        <status>0x93</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beats_translate_curpos</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beats_translate_curpos</key>
        <status>0x91</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beats_translate_curpos</key>
        <status>0x92</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beats_translate_curpos</key>
        <status>0x93</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0x90</status>
        <midino>0x2D</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0x91</status>
        <midino>0x2D</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>keylock</key>
        <status>0x92</status>
        <midino>0x2D</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>keylock</key>
        <status>0x93</status>
        <midino>0x2D</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>quantize</key>
        <status>0x90</status>
        <midino>0x4D</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>quantize</key>
        <status>0x91</status>
        <midino>0x4D</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>quantize</key>
        <status>0x92</status>
        <midino>0x4D</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>quantize</key>
        <status>0x93</status>
        <midino>0x4D</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.loop</key>
        <status>0x90</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.loop</key>
        <status>0x91</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.loop</key>
        <status>0x92</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.loop</key>
        <status>0x93</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.reloop</key>
        <status>0x90</status>
        <midino>0x46</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.reloop</key>
        <status>0x91</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.reloop</key>
        <status>0x92</status>
        <midino>0x46</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.reloop</key>
        <status>0x93</status>
        <midino>0x53</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.loop_halve</key>
        <status>0x90</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.loop_halve</key>
        <status>0x91</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.loop_halve</key>
        <status>0x92</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.loop_halve</key>
        <status>0x93</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_backward</key>
        <status>0x90</status>
        <midino>0x53</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_backward</key>
        <status>0x91</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_backward</key>
        <status>0x92</status>
        <midino>0x53</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_backward</key>
        <status>0x93</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.loop_double</key>
        <status>0x90</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.loop_double</key>
        <status>0x91</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.loop_double</key>
        <status>0x92</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.loop_double</key>
        <status>0x93</status>
        <midino>0x26</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatjump_forward</key>
        <status>0x90</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatjump_forward</key>
        <status>0x91</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatjump_forward</key>
        <status>0x92</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatjump_forward</key>
        <status>0x93</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x2C</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect1]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x2C</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect1]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>next_effect</key>
        <status>0x90</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>next_effect</key>
        <status>0x91</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect1]</group>
        <key>next_effect</key>
        <status>0x92</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect1]</group>
        <key>next_effect</key>
        <status>0x93</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x25</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x25</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect2]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x25</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect2]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x25</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>next_effect</key>
        <status>0x90</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>next_effect</key>
        <status>0x91</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect2]</group>
        <key>next_effect</key>
        <status>0x92</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect2]</group>
        <key>next_effect</key>
        <status>0x93</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x27</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x27</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect3]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x27</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect3]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x27</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>next_effect</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>next_effect</key>
        <status>0x91</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3_Effect3]</group>
        <key>next_effect</key>
        <status>0x92</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4_Effect3]</group>
        <key>next_effect</key>
        <status>0x93</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>VCI102.mix_toggle</key>
        <status>0x90</status>
        <midino>0x28</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>VCI102.mix_toggle</key>
        <status>0x91</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>VCI102.mix_toggle</key>
        <status>0x92</status>
        <midino>0x28</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>VCI102.mix_toggle</key>
        <status>0x93</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>next_chain</key>
        <status>0x90</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>next_chain</key>
        <status>0x91</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>next_chain</key>
        <status>0x92</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>next_chain</key>
        <status>0x93</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.setDeck</key>
        <status>0x90</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.setDeck</key>
        <status>0x91</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.setDeck</key>
        <status>0x90</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.setDeck</key>
        <status>0x91</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pfl</key>
        <status>0x92</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pfl</key>
        <status>0x93</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VCI102.solo</key>
        <status>0x90</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VCI102.solo</key>
        <status>0x91</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>VCI102.solo</key>
        <status>0x92</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>VCI102.solo</key>
        <status>0x93</status>
        <midino>0x52</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>slip_enabled</key>
        <status>0x90</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>slip_enabled</key>
        <status>0x91</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>slip_enabled</key>
        <status>0x92</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>slip_enabled</key>
        <status>0x93</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x3A</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x3A</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x3A</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x3A</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x4F</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x4F</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x4F</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit3]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x4F</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel1]_enable</key>
        <status>0x90</status>
        <midino>0x50</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel2]_enable</key>
        <status>0x91</status>
        <midino>0x50</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel3]_enable</key>
        <status>0x92</status>
        <midino>0x50</midino>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit4]</group>
        <key>group_[Channel4]_enable</key>
        <status>0x93</status>
        <midino>0x50</midino>
      </control>
    </controls>
    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>play_indicator</key>
        <status>0x90</status>
        <midino>0x22</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play_indicator</key>
        <status>0x91</status>
        <midino>0x22</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>play_indicator</key>
        <status>0x92</status>
        <midino>0x22</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>play_indicator</key>
        <status>0x93</status>
        <midino>0x22</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>cue_indicator</key>
        <status>0x90</status>
        <midino>0x23</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>cue_indicator</key>
        <status>0x91</status>
        <midino>0x23</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>cue_indicator</key>
        <status>0x92</status>
        <midino>0x23</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>cue_indicator</key>
        <status>0x93</status>
        <midino>0x23</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_1_enabled</key>
        <status>0x90</status>
        <midino>0x2A</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_1_enabled</key>
        <status>0x91</status>
        <midino>0x2A</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_1_enabled</key>
        <status>0x92</status>
        <midino>0x2A</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_1_enabled</key>
        <status>0x93</status>
        <midino>0x2A</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_2_enabled</key>
        <status>0x90</status>
        <midino>0x2B</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_2_enabled</key>
        <status>0x91</status>
        <midino>0x2B</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_2_enabled</key>
        <status>0x92</status>
        <midino>0x2B</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_2_enabled</key>
        <status>0x93</status>
        <midino>0x2B</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>sync_enabled</key>
        <status>0x90</status>
        <midino>0x24</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>sync_enabled</key>
        <status>0x91</status>
        <midino>0x24</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>sync_enabled</key>
        <status>0x92</status>
        <midino>0x24</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>sync_enabled</key>
        <status>0x93</status>
        <midino>0x24</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_enabled</key>
        <status>0x90</status>
        <midino>0x26</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_enabled</key>
        <status>0x91</status>
        <midino>0x33</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_enabled</key>
        <status>0x92</status>
        <midino>0x26</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_enabled</key>
        <status>0x93</status>
        <midino>0x33</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x2C</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x28</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit3_Effect1]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x2C</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit4_Effect1]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x28</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit1_Effect2]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit2_Effect2]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit3_Effect2]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x25</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit4_Effect2]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x25</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit1_Effect3]</group>
        <key>enabled</key>
        <status>0x90</status>
        <midino>0x27</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit2_Effect3]</group>
        <key>enabled</key>
        <status>0x91</status>
        <midino>0x27</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit3_Effect3]</group>
        <key>enabled</key>
        <status>0x92</status>
        <midino>0x27</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[EffectRack1_EffectUnit4_Effect3]</group>
        <key>enabled</key>
        <status>0x93</status>
        <midino>0x27</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x36</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x36</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>pfl</key>
        <status>0x92</status>
        <midino>0x36</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>pfl</key>
        <status>0x93</status>
        <midino>0x36</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>slip_enabled</key>
        <status>0x90</status>
        <midino>0x39</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>slip_enabled</key>
        <status>0x91</status>
        <midino>0x39</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>slip_enabled</key>
        <status>0x92</status>
        <midino>0x39</midino>
        <minimum>1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>slip_enabled</key>
        <status>0x93</status>
        <midino>0x39</midino>
        <minimum>1</minimum>
      </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
