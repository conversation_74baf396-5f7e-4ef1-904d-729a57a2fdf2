<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.6.2+">
  <info>
    <name>MIDI Keyboard</name>
    <author></author>
    <description>Generic MIDI Keyboard Mapping</description>
  </info>
  <controller id="Midi-Keyboard" port="Port">
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x01</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x29</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x29</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x2d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x2d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x2e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x2e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0x90</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0x80</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x41</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x41</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x4c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x4c</midino>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
