<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.8.0">
    <info>
        <name>Hercules DJ Control MP3</name>
        <author>Vitt<PERSON></author>
        <description>Complete mapping for Hercules DJ Control MP3.</description>
        <manual>hercules_djcontrol_mp3</manual>
    </info>
    <controller id="Hercules DJ Control MP3 MIDI">
        <scriptfiles>
            <file filename="Hercules-DJ-Control-MP3-scripts.js"
                functionprefix="HerculesMp3" />
        </scriptfiles>

        <controls>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options />
            </control>

            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>back</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>back</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>fwd</key>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>fwd</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x32</midino>
                <options />
            </control>

            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x033</midino>
                <options />
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.pitchpot</key>
                <status>0xB0</status>
                <midino>0x34</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.pitchpot</key>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.jog_wheel</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <status>0xB0</status>
                <midino>0x2E</midino>
                <options />
            </control>

            <control>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options />
            </control>

            <control>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <status>0xB0</status>
                <midino>0x2F</midino>
                <options />
            </control>

            <control>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <status>0xB0</status>
                <midino>0x2C</midino>
                <options />
            </control>

            <control>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options />
            </control>

            <control>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options />
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.mode</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.mode</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.fx</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <Button />
                </options>
            </control>

            <control>
                <group>[Master]</group>
                <key>HerculesMp3.joystick</key>
                <status>0xb0</status>
                <midino>0x39</midino>
                <options>
                    <Script-Binding />
                </options>
            </control>

            <control>
                <group>[Channel1]</group>
                <key>HerculesMp3.loadSelectedTrack</key>
                <status>0xB0</status>
                <midino>0x1B</midino>
                <options>
                    <Script-Binding />
                </options>

            </control>

            <control>
                <group>[Channel2]</group>
                <key>HerculesMp3.loadSelectedTrack</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <Script-Binding />
                </options>

            </control>

<!--            <control>-->
<!--                <group>[Channel1]</group>-->
<!--                <key>keylock</key>-->
<!--                <status>0xB0</status>-->
<!--                <midino>0x16</midino>-->
<!--                <options>-->
<!--                    <Button />-->
<!--                </options>-->
<!--            </control>-->
<!---->
<!--            <control>-->
<!--                <group>[Channel2]</group>-->
<!--                <key>keylock</key>-->
<!--                <status>0xB0</status>-->
<!--                <midino>0x1A</midino>-->
<!--                <options>-->
<!--                    <Button />-->
<!--                </options>-->
<!--            </control>-->


        </controls>
        <outputs>

        </outputs>
    </controller>
</MixxxMIDIPreset>
