<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Denon MC6000MK2</name>
        <author>Uwe Klotz a/k/a tapir</author>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=6251</forums>
        <manual>denon_mc6000mk2</manual>
    </info>
    <controller id="MC6000MK2">
        <scriptfiles>
            <file functionprefix="" filename="lodash.mixxx.js"/>
            <file functionprefix="" filename="midi-components-0.0.js"/>
            <file functionprefix="DenonMC6000MK2" filename="Denon-MC6000MK2-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[4].input</key>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[4].input</key>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[4].input</key>
                <status>0x92</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[4].input</key>
                <status>0x93</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[1].input</key>
                <status>0x80</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldRightSide.recvFilterButton</key>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[3].input</key>
                <status>0x80</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[2].input</key>
                <status>0x80</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[2].input</key>
                <status>0x81</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[2].input</key>
                <status>0x82</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[2].input</key>
                <status>0x83</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x80</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x81</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x82</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x83</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldLeftSide.recvFilterButton</key>
                <status>0x80</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[3].input</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[2].input</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[2].input</key>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[2].input</key>
                <status>0x92</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[2].input</key>
                <status>0x93</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x91</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvAreaButton</key>
                <status>0x80</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x92</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogTouchVinyl</key>
                <status>0x93</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldLeftSide.recvFilterButton</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[2].input</key>
                <status>0x80</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvAreaButton</key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvTapButton</key>
                <status>0x80</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogSpinVinyl</key>
                <status>0xB0</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogSpinVinyl</key>
                <status>0xB1</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogSpinVinyl</key>
                <status>0xB2</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x80</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogSpinVinyl</key>
                <status>0xB3</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x81</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x82</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x83</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvTapButton</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.playButton.input</key>
                <status>0x80</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.playButton.input</key>
                <status>0x81</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.playButton.input</key>
                <status>0x82</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.playButton.input</key>
                <status>0x83</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvBendPlusButton</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.effectFocusButton.input</key>
                <status>0x80</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.oldRightSide.recvDeckButton</key>
                <status>0x93</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.playButton.input</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.playButton.input</key>
                <status>0x91</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.playButton.input</key>
                <status>0x92</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.playButton.input</key>
                <status>0x93</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.oldRightSide.recvDeckButton</key>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.effectFocusButton.input</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvKeyLockButton</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvKeyLockButton</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvKeyLockButton</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvKeyLockButton</key>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvXfaderAssignRightButton</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvXfaderContourKnob</key>
                <status>0xB0</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvVinylButton</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvVinylButton</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvVinylButton</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvVinylButton</key>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvXfaderAssignLeftButton</key>
                <status>0x90</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB0</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvCueMixButton</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvXfaderAssignThruButton</key>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loopOutButtonInput</key>
                <status>0x92</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loopOutButtonInput</key>
                <status>0x93</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loopOutButtonInput</key>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loopOutButtonInput</key>
                <status>0x91</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loopOutButtonInput</key>
                <status>0x82</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loopOutButtonInput</key>
                <status>0x83</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loopOutButtonInput</key>
                <status>0x80</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loopOutButtonInput</key>
                <status>0x81</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loopInButtonInput</key>
                <status>0x82</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loopInButtonInput</key>
                <status>0x83</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loopInButtonInput</key>
                <status>0x80</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loopInButtonInput</key>
                <status>0x81</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loopInButtonInput</key>
                <status>0x92</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loopInButtonInput</key>
                <status>0x93</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loopInButtonInput</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loopInButtonInput</key>
                <status>0x91</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvXfaderAssignThruButton</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x80</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x81</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x82</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x83</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvXfaderAssignRightButton</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvXfaderAssignLeftButton</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x90</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x91</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x92</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvLoopCutPlusButton</key>
                <status>0x93</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvPanelButton</key>
                <status>0x80</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loadButton.input</key>
                <status>0x80</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loadButton.input</key>
                <status>0x81</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvPanelButton</key>
                <status>0x90</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftSide.shiftButtonInput</key>
                <status>0x80</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftSide.shiftButtonInput</key>
                <status>0x81</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvFwdButton</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.loadButton.input</key>
                <status>0x90</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.loadButton.input</key>
                <status>0x91</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x80</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftSide.shiftButtonInput</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x82</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftSide.shiftButtonInput</key>
                <status>0x91</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldLeftSide.recvFilterKnob</key>
                <status>0xB0</status>
                <midino>0x66</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x80</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x82</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x92</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x92</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x80</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x81</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x82</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x83</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvAutoLoopButton</key>
                <status>0x90</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvAutoLoopButton</key>
                <status>0x91</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[3].input</key>
                <status>0x80</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvAutoLoopButton</key>
                <status>0x92</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[3].input</key>
                <status>0x81</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvAutoLoopButton</key>
                <status>0x93</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[3].input</key>
                <status>0x82</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x91</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[3].input</key>
                <status>0x83</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[2].input</key>
                <status>0x80</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x92</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogTouch</key>
                <status>0x93</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.dryWetKnob.input</key>
                <status>0xB0</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[1].input</key>
                <status>0x80</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[1].input</key>
                <status>0x81</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[1].input</key>
                <status>0x82</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[1].input</key>
                <status>0x83</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x80</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x81</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x82</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x83</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[3].input</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[3].input</key>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[1].input</key>
                <status>0x80</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[3].input</key>
                <status>0x92</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[3].input</key>
                <status>0x93</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.rightSide.effectUnit.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.dryWetKnob.input</key>
                <status>0xB0</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[1].input</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[1].input</key>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[3].input</key>
                <status>0x80</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[1].input</key>
                <status>0x92</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[1].input</key>
                <status>0x93</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x91</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvViewButton</key>
                <status>0x80</status>
                <midino>0x4C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x92</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvCensorButton</key>
                <status>0x93</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvTrackSelectKnob</key>
                <status>0xB0</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.enableButtons[3].input</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvViewButton</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvJogSpin</key>
                <status>0xB0</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvJogSpin</key>
                <status>0xB1</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvJogSpin</key>
                <status>0xB2</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x80</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvJogSpin</key>
                <status>0xB3</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x81</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x82</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x83</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvTapButton</key>
                <status>0x80</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvBendMinusButton</key>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvTapButton</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.cueButton.input</key>
                <status>0x80</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.cueButton.input</key>
                <status>0x81</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.cueButton.input</key>
                <status>0x82</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.cueButton.input</key>
                <status>0x83</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.effectFocusButton.input</key>
                <status>0x80</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.oldLeftSide.recvDeckButton</key>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.cueButton.input</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.cueButton.input</key>
                <status>0x91</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.cueButton.input</key>
                <status>0x92</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.cueButton.input</key>
                <status>0x93</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvCueMixButton</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.leftSide.effectUnit.effectFocusButton.input</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xE0</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate</key>
                <status>0xE1</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xE2</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvCueMixButton</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate</key>
                <status>0xE3</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvXfaderAssignThruButton</key>
                <status>0x90</status>
                <midino>0x3E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headGain</key>
                <status>0xB0</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.oldLeftSide.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvXfaderAssignRightButton</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvCueMixButton</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvXfaderAssignLeftButton</key>
                <status>0x90</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvXfaderAssignRightButton</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.syncButton.input</key>
                <status>0x80</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.syncButton.input</key>
                <status>0x81</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.syncButton.input</key>
                <status>0x82</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.syncButton.input</key>
                <status>0x83</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvXfaderAssignLeftButton</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x80</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x81</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x82</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x83</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvXfaderAssignThruButton</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.syncButton.input</key>
                <status>0x90</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.syncButton.input</key>
                <status>0x91</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.syncButton.input</key>
                <status>0x92</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.syncButton.input</key>
                <status>0x93</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvBackButton</key>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x90</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x91</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvListButton</key>
                <status>0x80</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x92</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.recvLoopCutMinusButton</key>
                <status>0x93</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loadButton.input</key>
                <status>0x82</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loadButton.input</key>
                <status>0x83</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvListButton</key>
                <status>0x90</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightSide.shiftButtonInput</key>
                <status>0x82</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightSide.shiftButtonInput</key>
                <status>0x83</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.loadButton.input</key>
                <status>0x92</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.loadButton.input</key>
                <status>0x93</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.recvTrackSelectButton</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x80</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x82</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightSide.shiftButtonInput</key>
                <status>0x92</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightSide.shiftButtonInput</key>
                <status>0x93</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldRightSide.recvFilterKnob</key>
                <status>0xB0</status>
                <midino>0x67</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x80</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x82</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DenonMC6000MK2.leftDeck1.hotcueButtons[4].input</key>
                <status>0x80</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x92</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DenonMC6000MK2.leftDeck3.hotcueButtons[4].input</key>
                <status>0x81</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.oldRightSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DenonMC6000MK2.rightDeck2.hotcueButtons[4].input</key>
                <status>0x82</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.rightDeck4.hotcueButtons[4].input</key>
                <status>0x83</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>DenonMC6000MK2.oldRightSide.recvFilterButton</key>
                <status>0x80</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>DenonMC6000MK2.recvSamplerButton</key>
                <status>0x92</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DenonMC6000MK2.oldLeftSide.efxUnit.recvDeckButton</key>
                <status>0x90</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x53</midino>
                <on>0x01</on>
                <minimum>0.2</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x54</midino>
                <on>0x01</on>
                <minimum>0.4</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x55</midino>
                <on>0x01</on>
                <minimum>0.6</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x56</midino>
                <on>0x01</on>
                <minimum>0.75</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x57</midino>
                <on>0x01</on>
                <minimum>0.85</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>vu_meter</key>
                <status>0xB1</status>
                <midino>0x58</midino>
                <on>0x01</on>
                <minimum>0.92</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x53</midino>
                <on>0x01</on>
                <minimum>0.2</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x54</midino>
                <on>0x01</on>
                <minimum>0.4</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x55</midino>
                <on>0x01</on>
                <minimum>0.6</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x56</midino>
                <on>0x01</on>
                <minimum>0.75</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x57</midino>
                <on>0x01</on>
                <minimum>0.85</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>vu_meter</key>
                <status>0xB0</status>
                <midino>0x58</midino>
                <on>0x01</on>
                <minimum>0.92</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>peak_indicator</key>
                <status>0xB0</status>
                <midino>0x59</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x53</midino>
                <on>0x01</on>
                <minimum>0.2</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x54</midino>
                <on>0x01</on>
                <minimum>0.4</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x55</midino>
                <on>0x01</on>
                <minimum>0.6</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x56</midino>
                <on>0x01</on>
                <minimum>0.75</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x57</midino>
                <on>0x01</on>
                <minimum>0.85</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>vu_meter</key>
                <status>0xB3</status>
                <midino>0x58</midino>
                <on>0x01</on>
                <minimum>0.92</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>peak_indicator</key>
                <status>0xB1</status>
                <midino>0x59</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x53</midino>
                <on>0x01</on>
                <minimum>0.2</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x54</midino>
                <on>0x01</on>
                <minimum>0.4</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x55</midino>
                <on>0x01</on>
                <minimum>0.6</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x56</midino>
                <on>0x01</on>
                <minimum>0.75</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x57</midino>
                <on>0x01</on>
                <minimum>0.85</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>vu_meter</key>
                <status>0xB2</status>
                <midino>0x58</midino>
                <on>0x01</on>
                <minimum>0.92</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>peak_indicator</key>
                <status>0xB2</status>
                <midino>0x59</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>peak_indicator</key>
                <status>0xB3</status>
                <midino>0x59</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
