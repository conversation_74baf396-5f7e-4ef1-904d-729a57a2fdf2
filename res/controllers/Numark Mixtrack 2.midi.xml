<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Numark MixTrack (Pro) II</name>
        <author><PERSON>NOW<PERSON>, <PERSON><PERSON>, nino<PERSON>, special thanks to <PERSON> and Rice<PERSON>ws (<EMAIL>)</author>
        <description>Version v2.0</description>
        <manual>numark_mixtrack_pro_ii</manual>
    </info>
    <controller id="MixTrack">
        <scriptfiles>
            <file functionprefix="NumarkMixTrackII" filename="Numark-Mixtrack-2-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x6F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fx1_or_auto1</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.pitch_bend_down_or_keylock</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x6E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_toggle</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.play</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x6D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x57</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x6C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>NumarkMixTrackII.selectKnob</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.jogWheel</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x6B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reloop_toggle</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x80</status>
                <midino>0x52</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x6A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.jogWheel</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x80</status>
                <midino>0x51</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x69</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.cue</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x67</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.play</key>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x66</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.toggleScratchMode</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x65</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.loop_halve</key>
                <status>0x90</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.wheelTouch</key>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.loop_halve</key>
                <status>0x90</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.wheelTouch</key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.shift</key>
                <status>0x90</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play_stutter</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>NumarkMixTrackII.toggleDirectoryMode</key>
                <status>0x90</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.shift</key>
                <status>0x90</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>NumarkMixTrackII.toggleDirectoryMode</key>
                <status>0x90</status>
                <midino>0x76</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.tap_or_auto16</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play_stutter</key>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fx3_or_auto4</key>
                <status>0x90</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.cue</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.toggleDeleteHotcueMode</key>
                <status>0x90</status>
                <midino>0x74</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.beatsKnob</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fx2_or_auto2</key>
                <status>0x90</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.toggleScratchMode</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x73</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.beatsKnob</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fx1_or_auto1</key>
                <status>0x90</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headVolume</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x72</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.tap_or_auto16</key>
                <status>0x90</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.pitch_bend_up_or_range</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.hotcue</key>
                <status>0x90</status>
                <midino>0x71</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fx3_or_auto4</key>
                <status>0x90</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.pitch_bend_down_or_keylock</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.toggleDeleteHotcueMode</key>
                <status>0x90</status>
                <midino>0x70</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NumarkMixTrackII.fxKnobs</key>
                <status>0xB0</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.fx2_or_auto2</key>
                <status>0x90</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NumarkMixTrackII.pitch_bend_up_or_range</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x6E</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <on>0x64</on>
                <maximum>0</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_end_position</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>-1</maximum>
                <minimum>-1</minimum>
            </output>
            <output>
                <group>[Sampler6]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x6A</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x66</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler7]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x6B</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x5F</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler3]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x67</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_start_position</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>-1</maximum>
                <minimum>-1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beat_active</key>
                <status>0x90</status>
                <midino>0x5C</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x5B</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x3B</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler8]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x6C</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x73</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler4]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x68</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x5E</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x71</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_start_position</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>-1</maximum>
                <minimum>-1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beat_active</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x72</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x5A</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x6F</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_end_position</key>
                <status>0x90</status>
                <midino>0x57</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>-1</maximum>
                <minimum>-1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <on>0x64</on>
                <maximum>0</maximum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x5D</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler5]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x69</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Sampler1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x65</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x6D</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x00</on>
                <off>0x64</off>
                <maximum>0.1</maximum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
