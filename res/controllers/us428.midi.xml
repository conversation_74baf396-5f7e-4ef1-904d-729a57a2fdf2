<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.6.2+">
  <info>
    <name>Tascam US-428</name>
    <author>Auto-converted by <PERSON>jesta's PHP script</author>
    <description>Automatic conversion of the file us428.midi.xml for Mixxx 1.6.2</description>
    <manual>tascam_us_428</manual>
  </info>
  <controller id="us428" port="Port">
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0xa4</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headVolume</key>
        <status>0xB0</status>
        <midino>0x4c</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0xB0</status>
        <midino>0x4d</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x48</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4a</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x3e</midino>
      </control>
       <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x3f</midino>
      </control>
       <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x3f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x80</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x66</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x66</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x6e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x6e</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x60</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>slowback</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>slowback</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>slowfwd</key>
        <status>0x90</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>slowfwd</key>
        <status>0x80</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x41</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x49</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4b</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>preplay</key>
        <status>0x91</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>preplay</key>
        <status>0x81</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x91</status>
        <midino>0x3f</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x81</status>
        <midino>0x3f</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x91</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x81</status>
        <midino>0x40</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x33</midino>
      </control>
       <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x81</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x67</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x67</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x6f</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x6f</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>playposition</key>
        <status>0xB1</status>
        <midino>0x60</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>slowback</key>
        <status>0x91</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>slowback</key>
        <status>0x81</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>slowfwd</key>
        <status>0x91</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>slowfwd</key>
        <status>0x81</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB0</status>
        <midino>0x50</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0xB0</status>
        <midino>0x51</midino>
        <options>

      </options>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB0</status>
        <midino>0x52</midino>
        <options>

      </options>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
