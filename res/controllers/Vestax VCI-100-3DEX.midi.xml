<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.8.0+">
  <info>
    <name>Vestax VCI-100 (MixVibes 3DEX Editon)</name>
    <author><PERSON> ed<PERSON></author>
    <description>Preliminary controller mapping for Vestax VCI-100 with scripts</description>
    <manual>vestax_vci_100_mixvibes_3dex_edition</manual>
  </info>
  <controller id="Vestax VCI-100-3DEX ">
    <scriptfiles>
       <file filename="Vestax-VCI-100-3DEX-scripts.js" functionprefix="VestaxVCI1003DEX"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x08</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x07</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headVolume</key>
        <status>0xB0</status>
        <midino>0x58</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0xB0</status>
        <midino>0x59</midino>
      </control>


      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopin</key>
        <status>0x90</status>
        <midino>0x62</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopin</key>
        <status>0x80</status>
        <midino>0x62</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopout</key>
        <status>0x90</status>
        <midino>0x63</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopout</key>
        <status>0x80</status>
        <midino>0x63</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.reloop_exit</key>
        <status>0x90</status>
        <midino>0x64</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.reloop_exit</key>
        <status>0x80</status>
        <midino>0x64</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>LoadSelectedIntoFirstStopped</key>
        <status>0x90</status>
        <midino>0x5E</midino>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.selectDeck1</key>
        <status>0x90</status>
        <midino>0x67</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.selectDeck1</key>
        <status>0x80</status>
        <midino>0x67</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.selectDeck2</key>
        <status>0x90</status>
        <midino>0x68</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.selectDeck2</key>
        <status>0x80</status>
        <midino>0x68</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopMinus</key>
        <status>0x90</status>
        <midino>0x65</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopMinus</key>
        <status>0x80</status>
        <midino>0x65</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopPlus</key>
        <status>0x90</status>
        <midino>0x66</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI1003DEX.loopPlus</key>
        <status>0x80</status>
        <midino>0x66</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.pregainreset1</key>
        <status>0x90</status>
        <midino>0x0E</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x15</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.filterHighreset1</key>
        <status>0x90</status>
        <midino>0x0F</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x16</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.filterMidreset1</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x17</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.filterLowreset1</key>
        <status>0x90</status>
        <midino>0x11</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0e</midino>
        <options>
          <invert/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.jog_touch1</key>
        <status>0x90</status>
        <midino>0x30</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
          <group>[Channel1]</group>
          <key>VestaxVCI1003DEX.jog_wheel1</key>
          <status>0xB0</status>
          <midino>0x10</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
          <group>[Channel1]</group>
          <key>VestaxVCI1003DEX.jog_wheel_seek1</key>
          <status>0xB0</status>
          <midino>0x12</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.play1</key>
        <status>0x90</status>
        <midino>0x32</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.play1</key>
        <status>0x80</status>
        <midino>0x32</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.cue1</key>
        <status>0x90</status>
        <midino>0x33</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.cue1</key>
        <status>0x80</status>
        <midino>0x33</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>start_stop</key>
        <status>0x80</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.autoloop1</key>
        <status>0x90</status>
        <midino>0x35</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.autoloop1</key>
        <status>0x80</status>
        <midino>0x35</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.pfl1</key>
        <status>0x90</status>
        <midino>0x48</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.pfl1</key>
        <status>0x80</status>
        <midino>0x48</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.reverse1</key>
        <status>0x90</status>
        <midino>0x44</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.reverse1</key>
        <status>0x80</status>
        <midino>0x44</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.keylock1</key>
        <status>0x90</status>
        <midino>0x42</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.keylock1</key>
        <status>0x80</status>
        <midino>0x42</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x80</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.flanger1</key>
        <status>0x90</status>
        <midino>0x4a</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI1003DEX.flanger1</key>
        <status>0x80</status>
        <midino>0x4a</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x60</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x80</status>
        <midino>0x60</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x3a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x3a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x3b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x3b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x18</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.pregainreset2</key>
        <status>0x90</status>
        <midino>0x12</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x19</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.filterHighreset2</key>
        <status>0x90</status>
        <midino>0x13</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x1a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.filterMidreset2</key>
        <status>0x90</status>
        <midino>0x14</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x1b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.filterLowreset2</key>
        <status>0x90</status>
        <midino>0x15</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0f</midino>
        <options>
          <invert/>
        </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI1003DEX.jog_touch2</key>
         <status>0x90</status>
         <midino>0x31</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI1003DEX.jog_wheel2</key>
         <status>0xB0</status>
         <midino>0x11</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI1003DEX.jog_wheel_seek2</key>
         <status>0xB0</status>
         <midino>0x13</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel2]</group>
         <key>VestaxVCI1003DEX.play2</key>
        <status>0x90</status>
        <midino>0x36</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.play2</key>
        <status>0x90</status>
        <midino>0x36</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.cue2</key>
        <status>0x90</status>
        <midino>0x37</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.cue2</key>
        <status>0x80</status>
        <midino>0x37</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>start_stop</key>
        <status>0x80</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.autoloop2</key>
        <status>0x90</status>
        <midino>0x39</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.autoloop2</key>
        <status>0x80</status>
        <midino>0x39</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.pfl2</key>
        <status>0x90</status>
        <midino>0x49</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.pfl2</key>
        <status>0x80</status>
        <midino>0x49</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.reverse2</key>
        <status>0x90</status>
        <midino>0x45</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.reverse2</key>
        <status>0x80</status>
        <midino>0x45</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.keylock2</key>
        <status>0x90</status>
        <midino>0x43</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.keylock2</key>
        <status>0x80</status>
        <midino>0x43</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.flanger2</key>
        <status>0x90</status>
        <midino>0x4B</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI1003DEX.flanger2</key>
        <status>0x80</status>
        <midino>0x4B</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x61</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x80</status>
        <midino>0x61</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x3d</midino>
      </control>

<!-- FX channel A knobs 46,47,48,49-->
      <control>
        <group>[Microphone]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB0</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB0</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0xB0</status>
        <midino>0x47</midino>
      </control>
<!-- FX channel B knobs 54,55,56,57 -->
      <control>
        <group>[Microphone]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x54</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB0</status>
        <midino>0x57</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB0</status>
        <midino>0x56</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0xB0</status>
        <midino>0x55</midino>
      </control>

<!-- FX channel Sampler knobs 4A,4B,4C,4D -->
      <control>
        <group>[Sampler1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4A</midino>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4C</midino>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x4D</midino>
      </control>
<!-- FX channel Sampler Pad 1-4 54,57,5A,4B -->
      <control>
        <group>[Sampler1]</group>
        <key>VestaxVCI1003DEX.sampler1play</key>
        <status>0x90</status>
        <midino>0x54</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>VestaxVCI1003DEX.sampler1play</key>
        <status>0x80</status>
        <midino>0x54</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>VestaxVCI1003DEX.sampler2play</key>
        <status>0x90</status>
        <midino>0x57</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>VestaxVCI1003DEX.sampler2play</key>
        <status>0x80</status>
        <midino>0x57</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
<control>
        <group>[Sampler3]</group>
        <key>VestaxVCI1003DEX.sampler3play</key>
        <status>0x90</status>
        <midino>0x5A</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>VestaxVCI1003DEX.sampler3play</key>
        <status>0x80</status>
        <midino>0x5A</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>

<!-- pad 4 sends same midi as  fx buttons(sampler right, channel A right). ultra weird and fx button is more important
      <control>
        <group>[Sampler4]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x4B</midino>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x4B</midino>
      </control>
-->

      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x90</status>
        <midino>0x5d</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x80</status>
        <midino>0x5d</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x90</status>
        <midino>0x5c</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x80</status>
        <midino>0x5c</midino>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
