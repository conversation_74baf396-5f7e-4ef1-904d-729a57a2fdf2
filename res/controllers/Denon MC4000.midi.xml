<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Denon MC4000</name>
        <author><PERSON></author>
        <description>2-deck mapping for Denon MC4000 controller</description>
        <forums>https://mixxx.discourse.group/t/denon-mc4000-mapping/15311</forums>
        <manual>denon_mc4000</manual>
    </info>
    <controller id="MC4000">
        <scriptfiles>
            <file functionprefix="MC4000" filename="Denon-MC4000-scripts.js"/>
        </scriptfiles>
        <controls>
            <!--Platter touch -->
            <control>
                <group>[Channel1]</group>
                <key>MC4000.leftDeck.platterTouch</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC4000.rightDeck.platterTouch</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Vinyl mode buttons -->
            <control>
                <group>[Channel1]</group>
                <key>MC4000.leftDeck.vinylModeToggle</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC4000.rightDeck.vinylModeToggle</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!--  "Censor" buttons (shift + vinyl mode) -->
            <!--  The "reverseroll" control doesn't appear to be working -->
            <control>
                <group>[Channel1]</group>
                <key>reverseroll</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reverseroll</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Key lock buttons -->
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Pitch-fader range change toggle -->
            <control>
                <group>[Channel1]</group>
                <key>MC4000.leftDeck.rateRange</key>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC4000.rightDeck.rateRange</key>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Pitch-bend buttons -->
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down_small</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down_small</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up_small</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up_small</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <status>0x91</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Sync buttons -->
            <!-- Note: Behavior differs slightly from Serato -->
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Cue buttons (transport) -->
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Shift + Cue goes back to start of track -->
            <control>
                <group>[Channel1]</group>
                <key>start_stop</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>start_stop</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Play buttons -->
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Play + shift = stutter -->
            <control>
                <group>[Channel1]</group>
                <key>play_stutter</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play_stutter</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Cue (headphone PFL) -->
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Autoloop button maps to loop 4 -->
            <control>
                <group>[Channel1]</group>
                <key>beatloop_4_toggle</key>
                <status>0x94</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_4_toggle</key>
                <status>0x95</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Shift + Autoloop button maps to quantize-->
            <control>
                <group>[Channel1]</group>
                <key>quantize</key>
                <status>0x94</status>
                <midino>0x3F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>quantize</key>
                <status>0x95</status>
                <midino>0x3F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Beat halve -->
            <control>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <status>0x94</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <status>0x95</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Loop in (Shift + beat halve) -->
            <control>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <status>0x94</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <status>0x95</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Beat double -->
            <control>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <status>0x94</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <status>0x95</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Loop out (Shift + beat double)  -->
            <control>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <status>0x94</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <status>0x95</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Reloop -->
            <control>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <status>0x94</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <status>0x95</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- ### Hot cue pads ### -->
            <!-- Hot cue Pad 1 -->
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hot cue Pad 2 -->
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hot cue Pad 3 -->
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hot cue Pad 4 -->
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- FX Enable switches -->
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- FX Select switches -->
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>next_effect</key>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>next_effect</key>
                <status>0x98</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>next_effect</key>
                <status>0x98</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>next_effect</key>
                <status>0x99</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>next_effect</key>
                <status>0x99</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>next_effect</key>
                <status>0x99</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Map the TAP button to the main bpm setting
            (differs from Serato which maps it to the FX timing -->
            <control>
                <group>[Channel1]</group>
                <key>bpm_tap</key>
                <status>0x98</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>bpm_tap</key>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- NOTE: Shift button isn't mapped to anything as number shifted automatically -->
            <!-- TODO: "PAD MODE" currently isn't mapped to anything -->

            <!-- Headphone split cue switch-->
            <control>
                <group>[Master]</group>
                <key>MC4000.headphoneSplit</key>
                <status>0x9F</status>
                <midino>0x1C</midino>
                <options>
                    <!-- The Mixxx control is a toggle, we need an actual switch -->
                    <script-binding/>
                </options>
            </control>

            <!-- ### Navigation controls ### -->
            <!-- Load deck 1 -->
            <control>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x9F</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- Load deck 2 -->
            <control>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x9F</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Back Button maps to Shift+Tab key -->
            <control>
                <group>[Library]</group>
                <key>MoveFocusBackward</key>
                <status>0x9F</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Panel Button maps to Tab key -->
            <control>
                <group>[Library]</group>
                <key>MoveFocusForward</key>
                <status>0x9F</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Load "Prep": Map this to load preview -->
            <control>
                <group>[PreviewDeck1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x9F</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[PreviewDeck1]</group>
                <key>eject</key>
                <status>0x9F</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- Sampler pads -->
            <control>
                <group>[Sampler1]</group>
                <key>cue_gotoandplay</key>
                <status>0x9F</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>stop</key>
                <status>0x9F</status>
                <midino>0x28</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>cue_gotoandplay</key>
                <status>0x9F</status>
                <midino>0x22</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>stop</key>
                <status>0x9F</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>cue_gotoandplay</key>
                <status>0x9F</status>
                <midino>0x23</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>stop</key>
                <status>0x9F</status>
                <midino>0x2A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>cue_gotoandplay</key>
                <status>0x9F</status>
                <midino>0x24</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>stop</key>
                <status>0x9F</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- Jog whel -->
            <control>
                <group>[Channel1]</group>
                <key>MC4000.leftDeck.jogWheel</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC4000.rightDeck.jogWheel</key>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- FX Beats Encoder (rate of FX modulation in Serato) maps to FX dry/web mix knob -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MC4000.fxWetDry</key>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MC4000.fxWetDry</key>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- FX Beats encoder push button maps to show/hide FX parameters -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>show_parameters</key>
                <status>0x98</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>show_parameters</key>
                <status>0x99</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- Library encoder maps to Up/Down buttons -->
            <control>
                <group>[Library]</group>
                <key>MoveVertical</key>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <!-- Shift + Library encoder maps to PageUp/PageDown -->
            <control>
                <group>[Library]</group>
                <key>ScrollVertical</key>
                <status>0xBF</status>
                <midino>0x01</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <!-- Library encoder button push maps to GoToItem -->
            <control>
                <group>[Library]</group>
                <key>GoToItem</key>
                <status>0x9F</status>
                <midino>0x1F</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- FX meta knobs -->
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>meta</key>
                <status>0xB8</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>meta</key>
                <status>0xB8</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>meta</key>
                <status>0xB8</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>meta</key>
                <status>0xB9</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>meta</key>
                <status>0xB9</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>meta</key>
                <status>0xB9</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- ### Start of mixer channel strips (not in order)  ###
            Level Knob, Hi Knob, Mid Knob, Low knob, Filter knob, Channel Fader -->
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>super1</key>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- ### End of mixer channel strips (not in order) ### -->


            <!-- Sampler level -->
             <control>
                <group>[Master]</group>
                <key>MC4000.samplerLevel</key>
                <status>0xBF</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Crossfader -->
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Headphone gain -->
            <control>
                <group>[Master]</group>
                <key>headGain</key>
                <status>0xBF</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Headphone mix -->
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xBF</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Pitch fader LSB (14-bit value sent across 2 messages) -->
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xB1</status>
                <midino>0x29</midino>
                <options>
                    <fourteen-bit-lsb/>
                </options>
            </control>
            <!-- Pitch fader MSB (14-bit value sent across 2 messages) -->
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <fourteen-bit-msb/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <fourteen-bit-msb/>
                </options>
            </control>
        </controls>
        <!-- Outputs to control LEDS -->
        <outputs>
            <!-- Play -->
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <description>Left Play button LED</description>
                <status>0x90</status>
                <midino>0x00</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <description>Right Play button LED</description>
                <status>0x91</status>
                <midino>0x00</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Cue -->
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <description>Left Cue button LED</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <description>Right Cue button LED</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Sync -->
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>Left sync LED</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>Right sync LED</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Keylock -->
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- PFL Cue -->
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Autoloop button (maps to loop 4) -->
            <output>
                <group>[Channel1]</group>
                <key>beatloop_4_enabled</key>
                <status>0x94</status>
                <midino>0x32</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatloop_4_enabled</key>
                <status>0x95</status>
                <midino>0x32</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Hot Cue -->
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x94</status>
                <midino>0x14</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x95</status>
                <midino>0x14</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x94</status>
                <midino>0x15</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x95</status>
                <midino>0x15</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x94</status>
                <midino>0x16</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x95</status>
                <midino>0x16</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x94</status>
                <midino>0x17</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x95</status>
                <midino>0x17</midino>
                <on>0x01</on>
                <minimum>0.5</minimum>
            </output>
            <!-- FX Enable switches -->
            <output>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x00</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x01</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <status>0x99</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <!-- Sampler Pads (track_samples == 0 when unloaded, > 0 when loaded) -->
            <output>
                <group>[Sampler1]</group>
                <key>track_samples</key>
                <status>0x9F</status>
                <midino>0x21</midino>
                <minimum>0.5</minimum>
                <maximum>2147483647</maximum>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>track_samples</key>
                <status>0x9F</status>
                <midino>0x22</midino>
                <minimum>0.5</minimum>
                <maximum>2147483647</maximum>
            </output>
            <output>
                <group>[Sampler3]</group>
                <key>track_samples</key>
                <status>0x9F</status>
                <midino>0x23</midino>
                <minimum>0.5</minimum>
                <maximum>2147483647</maximum>
            </output>
            <output>
                <group>[Sampler4]</group>
                <key>track_samples</key>
                <status>0x9F</status>
                <midino>0x24</midino>
                <minimum>0.5</minimum>
                <maximum>2147483647</maximum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
