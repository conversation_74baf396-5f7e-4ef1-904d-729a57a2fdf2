<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="1.10.0" schemaVersion="1">
    <info>
        <name>Denon MC3000 v0.995</name>
        <author>BeMixxx</author>
        <description>Optimized for Denon MC3000 - Default Midi Channel</description>
        <manual>denon_mc3000</manual>
    </info>
    <controller id="MC3000 MIDI 1">
    <scriptfiles>
        <file filename="Denon-MC3000-scripts.js" functionprefix="mc3000"/>
    </scriptfiles>

<controls>
<!-- CROSFADER / FADER / PREGAIN / HI / MID /LOW -->
    <control>
        <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Master]</group>
                <key>crossfader</key>
    </control>

    <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <description>Volume Fader deck1 (A)</description>
                <key>volume</key>
    </control>
    <control>
                <status>0xb2</status>
                <midino>0x10</midino>
                <group>[Channel2]</group>
        <description>Volume Fader deck2 (B)</description>
                <key>volume</key>
    </control>
    <control>
                <status>0xb1</status>
                <midino>11</midino>
                <group>[Channel3]</group>
        <description>Volume Fader deck3 (C)</description>
                <key>volume</key>
    </control>
    <control>
                <status>0xb3</status>
                <midino>21</midino>
                <group>[Channel4]</group>
        <description>Volume Fader deck4 (D)</description>
                <key>volume</key>
    </control>

    <control>
                <status>0xb0</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
        <description>Pregain knob deck1 (A)</description>
                <key>pregain</key>
    </control>
    <control>
                <status>0xb2</status>
                <midino>12</midino>
                <group>[Channel2]</group>
        <description>Pregain knob deck2 (B)</description>
                <key>pregain</key>
    </control>
    <control>
                <status>0xb1</status>
                <midino>7</midino>
                <group>[Channel3]</group>
        <description>Pregain knob deck3 (C)</description>
                <key>pregain</key>
    </control>
    <control>
                <status>0xb3</status>
                <midino>17</midino>
                <group>[Channel4]</group>
        <description>Pregain knob deck4 (D)</description>
                <key>pregain</key>
    </control>

    <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
        <description>FilterHigh knob deck1 (A)</description>
                <key>filterHigh</key>
    </control>
    <control>
                <status>0xb2</status>
                <midino>0xd</midino>
                <group>[Channel2]</group>
        <description>FilterHigh knob deck2 (B)</description>
                <key>filterHigh</key>
    </control>
    <control>
                <status>0xb1</status>
                <midino>8</midino>
                <group>[Channel3]</group>
        <description>FilterHigh knob deck3 (C)</description>
                <key>filterHigh</key>
    </control>
    <control>
                <status>0xb3</status>
                <midino>18</midino>
                <group>[Channel4]</group>
        <description>FilterHigh knob deck4 (D)</description>
                <key>filterHigh</key>
    </control>

    <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
        <description>FilterMid knob deck1 (A)</description>
                <key>filterMid</key>
    </control>
    <control>
                <status>0xb2</status>
                <midino>0xe</midino>
                <group>[Channel2]</group>
        <description>FilterMid knob deck2 (B)</description>
                <key>filterMid</key>
    </control>
    <control>
                <status>0xb1</status>
                <midino>9</midino>
                <group>[Channel3]</group>
        <description>FilterMid knob deck3 (C)</description>
                <key>filterMid</key>
    </control>
    <control>
                <status>0xb3</status>
                <midino>19</midino>
                <group>[Channel4]</group>
        <description>FilterMid knob deck4 (D)</description>
                <key>filterMid</key>
    </control>

    <control>
                <status>0xb0</status>
                <midino>4</midino>
                <group>[Channel1]</group>
        <description>FilterLow knob deck1 (A)</description>
                <key>filterLow</key>
    </control>
    <control>
                <status>0xb2</status>
                <midino>15</midino>
                <group>[Channel2]</group>
        <description>FilterLow knob deck2 (B)</description>
                <key>filterLow</key>
    </control>
    <control>
                <status>0xb1</status>
                <midino>10</midino>
                <group>[Channel3]</group>
        <description>FilterLow knob deck3 (C)</description>
                <key>filterLow</key>
    </control>
    <control>
                <status>0xb3</status>
                <midino>20</midino>
                <group>[Channel4]</group>
        <description>FilterLow knob deck4 (D)</description>
                <key>filterLow</key>
    </control>

<!-- JOG WHEEL -->
    <control>
                <status>0xb0</status>
                <midino>0x51</midino>
                <group>[Channel1]</group>
        <description>JogWheel deck1 (A)</description>
                <key>mc3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0xb2</status>
                <midino>0x51</midino>
                <group>[Channel2]</group>
        <description>JogWheel deck2 (B)</description>
                <key>mc3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0xb1</status>
                <midino>0x51</midino>
                <group>[Channel3]</group>
        <description>JogWheel deck3 (C)</description>
                <key>mc3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0xb3</status>
                <midino>0x51</midino>
                <group>[Channel4]</group>
        <description>JogWheel deck4 (D)</description>
                <key>mc3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- TOUCH WHEEL -->
    <control>
                <status>0x90</status>
                <midino>81</midino>
                <group>[Channel1]</group>
        <description>TouchWheel pressed deck1 (A)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x80</status>
                <midino>81</midino>
                <group>[Channel1]</group>
        <description>TouchWheel released deck1 (A)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>81</midino>
                <group>[Channel2]</group>
        <description>TouchWheel pressed deck2 (B)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>81</midino>
                <group>[Channel2]</group>
        <description>TouchWheel released deck2 (B)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x91</status>
                <midino>81</midino>
                <group>[Channel3]</group>
        <description>TouchWheel pressed deck3 (C)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x81</status>
                <midino>81</midino>
                <group>[Channel3]</group>
        <description>TouchWheel released deck3 (C)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x93</status>
                <midino>81</midino>
                <group>[Channel4]</group>
        <description>TouchWheel pressed deck4 (D)</description>
                <key>mc3000.wheelTouch</key>
                <options>
            <script-binding/>
                </options>
    </control>
    <control>
                <status>0x83</status>
                <midino>81</midino>
                <group>[Channel4]</group>
        <description>TouchWheel released deck4 (D)</description>
                <key>mc3000.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!--PITCH SLIDER -->
    <control>
                <status>0xe0</status>
                <midino>0x50</midino>
                <group>[Channel1]</group>
                <key>mc3000.rate</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0xe2</status>
                <midino>0x28</midino>
                <group>[Channel2]</group>
                <key>mc3000.rate</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- PFL -->
    <control>
        <status>0x90</status>
        <midino>1</midino>
        <description>Cue PFL deck1 (A)</description>
        <group>[Channel1]</group>
        <key>pfl</key>
    </control>
    <control>
        <status>0x92</status>
        <midino>5</midino>
        <description>Cue PFL deck2 (B)</description>
        <group>[Channel2]</group>
        <key>pfl</key>
    </control>
    <control>
        <status>0x91</status>
        <midino>2</midino>
        <description>Cue PFL deck3 (C)</description>
        <group>[Channel3]</group>
        <key>pfl</key>
    </control>
    <control>
        <status>0x93</status>
        <midino>7</midino>
        <description>Cue PFL deck4 (D)</description>
        <group>[Channel4]</group>
        <key>pfl</key>
    </control>

<!-- EFX 1-4 FOR BEATLOOP_X -->
    <control>
                <status>0x90</status>
                <midino>21</midino>
                <group>[Channel1]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>18</midino>
                <group>[Channel1]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>19</midino>
                <group>[Channel1]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control><control>
                <status>0x90</status>
                <midino>20</midino>
                <group>[Channel1]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>85</midino>
                <group>[Channel2]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>82</midino>
                <group>[Channel2]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>83</midino>
                <group>[Channel2]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>84</midino>
                <group>[Channel2]</group>
                <key>mc3000.efx_beatLoopX</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- HOT CUE 1-4 -->
    <control>
                <status>0x90</status>
                <midino>23</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>23</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>24</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>24</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>25</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x92</status>
                <midino>25</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>32</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x92</status>
                <midino>32</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>

<!-- HOT CUE 5-8 -->
    <control>
                <status>0x90</status>
                <midino>72</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>72</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
        <status>0x90</status>
        <midino>73</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x92</status>
        <midino>73</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x90</status>
        <midino>74</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x92</status>
        <midino>74</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x90</status>
        <midino>75</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x92</status>
        <midino>75</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>

<!-- HOT CUE 1-4 RELEASED-->
    <control>
                <status>0x80</status>
                <midino>23</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>23</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x80</status>
                <midino>24</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>24</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>25</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x82</status>
                <midino>25</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>32</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x82</status>
                <midino>32</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
            </control>

<!-- HOT CUE 5-8 RELEASED -->
    <control>
                <status>0x80</status>
                <midino>72</midino>
                <group>[Channel1]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>72</midino>
                <group>[Channel2]</group>
                <key>mc3000.hotcueset</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
        <status>0x80</status>
        <midino>73</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x82</status>
        <midino>73</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x80</status>
        <midino>74</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x82</status>
        <midino>74</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x80</status>
        <midino>75</midino>
        <group>[Channel1]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0x82</status>
        <midino>75</midino>
        <group>[Channel2]</group>
        <key>mc3000.hotcueset</key>
        <options>
            <script-binding/>
        </options>
    </control>


<!-- EFX KNOB FX1 : MICGAIN,LFO,DEPTH DELAY -->
    <control>
        <status>0xb0</status>
        <midino>85</midino>
        <group>[Flanger]</group>
        <key>mc3000.lfoPeriod</key>
        <description></description>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0xb0</status>
        <midino>86</midino>
        <group>[Flanger]</group>
        <key>mc3000.lfoDepth</key>
        <description></description>
        <options>
            <script-binding/>
        </options>
        </control>
    <control>
        <status>0xb0</status>
        <midino>87</midino>
        <group>[Flanger]</group>
        <key>mc3000.lfoDelay</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0xb0</status>
        <midino>88</midino>
        <group>[Flanger]</group>
        <key>mc3000.micVolume</key>
        <options>
            <script-binding/>
        </options>
    </control>

<!-- EFX KNOB FX2 : HEADMIX -->
    <control>
        <status>0xb2</status>
        <midino>89</midino>
        <group>[Master]</group>
        <key>mc3000.headMix</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0xb2</status>
        <midino>90</midino>
        <group>[Master]</group>
        <key>mc3000.headVolume</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0xb2</status>
        <midino>91</midino>
        <group>[Master]</group>
        <key>mc3000.masterBalance</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
        <status>0xb2</status>
        <midino>92</midino>
        <group>[Master]</group>
        <key>mc3000.masterVolume</key>
        <options>
            <script-binding/>
        </options>
    </control>

<!-- EFX KNOB IN SAMPLE MODE-->
    <control>
        <status>0x90</status>
        <midino>0x21</midino>
        <group>[Sampler1]</group>
        <key>play</key>
    </control>

    <control>
        <status>0xb0</status>
        <midino>0x30</midino>
        <group>[Sampler1]</group>
        <key>pregain</key>
    </control>
    <control>
        <status>0x90</status>
        <midino>0x22</midino>
        <group>[Sampler2]</group>
        <key>play</key>
        <description></description>
    </control>
    <control>
        <status>0xb0</status>
        <midino>0x31</midino>
        <group>[Sampler2]</group>
        <key>pregain</key>
        <description></description>
    </control>
    <control>
        <status>0x90</status>
        <midino>0x23</midino>
                <group>[Sampler3]</group>
        <key>play</key>
        <description></description>
    </control>
    <control>
        <status>0xb0</status>
        <midino>0x32</midino>
        <group>[Sampler3]</group>
        <key>pregain</key>
        <description></description>
    </control>
    <control>
        <status>0x90</status>
        <midino>0x24</midino>
        <group>[Sampler4]</group>
        <key>play</key>
        <description></description>
    </control>
    <control>
        <status>0xb0</status>
        <midino>0x33</midino>
        <group>[Sampler4]</group>
        <key>pregain</key>
        <description></description>
    </control>

<!-- CUE DEFAULT -->
    <control>
        <status>0x90</status>
        <midino>0x42</midino>
        <group>[Channel1]</group>
        <description>Cue BT pressed deck1 (A)</description>
        <key>mc3000.cue_default</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
                <status>0x80</status>
                <midino>0x42</midino>
                <group>[Channel1]</group>
        <description>Cue BT released deck1 (A)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
        <description>Cue BT pressed deck2 (B)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
        <description>Cue BT released deck2 (B)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x91</status>
                <midino>0x42</midino>
                <group>[Channel3]</group>
        <description>Cue BT pressed deck3 (C)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x81</status>
                <midino>0x42</midino>
                <group>[Channel3]</group>
        <description>Cue BT released deck3 (C)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x93</status>
                <midino>0x42</midino>
                <group>[Channel4]</group>
        <description>Cue BT pressed deck4 (D)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x83</status>
                <midino>0x42</midino>
                <group>[Channel4]</group>
        <description>Cue BT released deck4 (D)</description>
                <key>mc3000.cue_default</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- PLAY -->
    <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description>Play BT deck1 (A)</description>
    </control>
    <control>
                <status>0x92</status>
                <midino>0x43</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description>Play BT deck2 (B)</description>
    </control>
    <control>
                <status>0x91</status>
                <midino>0x43</midino>
                <group>[Channel3]</group>
                <key>play</key>
                <description>Play BT deck3 (C)</description>
    </control>
    <control>
                <status>0x93</status>
                <midino>0x43</midino>
                <group>[Channel4]</group>
                <key>play</key>
                <description>Play BT deck4 (D)</description>
    </control>


<!-- LOOP -->
    <control>
        <status>0x90</status>
        <midino>0x37</midino>
        <group>[Channel1]</group>
        <key>mc3000.loopIn</key>
        <options>
            <script-binding/>
        </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>0x37</midino>
                <group>[Channel2]</group>
                <key>mc3000.loopIn</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x39</midino>
                <group>[Channel1]</group>
                <key>mc3000.loopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x92</status>
                <midino>0x39</midino>
                <group>[Channel2]</group>
                <key>mc3000.loopOut</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>105</midino>
                <group>[Channel1]</group>
                <key>mc3000.loopCutM</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>105</midino>
                <group>[Channel2]</group>
                <key>mc3000.loopCutM</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>106</midino>
                <group>[Channel1]</group>
                <key>mc3000.loopCutP</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>106</midino>
                <group>[Channel2]</group>
                <key>mc3000.loopCutP</key>
                <options>
                    <script-binding/>
                </options>
    </control>

    <!-- AUTOLOOP BT -->
    <control>
                <status>0x90</status>
                <midino>29</midino>
                <group>[Channel1]</group>
                <key>mc3000.autoLoop</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>29</midino>
                <group>[Channel2]</group>
                <key>mc3000.autoLoop</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x80</status>
                <midino>29</midino>
                <group>[Channel1]</group>
                <key>mc3000.autoLoop</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>29</midino>
                <group>[Channel2]</group>
                <key>mc3000.autoLoop</key>
                <options>
                    <script-binding/>
                </options>
    </control>

<!--MEDIA Chooser-->
    <control>
                <status>0x90</status>
                <midino>76</midino>
                <group>[Channel1]</group>
                <key>mc3000.killMid</key>
         <options>
                    <script-binding/>
                </options>
        <description>BT Sample</description>
    </control>
    <control>
                <status>0x90</status>
                <midino>77</midino>
                <group>[Channel2]</group>
                <key>mc3000.killMid</key>
        <options>
                    <script-binding/>
                </options>
                <description>BT EFX</description>
    </control>

    <control>
                <status>0x90</status>
                <midino>0x64</midino>
                <group>[Channel1]</group>
                <key>mc3000.killBass</key>
         <options>
                    <script-binding/>
                </options>
        <description> BT Browse</description>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x65</midino>
                <group>[Channel2]</group>
                <key>mc3000.killBass</key>
        <options>
                    <script-binding/>
                </options>
                <description>BT Record</description>
    </control>

<!--PlayList -->
    <control>
                <status>0x90</status>
                <midino>27</midino>
                <group>[Channel1]</group>
                <key>mc3000.cfMode</key>
        <options>
                    <script-binding/>
                </options>
                <description>BT CfMode</description>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x62</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x63</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x30</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description></description>
    </control>
    <control>
                <status>0x90</status>
                <midino>0x29</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description></description>
    </control>
    <control>
                <status>0x90</status>
                <midino>40</midino>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <description></description>
    </control>
    <control>
                <status>0xb0</status>
                <midino>0x54</midino>
                <group>[Playlist]</group>
                <key>mc3000.selectKnob</key>
                <description>Track select knob</description>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- KEYLOCK / SYNC / VINYL MODE -->
    <control>
                <status>0x90</status>
                <midino>6</midino>
                <group>[Channel1]</group>
                <description>Keylock BT deck1 (A)</description>
                <key>keylock</key>
    </control>
    <control>
                <status>0x92</status>
                <midino>6</midino>
                <group>[Channel2]</group>
                <description>Keylock BT deck2 (B)</description>
                <key>keylock</key>
    </control>
    <control>
                <status>0x91</status>
                <midino>6</midino>
                <group>[Channel3]</group>
                <description>Keylock BT deck3 (C)</description>
                <key>keylock</key>
    </control>
    <control>
                <status>0x93</status>
                <midino>6</midino>
                <group>[Channel4]</group>
                <description>Keylock BT deck4 (D)</description>
                <key>keylock</key>
    </control>


    <control>
                <status>0x90</status>
                <midino>107</midino>
                <group>[Channel1]</group>
                <description>Sync BT deck1 (A)</description>
                <key>beatsync</key>
           </control>
    <control>
                <status>0x92</status>
                <midino>107</midino>
                <group>[Channel2]</group>
                <description>Sync BT deck2 (B)</description>
                <key>beatsync</key>
     </control>
    <control>
                <status>0x91</status>
                <midino>107</midino>
                <group>[Channel3]</group>
                <description>Sync BT deck3 (C)</description>
                <key>beatsync</key>
     </control>
    <control>
                <status>0x93</status>
                <midino>107</midino>
                <group>[Channel4]</group>
                <description>Sync BT deck4 (D)</description>
                <key>beatsync</key>
     </control>



    <control>
                <status>0x90</status>
                <midino>4</midino>
                <group>[Channel1]</group>
                <key>mc3000.vinylmode</key>
                <description>VinylMode BT deck1 (A)</description>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>4</midino>
                <group>[Channel2]</group>
                <key>mc3000.vinylmode</key>
                <description>VinylMode BT deck2 (B)</description>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x91</status>
                <midino>4</midino>
                <group>[Channel3]</group>
                <key>mc3000.vinylmode</key>
                <description>VinylMode BT deck3 (C)</description>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x93</status>
                <midino>4</midino>
                <group>[Channel4]</group>
                <key>mc3000.vinylmode</key>
                <description>VinylMode BT deck4 (D)</description>
                <options>
                    <script-binding/>
                </options>
    </control>

<!-- PITCH/RATE + FWD/BACK -->
<!-- PRESSED CH1... -->
    <control>
                <status>0x90</status>
                <midino>12</midino>
                <group>[Channel1]</group>
                <key>mc3000.bendUp</key>
                <options>
                   <script-binding/>
                </options>
    </control>
        <control>
                <status>0x90</status>
                <midino>13</midino>
                <group>[Channel1]</group>
                <key>mc3000.bendDown</key>
                <options>
                   <script-binding/>
                </options>
    </control>
<!-- RELEASED CH1... -->
    <control>
        <status>0x80</status>
                <midino>12</midino>
                <group>[Channel1]</group>
                <key>mc3000.bendUp</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x80</status>
                <midino>13</midino>
                <group>[Channel1]</group>
                <key>mc3000.bendDown</key>
                <options>
                   <script-binding/>
                </options>
    </control>
<!-- PRESSED CH2... -->
    <control>
                <status>0x92</status>
                <midino>12</midino>
                <group>[Channel2]</group>
                <key>mc3000.bendUp</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>13</midino>
                <group>[Channel2]</group>
                <key>mc3000.bendDown</key>
                <options>
                   <script-binding/>
        </options>
    </control>
<!-- RELEASED CH2... -->
    <control>
                <status>0x82</status>
                <midino>12</midino>
                <group>[Channel2]</group>
                <key>mc3000.bendUp</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>13</midino>
                <group>[Channel2]</group>
                <key>mc3000.bendDown</key>
                <options>
                   <script-binding/>
                </options>
    </control>

<!-- SHIFT -->
    <control>
                <status>0x90</status>
                <midino>96</midino>
                <group>[Channel1]</group>
                <key>mc3000.shift</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>97</midino>
                <group>[Channel2]</group>
                <key>mc3000.shift</key>
                <options>
                    <script-binding/>
                </options>
    </control>
      <control>
                <status>0x80</status>
                <midino>96</midino>
                <group>[Channel1]</group>
                <key>mc3000.shift</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x82</status>
                <midino>97</midino>
                <group>[Channel2]</group>
                <key>mc3000.shift</key>
                <options>
                    <script-binding/>
                </options>
    </control>
<!-- FX ON 1 / FX ON 2 -->
    <control>
                <status>0x90</status>
                <midino>86</midino>
                <group>[Channel1]</group>
                <key>mc3000.fxon1</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>86</midino>
                <group>[Channel2]</group>
                <key>mc3000.fxon1</key>
                <options>
                    <script-binding/>
                </options>
    </control>
    <control>
                <status>0x90</status>
                <midino>87</midino>
                <group>[Channel1]</group>
                <key>mc3000.fxon2</key>
                <options>
                   <script-binding/>
                </options>
    </control>
    <control>
                <status>0x92</status>
                <midino>87</midino>
                <group>[Channel2]</group>
                <key>mc3000.fxon2</key>
                <options>
                    <script-binding/>
                </options>
    </control>

        </controls>

        <outputs></outputs>

    </controller>
</MixxxMIDIPreset>
