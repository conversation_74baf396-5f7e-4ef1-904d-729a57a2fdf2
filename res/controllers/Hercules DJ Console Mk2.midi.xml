<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.7.0">
  <info>
    <name>Hercules DJ Console Mk2</name>
    <author><PERSON></author>
    <description>This is a complete mapping for a Hercules DJ Console Mk2.</description>
    <manual>hercules_dj_console_mk2</manual>
  </info>
  <controller id="Hercules DJ Console Mk2 MIDI">
    <scriptfiles>
      <file filename="Hercules-DJ-Console-Mk2-scripts.js" functionprefix="HerculesMk2"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x31</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.play</key>
        <status>0xB0</status>
        <midino>0x08</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.cue</key>
    <status>0xB0</status>
        <midino>0x09</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>PrevTrack</key>
        <status>0xB0</status>
        <midino>0x0B</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NextTrack</key>
        <status>0xB0</status>
        <midino>0x0C</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x32</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.pitch</key>
        <status>0xB0</status>
        <midino>0x34</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.jog_wheel</key>
        <status>0xB0</status>
        <midino>0x36</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Headphones Deck A -->
        <group>[Channel1]</group>
        <key>HerculesMk2.pfl</key>
        <status>0xB0</status>
        <midino>0x21</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x2E</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x2F</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x30</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x0F</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x0E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x0D</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0xB0</status>
        <midino>0x13</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0xB0</status>
        <midino>0x0A</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.play</key>
        <status>0xB0</status>
        <midino>0x02</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.cue</key>
        <status>0xB0</status>
        <midino>0x03</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>PrevTrack</key>
        <status>0xB0</status>
        <midino>0x05</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NextTrack</key>
        <status>0xB0</status>
        <midino>0x06</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.buttons123mode</key>
        <status>0xB0</status>
        <midino>0x01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x033</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.pitch</key>
        <status>0xB0</status>
        <midino>0x35</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.jog_wheel</key>
        <status>0xB0</status>
        <midino>0x37</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Headphones Deck B -->
        <group>[Channel2]</group>
        <key>HerculesMk2.pfl</key>
        <status>0xB0</status>
        <midino>0x22</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x2B</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x2C</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x2D</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x11</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.buttons123</key>
        <status>0xB0</status>
        <midino>0x12</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0xB0</status>
        <midino>0x17</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0xB0</status>
        <midino>0x18</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0xB0</status>
        <midino>0x04</midino>
        <options>
          <Button/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.buttons123mode</key>
        <status>0xB0</status>
        <midino>0x07</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options>
          <Button />
        </options>
      </control>

      <control>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0xB0</status>
        <midino>0x1A</midino>
        <options>
          <Button />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>HerculesMk2.loadSelectedTrack</key>
        <status>0xB0</status>
        <midino>0x1B</midino>
        <options>
      <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>HerculesMk2.loadSelectedTrack</key>
        <status>0xB0</status>
        <midino>0x1C</midino>
        <options>
      <Script-Binding/>
        </options>
      </control>
      <control> <!-- Headphones Mix -->
        <group>[Master]</group>
        <key>HerculesMk2.pfl</key>
        <status>0xB0</status>
        <midino>0x24</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Headphones Split -->
        <group>[Master]</group>
        <key>HerculesMk2.pfl</key>
        <status>0xB0</status>
        <midino>0x23</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
    </controls>

    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0xB0</status>
        <midino>0x08</midino>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0xB0</status>
        <midino>0x02</midino>
        <minimum>0.5</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x00</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x05</midino>
        <minimum>0.9</minimum>
        <maximum>0.99</maximum>
      </output>

      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x0A</midino>
        <minimum>0.4</minimum>
        <maximum>1</maximum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <minimum>0.5</minimum>
        <maximum>1</maximum>
      </output>
      <!--
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x0D</midino>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x0E</midino>
        <minimum>0.7</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x0F</midino>
        <minimum>0.8</minimum>
      </output>
      -->
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x04</midino>
        <minimum>0.4</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x1A</midino>
        <minimum>0.5</minimum>
      </output>
      <!--
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x12</midino>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x11</midino>
        <minimum>0.7</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <minimum>0.8</minimum>
      </output>
      -->
    </outputs>
  </controller>
</MixxxMIDIPreset>
