<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset schemaVersion="1" mixxxVersion="2.5.0+">
    <info>
        <name>Numark NS6II</name>
        <author>Swiftb0y</author>
        <description>Mapping for the Numark NS6II controller. It is able to fully communicate with the integrated screens. You can manipulate the Beatgrid of the track via the slicer pad page (since mixxx doesn't have slicer capabilities)</description>
        <wiki>Encoded URL to Mixxx wiki page documenting this controller mapping</wiki>
    </info>
    <settings>
        <option
            variable="useButtonBacklight"
            type="boolean"
            default="true"
            label="Use Button backlight"
            >
            <description>All buttons that have an LED will stay dimly lit even when turned off so they're easier to see in dark environments</description>
        </option>
        <option
            variable="navEncoderAcceleration"
            type="integer"
            min="1"
            default="5"
            label="Move this many tracks at once when pressing :hwbtn:SHIFT and moving the Browse Encoder">
            <description>Amount of tracks moved in a single step when turning the BROWSE encoder while pressing SHIFT</description>
        </option>
        <option
            variable="defaultLoopRootSize"
            type="enum"
            label="Loop size of the smallest Pad in Auto-Loop (and similar) Padmodes">
            <!-- values are actually exponents 2**n-->
            <value label="1/32">-5</value>
            <value label="1/16">-4</value>
            <value label="1/8">-3</value>
            <value label="1/4">-2</value>
            <value label="1/2">-1</value>
            <value label="1" default="true">0</value>
            <value label="2">1</value>
            <value label="4">2</value>
            <value label="8">3</value>
            <value label="16">4</value>
            <value label="32">5</value>
            <value label="64">6</value>
            <value label="128">7</value>
            <description>This specifies the smallest loop size, the larger loopsizes are automatically available on the other pads. This can also be changed on the fly using :hwbtn:SHIFT + "parameter adjust"</description>
        </option>
    </settings>
    <controller id="NS6">
        <scriptfiles>
            <file functionprefix="" filename="lodash.mixxx.js"/>
            <file functionprefix="" filename="midi-components-0.0.js"/>
            <file functionprefix="" filename="common-controller-scripts.js"/>
            <file functionprefix="NS6II" filename="Numark-NS6II-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].play.input</key>
                <status>0x80</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].play.input</key>
                <status>0x81</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].play.input</key>
                <status>0x82</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].play.input</key>
                <status>0x83</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.cues.input</key>
                <status>0x84</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.cues.input</key>
                <status>0x85</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.cues.input</key>
                <status>0x86</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.cues.input</key>
                <status>0x87</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[1].input</key>
                <status>0x88</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[1].input</key>
                <status>0x89</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].play.input</key>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].play.input</key>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].play.input</key>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].play.input</key>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.cues.input</key>
                <status>0x94</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.cues.input</key>
                <status>0x95</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.cues.input</key>
                <status>0x96</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.cues.input</key>
                <status>0x97</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[1].input</key>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[1].input</key>
                <status>0x99</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].knobs[1].input</key>
                <status>0xB8</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].knobs[1].input</key>
                <status>0xB9</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.libraryNavigation.turn.input</key>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].cue.input</key>
                <status>0x80</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].cue.input</key>
                <status>0x81</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].cue.input</key>
                <status>0x82</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].cue.input</key>
                <status>0x83</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[2].input</key>
                <status>0x88</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[2].input</key>
                <status>0x89</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].cue.input</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].cue.input</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].cue.input</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].cue.input</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[2].input</key>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[2].input</key>
                <status>0x99</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].knobs[2].input</key>
                <status>0xB8</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].knobs[2].input</key>
                <status>0xB9</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.libraryNavigation.turn.input</key>
                <status>0xBF</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].sync.input</key>
                <status>0x80</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].sync.input</key>
                <status>0x81</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].sync.input</key>
                <status>0x82</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].sync.input</key>
                <status>0x83</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.cues.input</key>
                <status>0x84</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.cues.input</key>
                <status>0x85</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.cues.input</key>
                <status>0x86</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.cues.input</key>
                <status>0x87</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[3].input</key>
                <status>0x88</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[3].input</key>
                <status>0x89</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[0].loadTrackIntoDeck.input</key>
                <status>0x8F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].sync.input</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].sync.input</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].sync.input</key>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].sync.input</key>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.cues.input</key>
                <status>0x94</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.cues.input</key>
                <status>0x95</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.cues.input</key>
                <status>0x96</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.cues.input</key>
                <status>0x97</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[3].input</key>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[3].input</key>
                <status>0x99</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[0].loadTrackIntoDeck.input</key>
                <status>0x9F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].knobs[3].input</key>
                <status>0xB8</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].knobs[3].input</key>
                <status>0xB9</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].sync.input</key>
                <status>0x80</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].sync.input</key>
                <status>0x81</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].sync.input</key>
                <status>0x82</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].sync.input</key>
                <status>0x83</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[1].loadTrackIntoDeck.input</key>
                <status>0x8F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].sync.input</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].sync.input</key>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].sync.input</key>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].sync.input</key>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[1].loadTrackIntoDeck.input</key>
                <status>0x9F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].dryWetKnob.input</key>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].dryWetKnob.input</key>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].play.input</key>
                <status>0x80</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].play.input</key>
                <status>0x81</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].play.input</key>
                <status>0x82</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].play.input</key>
                <status>0x83</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].slip.input</key>
                <status>0x84</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].slip.input</key>
                <status>0x85</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].slip.input</key>
                <status>0x86</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].slip.input</key>
                <status>0x87</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].effectFocusButton.input</key>
                <status>0x88</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].effectFocusButton.input</key>
                <status>0x89</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[2].loadTrackIntoDeck.input</key>
                <status>0x8F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].play.input</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].play.input</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].play.input</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].play.input</key>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].slip.input</key>
                <status>0x94</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].slip.input</key>
                <status>0x95</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].slip.input</key>
                <status>0x96</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].slip.input</key>
                <status>0x97</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].effectFocusButton.input</key>
                <status>0x98</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].effectFocusButton.input</key>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[2].loadTrackIntoDeck.input</key>
                <status>0x9F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].cue.input</key>
                <status>0x80</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].cue.input</key>
                <status>0x81</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].cue.input</key>
                <status>0x82</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].cue.input</key>
                <status>0x83</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel1.input</key>
                <status>0x88</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel1.input</key>
                <status>0x89</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[3].loadTrackIntoDeck.input</key>
                <status>0x8F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].cue.input</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].cue.input</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].cue.input</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].cue.input</key>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel1.input</key>
                <status>0x98</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel1.input</key>
                <status>0x99</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.channels[3].loadTrackIntoDeck.input</key>
                <status>0x9F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].jog.inputTouch</key>
                <status>0x80</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].jog.inputTouch</key>
                <status>0x81</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].jog.inputTouch</key>
                <status>0x82</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].jog.inputTouch</key>
                <status>0x83</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel2.input</key>
                <status>0x88</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel2.input</key>
                <status>0x89</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.libraryNavigation.press.input</key>
                <status>0x8F</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].jog.inputTouch</key>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].jog.inputTouch</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].jog.inputTouch</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].jog.inputTouch</key>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel2.input</key>
                <status>0x98</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel2.input</key>
                <status>0x99</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.libraryNavigation.press.input</key>
                <status>0x9F</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].jog.inputWheel</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].jog.inputWheel</key>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].jog.inputWheel</key>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].jog.inputWheel</key>
                <status>0xB3</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].scratch.input</key>
                <status>0x80</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].scratch.input</key>
                <status>0x81</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].scratch.input</key>
                <status>0x82</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].scratch.input</key>
                <status>0x83</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel3.input</key>
                <status>0x88</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel3.input</key>
                <status>0x89</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].scratch.input</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].scratch.input</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].scratch.input</key>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].scratch.input</key>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel3.input</key>
                <status>0x98</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel3.input</key>
                <status>0x99</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel4.input</key>
                <status>0x88</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel4.input</key>
                <status>0x89</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableOnChannelButtons.Channel4.input</key>
                <status>0x98</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableOnChannelButtons.Channel4.input</key>
                <status>0x99</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NS6II.mixer.crossfader.input</key>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.slider.input</key>
                <status>0x84</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.slider.input</key>
                <status>0x85</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.slider.input</key>
                <status>0x86</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.slider.input</key>
                <status>0x87</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.slider.input</key>
                <status>0x94</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.slider.input</key>
                <status>0x95</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.slider.input</key>
                <status>0x96</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.slider.input</key>
                <status>0x97</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitch.inputMSB</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitch.inputMSB</key>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitch.inputMSB</key>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitch.inputMSB</key>
                <status>0xB3</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Mixer Profile]</group>
                <key>NS6II.mixer.crossfaderContour.input</key>
                <status>0xBF</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendPlus.input</key>
                <status>0x80</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendPlus.input</key>
                <status>0x81</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendPlus.input</key>
                <status>0x82</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendPlus.input</key>
                <status>0x83</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.sampler.input</key>
                <status>0x84</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.sampler.input</key>
                <status>0x85</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.sampler.input</key>
                <status>0x86</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.sampler.input</key>
                <status>0x87</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[1].input</key>
                <status>0x88</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[1].input</key>
                <status>0x89</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendPlus.input</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendPlus.input</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendPlus.input</key>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendPlus.input</key>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.sampler.input</key>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.sampler.input</key>
                <status>0x95</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.sampler.input</key>
                <status>0x96</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.sampler.input</key>
                <status>0x97</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[1].input</key>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[1].input</key>
                <status>0x99</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendMinus.input</key>
                <status>0x80</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendMinus.input</key>
                <status>0x81</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendMinus.input</key>
                <status>0x82</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendMinus.input</key>
                <status>0x83</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[2].input</key>
                <status>0x88</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[2].input</key>
                <status>0x89</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendMinus.input</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendMinus.input</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendMinus.input</key>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendMinus.input</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[2].input</key>
                <status>0x98</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[2].input</key>
                <status>0x99</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].bleep.input</key>
                <status>0x80</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].bleep.input</key>
                <status>0x81</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].bleep.input</key>
                <status>0x82</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].bleep.input</key>
                <status>0x83</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[3].input</key>
                <status>0x88</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[3].input</key>
                <status>0x89</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].bleep.input</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].bleep.input</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].bleep.input</key>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].bleep.input</key>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].enableButtons[3].input</key>
                <status>0x98</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].enableButtons[3].input</key>
                <status>0x99</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.loop.input</key>
                <status>0x84</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.loop.input</key>
                <status>0x85</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.loop.input</key>
                <status>0x86</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.loop.input</key>
                <status>0x87</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.view.input</key>
                <status>0x8F</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.loop.input</key>
                <status>0x94</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.loop.input</key>
                <status>0x95</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.loop.input</key>
                <status>0x96</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.loop.input</key>
                <status>0x97</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.view.input</key>
                <status>0x9F</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.sampler.input</key>
                <status>0x84</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.sampler.input</key>
                <status>0x85</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.sampler.input</key>
                <status>0x86</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.sampler.input</key>
                <status>0x87</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.area.input</key>
                <status>0x8F</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.sampler.input</key>
                <status>0x94</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.sampler.input</key>
                <status>0x95</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.sampler.input</key>
                <status>0x96</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.sampler.input</key>
                <status>0x97</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.area.input</key>
                <status>0x9F</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].bleep.input</key>
                <status>0x80</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].bleep.input</key>
                <status>0x81</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].bleep.input</key>
                <status>0x82</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].bleep.input</key>
                <status>0x83</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.auto.input</key>
                <status>0x84</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.auto.input</key>
                <status>0x85</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.auto.input</key>
                <status>0x86</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.auto.input</key>
                <status>0x87</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].bleep.input</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].bleep.input</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].bleep.input</key>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].bleep.input</key>
                <status>0x93</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.modeSelectors.auto.input</key>
                <status>0x94</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.modeSelectors.auto.input</key>
                <status>0x95</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.modeSelectors.auto.input</key>
                <status>0x96</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.modeSelectors.auto.input</key>
                <status>0x97</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.back.input</key>
                <status>0x8F</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.back.input</key>
                <status>0x9F</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.back.input</key>
                <status>0x8F</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.back.input</key>
                <status>0x9F</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.view.input</key>
                <status>0x8F</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.view.input</key>
                <status>0x9F</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[0].input</key>
                <status>0x84</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[0].input</key>
                <status>0x85</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[0].input</key>
                <status>0x86</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[0].input</key>
                <status>0x87</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.lprep.input</key>
                <status>0x8F</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[0].input</key>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[0].input</key>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[0].input</key>
                <status>0x96</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[0].input</key>
                <status>0x97</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.lprep.input</key>
                <status>0x9F</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[1].input</key>
                <status>0x84</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[1].input</key>
                <status>0x85</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[1].input</key>
                <status>0x86</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[1].input</key>
                <status>0x87</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[1].input</key>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[1].input</key>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[1].input</key>
                <status>0x96</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[1].input</key>
                <status>0x97</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[2].input</key>
                <status>0x84</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[2].input</key>
                <status>0x85</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[2].input</key>
                <status>0x86</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[2].input</key>
                <status>0x87</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[2].input</key>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[2].input</key>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[2].input</key>
                <status>0x96</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[2].input</key>
                <status>0x97</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].preGain.input</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].preGain.input</key>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].preGain.input</key>
                <status>0xB2</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].preGain.input</key>
                <status>0xB3</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[0].input</key>
                <status>0x80</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[0].input</key>
                <status>0x81</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[0].input</key>
                <status>0x82</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[0].input</key>
                <status>0x83</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[3].input</key>
                <status>0x84</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[3].input</key>
                <status>0x85</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[3].input</key>
                <status>0x86</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[3].input</key>
                <status>0x87</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[0].input</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[0].input</key>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[0].input</key>
                <status>0x92</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[0].input</key>
                <status>0x93</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[3].input</key>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[3].input</key>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[3].input</key>
                <status>0x96</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[3].input</key>
                <status>0x97</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqKnobs[0].input</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqKnobs[0].input</key>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqKnobs[0].input</key>
                <status>0xB2</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqKnobs[0].input</key>
                <status>0xB3</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[1].input</key>
                <status>0x80</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[1].input</key>
                <status>0x81</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[1].input</key>
                <status>0x82</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[1].input</key>
                <status>0x83</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[4].input</key>
                <status>0x84</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[4].input</key>
                <status>0x85</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[4].input</key>
                <status>0x86</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[4].input</key>
                <status>0x87</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[1].input</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[1].input</key>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[1].input</key>
                <status>0x92</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[1].input</key>
                <status>0x93</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[4].input</key>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[4].input</key>
                <status>0x95</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[4].input</key>
                <status>0x96</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[4].input</key>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqKnobs[1].input</key>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqKnobs[1].input</key>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqKnobs[1].input</key>
                <status>0xB2</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqKnobs[1].input</key>
                <status>0xB3</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[2].input</key>
                <status>0x80</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[2].input</key>
                <status>0x81</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[2].input</key>
                <status>0x82</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[2].input</key>
                <status>0x83</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[5].input</key>
                <status>0x84</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[5].input</key>
                <status>0x85</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[5].input</key>
                <status>0x86</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[5].input</key>
                <status>0x87</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqCaps[2].input</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqCaps[2].input</key>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqCaps[2].input</key>
                <status>0x92</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqCaps[2].input</key>
                <status>0x93</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[5].input</key>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[5].input</key>
                <status>0x95</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[5].input</key>
                <status>0x96</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[5].input</key>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].eqKnobs[2].input</key>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].eqKnobs[2].input</key>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].eqKnobs[2].input</key>
                <status>0xB2</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].eqKnobs[2].input</key>
                <status>0xB3</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].filterCap.input</key>
                <status>0x80</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].filterCap.input</key>
                <status>0x81</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].filterCap.input</key>
                <status>0x82</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].filterCap.input</key>
                <status>0x83</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[6].input</key>
                <status>0x84</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[6].input</key>
                <status>0x85</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[6].input</key>
                <status>0x86</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[6].input</key>
                <status>0x87</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].filterCap.input</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].filterCap.input</key>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].filterCap.input</key>
                <status>0x92</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].filterCap.input</key>
                <status>0x93</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[6].input</key>
                <status>0x94</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[6].input</key>
                <status>0x95</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[6].input</key>
                <status>0x96</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[6].input</key>
                <status>0x97</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].filter.input</key>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].filter.input</key>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].filter.input</key>
                <status>0xB2</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].filter.input</key>
                <status>0xB3</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].pfl.input</key>
                <status>0x80</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].pfl.input</key>
                <status>0x81</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].pfl.input</key>
                <status>0x82</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].pfl.input</key>
                <status>0x83</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[7].input</key>
                <status>0x84</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[7].input</key>
                <status>0x85</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[7].input</key>
                <status>0x86</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[7].input</key>
                <status>0x87</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.lprep.input</key>
                <status>0x8F</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].pfl.input</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].pfl.input</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].pfl.input</key>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].pfl.input</key>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[7].input</key>
                <status>0x94</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[7].input</key>
                <status>0x95</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[7].input</key>
                <status>0x96</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[7].input</key>
                <status>0x97</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.lprep.input</key>
                <status>0x9F</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[0].input</key>
                <status>0x84</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[0].input</key>
                <status>0x85</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[0].input</key>
                <status>0x86</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[0].input</key>
                <status>0x87</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NS6II.mixer.splitCue.input</key>
                <status>0x8F</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[0].input</key>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[0].input</key>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[0].input</key>
                <status>0x96</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[0].input</key>
                <status>0x97</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NS6II.mixer.splitCue.input</key>
                <status>0x9F</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].volume.input</key>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].volume.input</key>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[2].volume.input</key>
                <status>0xB2</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].volume.input</key>
                <status>0xB3</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[1].input</key>
                <status>0x84</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[1].input</key>
                <status>0x85</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[1].input</key>
                <status>0x86</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[1].input</key>
                <status>0x87</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[1].input</key>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[1].input</key>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[1].input</key>
                <status>0x96</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[1].input</key>
                <status>0x97</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].crossfaderOrientation.input</key>
                <status>0x80</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].crossfaderOrientation.input</key>
                <status>0x81</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].crossfaderOrientation.input</key>
                <status>0x82</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].crossfaderOrientation.input</key>
                <status>0x83</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[2].input</key>
                <status>0x84</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[2].input</key>
                <status>0x85</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[2].input</key>
                <status>0x86</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[2].input</key>
                <status>0x87</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.area.input</key>
                <status>0x8F</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.mixer.channels[0].crossfaderOrientation.input</key>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.mixer.channels[1].crossfaderOrientation.input</key>
                <status>0x91</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.channels[2].crossfaderOrientation.input</key>
                <status>0x92</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.channels[3].crossfaderOrientation.input</key>
                <status>0x93</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[2].input</key>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[2].input</key>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[2].input</key>
                <status>0x96</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[2].input</key>
                <status>0x97</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.mixer.browseSection.area.input</key>
                <status>0x9F</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].slip.input</key>
                <status>0x80</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].slip.input</key>
                <status>0x81</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].slip.input</key>
                <status>0x82</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].slip.input</key>
                <status>0x83</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[3].input</key>
                <status>0x84</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[3].input</key>
                <status>0x85</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[3].input</key>
                <status>0x86</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[3].input</key>
                <status>0x87</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].slip.input</key>
                <status>0x90</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].slip.input</key>
                <status>0x91</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].slip.input</key>
                <status>0x92</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].slip.input</key>
                <status>0x93</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[3].input</key>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[3].input</key>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[3].input</key>
                <status>0x96</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[3].input</key>
                <status>0x97</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].shiftButton.input</key>
                <status>0x80</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].shiftButton.input</key>
                <status>0x81</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].shiftButton.input</key>
                <status>0x82</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].shiftButton.input</key>
                <status>0x83</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[4].input</key>
                <status>0x84</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[4].input</key>
                <status>0x85</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[4].input</key>
                <status>0x86</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[4].input</key>
                <status>0x87</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[1].input</key>
                <status>0x88</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[1].input</key>
                <status>0x89</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].shiftButton.input</key>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].shiftButton.input</key>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].shiftButton.input</key>
                <status>0x92</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].shiftButton.input</key>
                <status>0x93</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[4].input</key>
                <status>0x94</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[4].input</key>
                <status>0x95</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[4].input</key>
                <status>0x96</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[4].input</key>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[1].input</key>
                <status>0x98</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[1].input</key>
                <status>0x99</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[5].input</key>
                <status>0x84</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[5].input</key>
                <status>0x85</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[5].input</key>
                <status>0x86</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[5].input</key>
                <status>0x87</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[2].input</key>
                <status>0x88</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[2].input</key>
                <status>0x89</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[5].input</key>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[5].input</key>
                <status>0x95</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[5].input</key>
                <status>0x96</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[5].input</key>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[2].input</key>
                <status>0x98</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[2].input</key>
                <status>0x99</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[6].input</key>
                <status>0x84</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[6].input</key>
                <status>0x85</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[6].input</key>
                <status>0x86</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[6].input</key>
                <status>0x87</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[3].input</key>
                <status>0x88</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[3].input</key>
                <status>0x89</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[6].input</key>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[6].input</key>
                <status>0x95</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[6].input</key>
                <status>0x96</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[6].input</key>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].fxCaps[3].input</key>
                <status>0x98</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].fxCaps[3].input</key>
                <status>0x99</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[7].input</key>
                <status>0x84</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[7].input</key>
                <status>0x85</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[7].input</key>
                <status>0x86</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[7].input</key>
                <status>0x87</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.pads[7].input</key>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.pads[7].input</key>
                <status>0x95</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.pads[7].input</key>
                <status>0x96</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.pads[7].input</key>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x84</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x85</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x86</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x87</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x94</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x95</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x96</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.parameterLeft.input</key>
                <status>0x97</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.parameterRight.input</key>
                <status>0x84</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.parameterRight.input</key>
                <status>0x85</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.parameterRight.input</key>
                <status>0x86</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.parameterRight.input</key>
                <status>0x87</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].padUnit.padsContainer.parameterRight.input</key>
                <status>0x94</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].padUnit.padsContainer.parameterRight.input</key>
                <status>0x95</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].padUnit.padsContainer.parameterRight.input</key>
                <status>0x96</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].padUnit.padsContainer.parameterRight.input</key>
                <status>0x97</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitch.inputLSB</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitch.inputLSB</key>
                <status>0xB1</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitch.inputLSB</key>
                <status>0xB2</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitch.inputLSB</key>
                <status>0xB3</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendPlus.input</key>
                <status>0x80</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendPlus.input</key>
                <status>0x81</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendPlus.input</key>
                <status>0x82</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendPlus.input</key>
                <status>0x83</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendPlus.input</key>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendPlus.input</key>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendPlus.input</key>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendPlus.input</key>
                <status>0x93</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].stripSearch.inputMSB</key>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].stripSearch.inputMSB</key>
                <status>0xB1</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].stripSearch.inputMSB</key>
                <status>0xB2</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].stripSearch.inputMSB</key>
                <status>0xB3</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendMinus.input</key>
                <status>0x80</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendMinus.input</key>
                <status>0x81</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendMinus.input</key>
                <status>0x82</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendMinus.input</key>
                <status>0x83</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].pitchBendMinus.input</key>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].pitchBendMinus.input</key>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].pitchBendMinus.input</key>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].pitchBendMinus.input</key>
                <status>0x93</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].mixMode.input</key>
                <status>0x88</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].mixMode.input</key>
                <status>0x89</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>NS6II.EffectUnits[1].mixMode.input</key>
                <status>0x98</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>NS6II.EffectUnits[2].mixMode.input</key>
                <status>0x99</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].scratch.input</key>
                <status>0x80</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].scratch.input</key>
                <status>0x81</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].scratch.input</key>
                <status>0x82</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].scratch.input</key>
                <status>0x83</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].scratch.input</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].scratch.input</key>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].scratch.input</key>
                <status>0x92</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].scratch.input</key>
                <status>0x93</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.decks[0].stripSearch.inputLSB</key>
                <status>0xB0</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.decks[1].stripSearch.inputLSB</key>
                <status>0xB1</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.decks[2].stripSearch.inputLSB</key>
                <status>0xB2</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.decks[3].stripSearch.inputLSB</key>
                <status>0xB3</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.mixer.extInputChannel3.input</key>
                <status>0x9F</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.knobCapBehavior.input</key>
                <status>0x9F</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[NS6II]</group>
                <key>NS6II.filterKnobBehavior.input</key>
                <status>0x9F</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.mixer.extInputChannel4.input</key>
                <status>0x9F</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>NS6II.deckWatcherInput</key>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>NS6II.deckWatcherInput</key>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>NS6II.deckWatcherInput</key>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>NS6II.deckWatcherInput</key>
                <status>0x93</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>NS6II.PCSelectorInput</key>
                <status>0x8F</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>NS6II.PCSelectorInput</key>
                <status>0x9F</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>NS6II.PCSelectorInput</key>
                <status>0x8F</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key>NS6II.PCSelectorInput</key>
                <status>0x9F</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
