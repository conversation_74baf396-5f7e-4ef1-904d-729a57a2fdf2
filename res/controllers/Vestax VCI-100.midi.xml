<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.8.0+">
  <info>
    <name>Vestax VCI-100</name>
    <author><PERSON></author>
    <description>Preliminary controller mapping for Vestax VCI-100 with scripts</description>
    <manual>vestax_vci_100mki</manual>
  </info>
  <controller id="Vestax VCI-100">
    <scriptfiles>
       <file filename="Vestax-VCI-100-scripts.js" functionprefix="VestaxVCI100"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x08</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x07</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headVolume</key>
        <status>0xB0</status>
        <midino>0x58</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <status>0xB0</status>
        <midino>0x59</midino>
      </control>
      <control>
        <group>[Master]</group>
        <key>balance</key>
        <status>0xB0</status>
        <midino>0x1f</midino>
      </control>

      <!-- Loop & Hotcues -->
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key1</key>
        <status>0x90</status>
        <midino>0x62</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key1</key>
        <status>0x80</status>
        <midino>0x62</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key2</key>
        <status>0x90</status>
        <midino>0x63</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key2</key>
        <status>0x80</status>
        <midino>0x63</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key3</key>
        <status>0x90</status>
        <midino>0x64</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>VestaxVCI100.key3</key>
        <status>0x80</status>
        <midino>0x64</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI100.loopMode</key>
        <status>0x90</status>
        <midino>0x65</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI100.loopMode</key>
        <status>0x80</status>
        <midino>0x65</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI100.hotcueMode</key>
        <status>0x90</status>
        <midino>0x66</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI100.hotcueMode</key>
        <status>0x80</status>
        <midino>0x66</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI100.loopMode</key>
        <status>0x90</status>
        <midino>0x67</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI100.loopMode</key>
        <status>0x80</status>
        <midino>0x67</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI100.hotcueMode</key>
        <status>0x90</status>
        <midino>0x68</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>VestaxVCI100.hotcueMode</key>
        <status>0x80</status>
        <midino>0x68</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB0</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0xB0</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB0</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x14</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x15</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x16</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x17</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0e</midino>
        <options>
          <invert/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>VestaxVCI100.jog_touch</key>
        <status>0x90</status>
        <midino>0x30</midino>
        <options>
           <Script-Binding/>
        </options>
      </control>
      <control>
          <group>[Channel1]</group>
          <key>VestaxVCI100.jog_wheel_scratch</key>
          <status>0xB0</status>
          <midino>0x10</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
          <group>[Channel1]</group>
          <key>VestaxVCI100.jog_wheel</key>
          <status>0xB0</status>
          <midino>0x12</midino>
          <options>
              <Script-Binding/>
          </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x33</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x35</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x35</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x80</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x42</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x80</status>
        <midino>0x46</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x60</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x80</status>
        <midino>0x60</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x3a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x3a</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x3b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x3b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x18</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x19</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x1a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x1b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0f</midino>
        <options>
          <invert/>
        </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI100.jog_touch</key>
         <status>0x90</status>
         <midino>0x31</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI100.jog_wheel_scratch</key>
         <status>0xB0</status>
         <midino>0x11</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
         <group>[Channel2]</group>
         <key>VestaxVCI100.jog_wheel</key>
         <status>0xB0</status>
         <midino>0x13</midino>
         <options>
            <Script-Binding/>
         </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x37</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x37</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x38</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x39</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x80</status>
        <midino>0x49</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x90</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>back</key>
        <status>0x80</status>
        <midino>0x45</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>fwd</key>
        <status>0x80</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x4b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x4b</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x61</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x80</status>
        <midino>0x61</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x80</status>
        <midino>0x3d</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB0</status>
        <midino>0x54</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDelay</key>
        <status>0xB0</status>
        <midino>0x55</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB0</status>
        <midino>0x56</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x90</status>
        <midino>0x5d</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x80</status>
        <midino>0x5d</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x90</status>
        <midino>0x5c</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x80</status>
        <midino>0x5c</midino>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
