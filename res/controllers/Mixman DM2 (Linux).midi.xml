<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset mixxxVersion="1.7.0+" schemaVersion="1">
  <info>
    <name>Mixman DM2 (Linux)</name>
    <author><PERSON></author>
    <description>This is the DM2 mapping for the Linux driver (dm2linux.sourceforge.net)</description>
    <manual>mixman_dm2</manual>
  </info>
  <controller id="Mixman DM2 (Linux)">
    <scriptfiles>
      <file functionprefix="DM2" filename="Mixman DM2 (Linux).js"/>
    </scriptfiles>
    <controls>
      <!-- Wheels map to "scratch", but with different scaling, see script. -->
      <control>
        <status>0xb0</status>
        <midino>0x01</midino>
        <group>[Channel1]</group>
        <key>DM2.scratch1</key>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x03</midino>
        <group>[Channel2]</group>
        <key>DM2.scratch2</key>
        <options>
          <script-binding/>
        </options>
      </control>

      <!-- Master controls -->

      <control>
        <status>0xb0</status>
        <midino>0x2</midino>
        <group>[Master]</group>
        <key>crossfader</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x79</midino>
        <group>[Master]</group>
        <key>volume</key>
        <options>
          <normal/>
        </options>
      </control>

      <!-- Flanger -->

      <control>
        <status>0x90</status>
        <midino>0x35</midino>
        <group>[Channel1]</group>
        <key>flanger</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x33</midino>
        <group>[Channel2]</group>
        <key>flanger</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x04</midino>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <options>
          <normal/>
        </options>
      </control>
      <!-- We have a joystick with two axes, so use a key to shift one axis. -->
      <control>
        <status>0x90</status>
        <midino>0x34</midino>
        <group>[Flanger]</group>
        <key>DM2.shift</key>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x05</midino>
        <group>[Flanger]</group>
        <key>DM2.delay_period</key>
        <options>
          <script-binding/>
        </options>
      </control>

      <!-- Playlist controls and track loading -->

      <control>
        <status>0x90</status>
        <midino>0x41</midino>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x42</midino>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x43</midino>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x44</midino>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <options>
          <normal/>
        </options>
      </control>

      <!-- Basic controls Ch1 -->

      <control>
        <status>0x90</status>
        <midino>0x37</midino>
        <group>[Channel1]</group>
        <key>play</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x14</midino>
        <group>[Channel1]</group>
        <key>reverse</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x14</midino>
        <group>[Channel1]</group>
        <key>playposition</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x17</midino>
        <group>[Channel1]</group>
        <key>pregain</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x16</midino>
        <group>[Channel1]</group>
        <key>rate</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x16</midino>
        <group>[Channel1]</group>
        <key>rate_perm_up_small</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x15</midino>
        <group>[Channel1]</group>
        <key>rate_perm_down_small</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3b</midino>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x15</midino>
        <group>[Channel1]</group>
        <key>volume</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3f</midino>
        <group>[Channel1]</group>
        <key>back</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3e</midino>
        <group>[Channel1]</group>
        <key>fwd</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3d</midino>
        <group>[Channel1]</group>
        <key>pfl</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3c</midino>
        <group>[Channel1]</group>
        <key>DM2.beatsync</key>
        <options>
          <script-binding/>
        </options>
      </control>

      <!-- Basic controls Ch2 -->

      <control>
        <status>0x90</status>
        <midino>0x36</midino>
        <group>[Channel2]</group>
        <key>play</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x24</midino>
        <group>[Channel2]</group>
        <key>reverse</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x24</midino>
        <group>[Channel2]</group>
        <key>playposition</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x27</midino>
        <group>[Channel2]</group>
        <key>pregain</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x26</midino>
        <group>[Channel2]</group>
        <key>rate</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x26</midino>
        <group>[Channel2]</group>
        <key>rate_perm_up_small</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x25</midino>
        <group>[Channel2]</group>
        <key>rate_perm_down_small</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x3a</midino>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x25</midino>
        <group>[Channel2]</group>
        <key>volume</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x30</midino>
        <group>[Channel2]</group>
        <key>back</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x31</midino>
        <group>[Channel2]</group>
        <key>fwd</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x32</midino>
        <group>[Channel2]</group>
        <key>pfl</key>
        <options>
          <normal/>
        </options>
      </control>

      <!-- Filtering controls -->

      <control>
        <status>0x90</status>
        <midino>0x10</midino>
        <group>[Channel1]</group>
        <key>filterHighKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x11</midino>
        <group>[Channel1]</group>
        <key>filterMidKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x12</midino>
        <group>[Channel1]</group>
        <key>filterLowKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x20</midino>
        <group>[Channel2]</group>
        <key>filterHighKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x21</midino>
        <group>[Channel2]</group>
        <key>filterMidKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0x90</status>
        <midino>0x22</midino>
        <group>[Channel2]</group>
        <key>filterLowKill</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x10</midino>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x11</midino>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x12</midino>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x20</midino>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x21</midino>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <status>0xb0</status>
        <midino>0x22</midino>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <options>
          <normal/>
        </options>
      </control>
    </controls>



    <!-- Light show while playing a track -->
    <outputs>
      <output>
	<group>[Channel1]</group>
	<key>play</key>
	<status>0xB0</status>
	<midino>0x58</midino>
	<minimum>0.5</minimum>
      </output>
      <output>
	<group>[Channel2]</group>
	<key>play</key>
	<status>0xB0</status>
	<midino>0x59</midino>
	<minimum>0.5</minimum>
      </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
