<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Ion Discover DJ Pro</name>
        <author>j<PERSON><PERSON><PERSON>,<PERSON><PERSON>oman</author>
        <description>A mapping for the Ion Discover DJ Pro, a 2-deck controller with integrated audio interface and iPhone dock.</description>
        <wiki>https://mixxx.org/wiki/doku.php/ion_discover_dj_pro</wiki>
        <forums>https://mixxx.org/forums/viewtopic.php?f=7&amp;t=3343</forums>
    </info>
    <controller id="DISCOVER DJ PRO MIDI 1">
        <scriptfiles>
            <file functionprefix="IonDiscoverDjPro" filename="Ion-Discover-DJ-Pro-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Master]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Playlist]</group>
                <key>IonDiscoverDjPro.selectKnob</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x69</midino>
                <group>[Playlist]</group>
                <key>IonDiscoverDjPro.toggleDirectoryMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>IonDiscoverDjPro.toggleDirectoryMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.playFromCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x65</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5C</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5B</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5A</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x61</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x54</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.loopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x55</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x44</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x51</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x63</midino>
                <group>[Channel1]</group>
                <key>reverse</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x64</midino>
                <group>[Channel2]</group>
                <key>reverse</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x48</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4e</midino>
                <group>[Channel1]</group>
                <key>IonDiscoverDjPro.WheelTouch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x66</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x60</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5f</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5e</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x62</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.loopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x58</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x46</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x50</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>-
            <control>
                <status>0xb0</status>
                <midino>0x4d</midino>
                <group>[Channel2]</group>
                <key>IonDiscoverDjPro.WheelTouch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3b</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5a</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x65</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5a</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5b</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>reverse</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x63</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x70</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x51</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x42</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x66</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5e</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x5f</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x60</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>reverse</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x64</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x71</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x52</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
