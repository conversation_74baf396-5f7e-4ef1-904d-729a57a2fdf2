<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.3.1" schemaVersion="1">
    <info>
    	<name>Denon MC7000</name>
    	<author>OsZ, JL</author>
    	<description>A professional 4-channel DJ controller featuring 2 separate audio interfaces. Please refer to the Mixxx user manual to get the audio interfaces working on Linux.</description>
    	<forums>https://mixxx.discourse.group/t/denon-mc7000-mapping/18235</forums>
    	<wiki>https://github.com/mixxxdj/mixxx/wiki/Denon-MC7000</wiki>
        <manual>denon_mc7000</manual>
    </info>
    <settings>
        <group label="Jogwheels">
            <group label="General">
                <option
                    variable="jogSensitivity"
                    type="real"
                    min="0.05"
                    max="10.0"
                    default="1.0"
                    label="Jogwheel sensitivity">
                    <description>
                        Sensitivity factor of the jog wheel (also depends on audio latency)
                        0.5 for half, 2 for double sensitivity - Recommendation:
                        set to 0.5 with audio buffer set to 50ms
                        set to 1 with audio buffer set to 25ms
                        set to 3 with audio buffer set to 5ms
                    </description>
                </option>
            </group>
            <group label="Acceleration">
                <option
                    variable="jogAccelerationEnabled"
                    type="boolean"
                    default="false"
                    label="Enable jogwheel acceleration">
                    <description>
                        If enabled, the track speed will accelerate faster than the physical jogwheel movement. Be aware that the absolute track position will drift relative to the jogwheel position in this mode!
                        (exponent: 0 and coefficient: 1 = no acceleration)
                    </description>
                </option>
                <option
                    variable="jogAccelerationExponent"
                    type="real"
                    min="0"
                    max="20.0"
                    default="0.8"
                    step="0.1"
                    label="Acceleration exponent">
                    <description>
                        The exponent of the acceleration curve
                    </description>
                </option>
                <option
                    variable="jogAccelerationCoefficient"
                    type="real"
                    min="0.05"
                    max="20.0"
                    step="0.1"
                    default="1.0"
                    label="Acceleration coefficient">
                    <description>
                        The scaling factor of the acceleration curve
                    </description>
                </option>
            </group>
        </group>
        <group label="Alternative Mapping">
            <group label="Parameter Buttons">
                <option
                    variable="parameterButtonMode"
                    type="enum"
                    label="Parameter Button Mode">
                    <value label="Change Star Rating (Shift: Track Color)" default="true">starsAndColor</value>
                    <value label="Perform Beat Jump (Shift: Adjust Jump Size)">beatjump</value>
                    <value label="Set Intro/Outro (Shift: Clear Intro/Outro)">introOutro</value>
                    <description>
                        Change the functionality of the orange parameter buttons
                        in the bottom left/right corner of the controller.
                    </description>
                </option>
                <option
                    variable="parameterButtonPitchPlayOverrideEnabled"
                    label="Override parameter button mode during pitch play to set pitch range"
                    type="boolean"
                    default="true">
                    <description>
                        Whether to use the parameter buttons to change the pitch
                        range during pitch play mode. If this option is enabled,
                        the pitch change functionality overrides the normal
                        parameter button mode during pitch play.
                    </description>
                </option>
            </group>
        </group>
        <group label="Samplers">
            <option
                variable="prevSamplerStop"
                label="Auto-stop previous samplers when starting new sampler"
                type="boolean"
                default="true">
                <description>
                    Whether the previous sampler shall stop before a new sampler starts.
                    When true, a running sampler will stop before the new sampler starts.
                    When false, all triggered samplers will play simultaneously.
                </description>
            </option>
            <option
                variable="samplerQty"
                type="enum"
                label="Quantity">
                <value label="16" default="true">16</value>
                <value label="32">32</value>
                <description>
                    The number of samplers to use. When 32 samplers are used,
                    deck 1 will trigger sampler 1 to 8, deck 2 will trigger
                    sampler 9 to 16, deck 3 will trigger sampler 17 to 24 and
                    deck 4 will trigger sampler 25 to 32. Please note that your
                    Mixxx skin needs to support more than 16 samplers.
                </description>
            </option>
        </group>
    </settings>
    <controller id="DENON MC7000">
        <scriptfiles>
            <file filename="Denon-MC7000-scripts.js" functionprefix="MC7000"/>
        </scriptfiles>
        <controls>
<!--  SCRIPT BINDINGS  -->
    <!--  PAD MODE  -->
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeCue</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeCue</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeCue</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeCue</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeCueLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeCueLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeCueLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeCueLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeFlip</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeFlip</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeFlip</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeFlip</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeRoll</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeRoll</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeRoll</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeRoll</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeSavedLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeSavedLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeSavedLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeSavedLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeSlicer</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeSlicer</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeSlicer</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeSlicer</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeSlicerLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeSlicerLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeSlicerLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeSlicerLoop</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeSampler</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeSampler</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeSampler</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeSampler</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModeVelSamp</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModeVelSamp</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModeVelSamp</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModeVelSamp</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.padModePitch</key>
                <description>Pad Mode HotCues</description>
                <status>0x94</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.padModePitch</key>
                <description>Pad Mode HotCues</description>
                <status>0x95</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.padModePitch</key>
                <description>Pad Mode HotCues</description>
                <status>0x96</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.padModePitch</key>
                <description>Pad Mode HotCues</description>
                <status>0x97</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.shiftButton</key>
                <description>Shift Button</description>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.shiftButton</key>
                <description>Shift Button</description>
                <status>0x91</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.shiftButton</key>
                <description>Shift Button</description>
                <status>0x92</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.shiftButton</key>
                <description>Shift Button</description>
                <status>0x93</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  PITCH FADER POSITION  -->
            <control>
                <group>[Channel1]</group>
                <key>MC7000.pitchFaderPosition</key>
                <description>MIDI Learned from 180 messages.</description>
                <status>0xB0</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.pitchFaderPosition</key>
                <description>MIDI Learned from 144 messages.</description>
                <status>0xB1</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.pitchFaderPosition</key>
                <description>MIDI Learned from 150 messages.</description>
                <status>0xB2</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.pitchFaderPosition</key>
                <description>MIDI Learned from 128 messages.</description>
                <status>0xB3</status>
                <midino>0x77</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.pitchFaderMSB</key>
                <description>MIDI Learned from 180 messages.</description>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.pitchFaderMSB</key>
                <description>MIDI Learned from 144 messages.</description>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.pitchFaderMSB</key>
                <description>MIDI Learned from 150 messages.</description>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.pitchFaderMSB</key>
                <description>MIDI Learned from 128 messages.</description>
                <status>0xB3</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
	<!--  Key Adjust -->
            <control>
                <group>[Channel1]</group>
                <key>MC7000.keySelect</key>
                <description>Pitch +/- needs tuning</description>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.keySelect</key>
                <description>Pitch +/- needs tuning</description>
                <status>0xB1</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.keySelect</key>
                <description>Pitch +/- needs tuning</description>
                <status>0xB2</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.keySelect</key>
                <description>Pitch +/- needs tuning</description>
                <status>0xB3</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
	<!--  CHANNEL ASSIGN TO CROSSFADER -->
  			<control>
                <group>[Channel1]</group>
                <key>MC7000.crossfaderAssign</key>
                <description>Ch1 THRU</description>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.crossfaderAssign</key>
                <description>Ch2 THRU</description>
                <status>0x91</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.crossfaderAssign</key>
                <description>Ch3 THRU</description>
                <status>0x92</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.crossfaderAssign</key>
                <description>Ch4 THRU</description>
                <status>0x93</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  RATE RANGE  -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.prevRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.prevRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.prevRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.prevRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x93</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    		<control>
                <group>[Channel1]</group>
                <key>MC7000.nextRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.nextRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.nextRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.nextRateRange</key>
                <description>Toggle next Rate Range</description>
                <status>0x93</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  NEEDLE SEARCH  -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.needleSearchTouch</key>
                <description>Touch detection for Needle Search </description>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.needleSearchTouchShift</key>
                <description>"Shift" + Touch detection for Needle Search </description>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.needleSearchMSB</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.needleSearchStripPosition</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB0</status>
                <midino>0x78</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.needleSearchTouch</key>
                <description>Touch detection for Needle Search </description>
                <status>0x91</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.needleSearchTouchShift</key>
                <description>"Shift" + Touch detection for Needle Search </description>
                <status>0x91</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.needleSearchMSB</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB1</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.needleSearchStripPosition</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB1</status>
                <midino>0x78</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.needleSearchTouch</key>
                <description>Touch detection for Needle Search </description>
                <status>0x92</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.needleSearchTouchShift</key>
                <description>"Shift" + Touch detection for Needle Search </description>
                <status>0x92</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.needleSearchMSB</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB2</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.needleSearchStripPosition</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB2</status>
                <midino>0x78</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.needleSearchTouch</key>
                <description>Touch detection for Needle Search </description>
                <status>0x93</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.needleSearchTouchShift</key>
                <description>"Shift" + Touch detection for Needle Search </description>
                <status>0x93</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.needleSearchMSB</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB3</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.needleSearchStripPosition</key>
                <description>Jump to track position (Needle Search)</description>
                <status>0xB3</status>
                <midino>0x78</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  DECK BUTTONS  -->
            <control>
                <group>[Channel1]</group>
                <key>MC7000.switchDeck</key>
                <description>Switch to deck 1</description>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.switchDeck</key>
                <description>Switch to deck 2</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.switchDeck</key>
                <description>Switch to deck 3</description>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.switchDeck</key>
                <description>Switch to deck 4</description>
                <status>0x93</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  FX WET/DRY + SAMPLER + CROSS FADER CURVE  -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>MC7000.fxWetDry</key>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>MC7000.fxWetDry</key>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Master]</group>
                <key>MC7000.samplerLevel</key>
                <status>0xBF</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
	        <control>
                <group>[Mixer Profile]</group>
                <key>MC7000.crossFaderCurve</key>
                <description>sets the Crossfader Curve</description>
                <status>0xBF</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
    <!--  JOG WHEEL  -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.vinylModeToggle</key>
                <description>Vinyl toggle</description>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.vinylModeToggle</key>
                <description>Vinyl toggle</description>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.vinylModeToggle</key>
                <description>Vinyl toggle</description>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.vinylModeToggle</key>
                <description>Vinyl toggle</description>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.wheelTouch</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.wheelTouch</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.wheelTouch</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.wheelTouch</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.wheelTurn</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.wheelTurn</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.wheelTurn</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.wheelTurn</key>
                <description>MIDI Learned from 759 messages.</description>
                <status>0xB3</status>
                <midino>0x06</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
<!--  EQ / FILTER -->
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <description>Hi Ch1</description>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <description>Hi Ch2</description>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter3</key>
                <description>Hi Ch3</description>
                <status>0xB2</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
  			<control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter3</key>
                <description>Hi Ch4</description>
                <status>0xB3</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <description>MID Ch1</description>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <description>MID Ch2</description>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter2</key>
                <description>MID Ch3</description>
                <status>0xB2</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter2</key>
                <description>MID Ch4</description>
                <status>0xB3</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <description>LOW Ch1</description>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <description>LOW Ch2</description>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>parameter1</key>
                <description>LOW Ch3</description>
                <status>0xB2</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>parameter1</key>
                <description>LOW Ch4</description>
                <status>0xB3</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>super1</key>
                <description>Filter Ch1</description>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>super1</key>
                <description>Filter Ch2</description>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>super1</key>
                <description>Filter Ch3</description>
                <status>0xB2</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>super1</key>
                <description>Filter Ch4</description>
                <status>0xB3</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!--  GAIN / FADERS / PFL -->
			<control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>Level / Gain Ch1</description>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>Level / Gain Ch2</description>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>pregain</key>
                <description>Level / Gain Ch3</description>
                <status>0xB2</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>pregain</key>
                <description>Level / Gain Ch4</description>
                <status>0xB3</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>Line Fader Ch1</description>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>Line Fader Ch2</description>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>volume</key>
                <description>Line Fader Ch3</description>
                <status>0xB2</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>volume</key>
                <description>Line Fader Ch4</description>
                <status>0xB3</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <description>Crossfader</description>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>PFL Ch1</description>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>PFL Ch2</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>pfl</key>
                <description>PFL Ch3</description>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>pfl</key>
                <description>PFL Ch4</description>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!--  PLAY / CUE / SYNC  -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.play</key>
                <description>Play Ch1</description>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.play</key>
                <description>Play Ch2</description>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.play</key>
                <description>Play Ch3</description>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.play</key>
                <description>Play Ch4</description>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play_stutter</key>
                <description>Stutter play Ch1</description>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play_stutter</key>
                <description>Stutter play Ch2</description>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>play_stutter</key>
                <description>Stutter play Ch3</description>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>play_stutter</key>
                <description>Stutter play Ch4</description>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[PreviewDeck1]</group>
                <key>play</key>
                <description>Play Preview Deck</description>
                <status>0x9F</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>CUE Ch1</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>CUE Ch2</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>cue_default</key>
                <description>CUE Ch3</description>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>cue_default</key>
                <description>CUE Ch4</description>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>start_stop</key>
                <description>start stop Ch1</description>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>start_stop</key>
                <description>start stop Ch2</description>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>start_stop</key>
                <description>start stop Ch3</description>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>start_stop</key>
                <description>start stop Ch4</description>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>SYNC Ch1</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>SYNC Ch2</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>sync_enabled</key>
                <description>SYNC Ch3</description>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>sync_enabled</key>
                <description>SYNC Ch4</description>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!--  BEAT GRID / LOOP  -->
            <control>
                <group>[Channel1]</group>
                <key>beats_translate_curpos</key>
                <description>BeatGrid Adjust Ch1</description>
                <status>0x94</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beats_translate_curpos</key>
                <description>BeatGrid Adjust Ch2</description>
                <status>0x95</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beats_translate_curpos</key>
                <description>BeatGrid Adjust Ch3</description>
                <status>0x96</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beats_translate_curpos</key>
                <description>BeatGrid Adjust Ch4</description>
                <status>0x97</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beats_translate_match_alignment</key>
                <description>BeatGrid Slide Ch1</description>
                <status>0x94</status>
                <midino>0x48</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beats_translate_match_alignment</key>
                <description>BeatGrid Slide Ch2</description>
                <status>0x95</status>
                <midino>0x48</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beats_translate_match_alignment</key>
                <description>BeatGrid Slide Ch3</description>
                <status>0x96</status>
                <midino>0x48</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beats_translate_match_alignment</key>
                <description>BeatGrid Slide Ch4</description>
                <status>0x97</status>
                <midino>0x48</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x94</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x95</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x96</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x97</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_activate</key>
                <description>AutoLoop Ch1</description>
                <status>0x94</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_activate</key>
                <description>AutoLoop Ch2</description>
                <status>0x95</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_activate</key>
                <description>AutoLoop Ch3</description>
                <status>0x96</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_activate</key>
                <description>AutoLoop Ch4</description>
                <status>0x97</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description>X1/2 Ch1</description>
                <status>0x94</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_halve</key>
                <description>X1/2 Ch2</description>
                <status>0x95</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_halve</key>
                <description>X1/2 Ch3</description>
                <status>0x96</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_halve</key>
                <description>X1/2 Ch4</description>
                <status>0x97</status>
                <midino>0x34</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <description>X2 Ch1</description>
                <status>0x94</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <description>X2 Ch2</description>
                <status>0x95</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_double</key>
                <description>X2 Ch3</description>
                <status>0x96</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_double</key>
                <description>X2 Ch4</description>
                <status>0x97</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description>Loop in Ch1</description>
                <status>0x94</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description>Loop in Ch2</description>
                <status>0x95</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_in</key>
                <description>Loop in Ch3</description>
                <status>0x96</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_in</key>
                <description>Loop in Ch4</description>
                <status>0x97</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description>Loop out Ch1</description>
                <status>0x94</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description>Loop out Ch2</description>
                <status>0x95</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_out</key>
                <description>Loop out Ch3</description>
                <status>0x96</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_out</key>
                <description>Loop out Ch4</description>
                <status>0x97</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reloop_toggle</key>
                <description>Reloop Ch1</description>
                <status>0x94</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_toggle</key>
                <description>Reloop Ch2</description>
                <status>0x95</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>reloop_toggle</key>
                <description>Reloop Ch3</description>
                <status>0x96</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>reloop_toggle</key>
                <description>Reloop Ch4</description>
                <status>0x97</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!-- PARAMETER BUTTONS -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.parameterButtonLeft</key>
                <description></description>
                <status>0x94</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.parameterButtonRight</key>
                <description></description>
                <status>0x94</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.parameterButtonLeftShifted</key>
                <description></description>
                <status>0x94</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.parameterButtonRightShifted</key>
                <description></description>
                <status>0x94</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.parameterButtonLeft</key>
                <description></description>
                <status>0x95</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.parameterButtonRight</key>
                <description></description>
                <status>0x95</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.parameterButtonLeftShifted</key>
                <description></description>
                <status>0x95</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.parameterButtonRightShifted</key>
                <description></description>
                <status>0x95</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.parameterButtonLeft</key>
                <description></description>
                <status>0x96</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.parameterButtonRight</key>
                <description></description>
                <status>0x96</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.parameterButtonLeftShifted</key>
                <description></description>
                <status>0x96</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.parameterButtonRightShifted</key>
                <description></description>
                <status>0x96</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.parameterButtonLeft</key>
                <description></description>
                <status>0x97</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.parameterButtonRight</key>
                <description></description>
                <status>0x97</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.parameterButtonLeftShifted</key>
                <description></description>
                <status>0x97</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.parameterButtonRightShifted</key>
                <description></description>
                <status>0x97</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
<!-- LIBRARY -->
            <control>
                <group>[Library]</group>
                <key>MoveVertical</key>
                <description>Select line</description>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>ScrollVertical</key>
                <description>Select page</description>
                <status>0xBF</status>
                <midino>0x01</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.loadButton</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x9F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.loadButton</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x9F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.loadButton</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x9F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.loadButton</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x9F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>GoToItem</key>
                <description>Select Item</description>
                <status>0x9F</status>
                <midino>0x1F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MoveFocusForward</key>
                <description>change active panel</description>
                <status>0x9F</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MoveFocusBackward</key>
                <description>change active panel</description>
                <status>0x9F</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[PreviewDeck1]</group>
                <key>LoadSelectedTrack</key>
                <description>Load in Preview deck</description>
                <status>0x9F</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1]</group>
                <key>show</key>
                <description>Show Effect Rack</description>
                <status>0x9F</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <description>Full screen Library</description>
                <status>0x9F</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MC7000.sortLibrary</key>
                <description>BPM</description>
                <status>0x9F</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MC7000.sortLibrary</key>
                <description>KEY</description>
                <status>0x9F</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MC7000.sortLibrary</key>
                <description>Artist</description>
                <status>0x9F</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>MC7000.sortLibrary</key>
                <description>Title</description>
                <status>0x9F</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
<!-- EFFECTS -->
    <!-- ASSIGN EFFECT RACK TO DECK -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <description>FX 1 to Ch1</description>
                <status>0x98</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <description>FX 1 to Ch2</description>
                <status>0x98</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel3]_enable</key>
                <description>FX 1 to Ch3</description>
                <status>0x98</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel4]_enable</key>
                <description>FX 1 to Ch4</description>
                <status>0x98</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <description>FX 2 to Ch1</description>
                <status>0x99</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <description>FX 2 to Ch2</description>
                <status>0x99</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel3]_enable</key>
                <description>FX 2 to Ch3</description>
                <status>0x99</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel4]_enable</key>
                <description>FX 2 to Ch4</description>
                <status>0x99</status>
                <midino>0x08</midino>
                <options>
                    <normal/>
                </options>
            </control>
    <!-- FX1 -->
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <description>FX1 1 on</description>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <description>FX1 2 on</description>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <description>FX1 3 on</description>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>meta</key>
                <description>FX1 Level 1</description>
                <status>0xB8</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>meta</key>
                <description>FX1 Level 2</description>
                <status>0xB8</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>meta</key>
                <description>FX1 Level 3</description>
                <status>0xB8</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
    <!-- FX2 -->
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <description>FX2 1 on</description>
                <status>0x99</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <description>FX2 2 on</description>
                <status>0x99</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <description>FX2 3 on</description>
                <status>0x99</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>meta</key>
                <description>FX2 Level 1</description>
                <status>0xB9</status>
                <midino>0x00</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>meta</key>
                <description>FX2 Level 2</description>
                <status>0xB9</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>meta</key>
                <description>FX2 Level 3</description>
                <status>0xB9</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
    <!-- FX Select -->
			<control>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>next_effect</key>
                <description>FX1 1</description>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>next_effect</key>
                <description>FX2 1</description>
                <status>0x99</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>next_effect</key>
                <description>FX1 3</description>
                <status>0x98</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>next_effect</key>
                <description>FX1 2</description>
                <status>0x98</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>next_effect</key>
                <description>FX2 3</description>
                <status>0x99</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>next_effect</key>
                <description>FX2 2</description>
                <status>0x99</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
    <!-- FX TAP BUTTON ON FX MASTER AND HEADPHONE -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Master]_enable</key>
                <description>FX1 on master</description>
                <status>0x98</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Master]_enable</key>
                <description>FX2 on master</description>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
  			<control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Headphone]_enable</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x98</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Headphone]_enable</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x99</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!-- SLIP / CENSOR / REVERSE -->
	  		<control>
                <group>[Channel1]</group>
                <key>slip_enabled</key>
                <description>MIDI Learned from 12 messages.</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>slip_enabled</key>
                <description>MIDI Learned from 124 messages.</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>slip_enabled</key>
                <description>MIDI Learned from 70 messages.</description>
                <status>0x92</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>slip_enabled</key>
                <description>MIDI Learned from 160 messages.</description>
                <status>0x93</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.censor</key>
                <description>Backspin Ch1</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.censor</key>
                <description>Backspin Ch2</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.censor</key>
                <description>Backspin Ch3</description>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.censor</key>
                <description>Backspin Ch4</description>
                <status>0x93</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.stopTime</key>
                <description>Stop Time Ch1</description>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.stopTime</key>
                <description>Stop Time Ch2</description>
                <status>0xB1</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.stopTime</key>
                <description>Stop Time Ch3</description>
                <status>0xB2</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.stopTime</key>
                <description>Stop Time Ch4</description>
                <status>0xB3</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.reverse</key>
                <description>Reverse Ch1</description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.reverse</key>
                <description>Reverse Ch2</description>
                <status>0x91</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.reverse</key>
                <description>Reverse Ch3</description>
                <status>0x92</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.reverse</key>
                <description>Reverse Ch4</description>
                <status>0x93</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
<!-- KEYs -->
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>Keylock Ch1</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>Keylock Ch2</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>keylock</key>
                <description>Keylock Ch3</description>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>keylock</key>
                <description>Keylock Ch4</description>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_key</key>
                <description>sync_key Ch1</description>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_key</key>
                <description>sync_key Ch2</description>
                <status>0x91</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>sync_key</key>
                <description>sync_key Ch3</description>
                <status>0x92</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>sync_key</key>
                <description>sync_key Ch4</description>
                <status>0x93</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.keyReset</key>
                <description>Default Key</description>
                <status>0x90</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>MC7000.keyReset</key>
                <description>Default Key</description>
                <status>0x91</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.keyReset</key>
                <description>Default Key</description>
                <status>0x92</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.keyReset</key>
                <description>Default Key</description>
                <status>0x93</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
<!-- Pitch Bend -->
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down_small</key>
                <description>Pitch Bend - </description>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down_small</key>
                <description>Pitch Bend - </description>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_down_small</key>
                <description>Pitch Bend - </description>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_down_small</key>
                <description>Pitch Bend - </description>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up_small</key>
                <description>Pitch Bend + </description>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up_small</key>
                <description>Pitch Bend + </description>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_up_small</key>
                <description>Pitch Bend + </description>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_up_small</key>
                <description>Pitch Bend + </description>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
<!-- SET PAD -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-1</description>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-1</description>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-1</description>
                <status>0x96</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-1</description>
                <status>0x97</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-2</description>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-2</description>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-2</description>
                <status>0x96</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD-2</description>
                <status>0x97</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_3</description>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_3</description>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_3</description>
                <status>0x96</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_3</description>
                <status>0x97</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_4</description>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_4</description>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_4</description>
                <status>0x96</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_4</description>
                <status>0x97</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_5</description>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_5</description>
                <status>0x95</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_5</description>
                <status>0x96</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_5</description>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_6</description>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_6</description>
                <status>0x95</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_6</description>
                <status>0x96</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_6</description>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_7</description>
                <status>0x94</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_7</description>
                <status>0x95</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_7</description>
                <status>0x96</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_7</description>
                <status>0x97</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_8</description>
                <status>0x94</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_8</description>
                <status>0x95</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_8</description>
                <status>0x96</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>PAD_8</description>
                <status>0x97</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
<!-- CLEAR PAD -->
			<control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-1</description>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-1</description>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-1</description>
                <status>0x96</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-1</description>
                <status>0x97</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-2</description>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-2</description>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-2</description>
                <status>0x96</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD-2</description>
                <status>0x97</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_3</description>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_3</description>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_3</description>
                <status>0x96</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_3</description>
                <status>0x97</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_4</description>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_4</description>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_4</description>
                <status>0x96</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_4</description>
                <status>0x97</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_5</description>
                <status>0x94</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_5</description>
                <status>0x95</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_5</description>
                <status>0x96</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_5</description>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_6</description>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_6</description>
                <status>0x95</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_6</description>
                <status>0x96</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_6</description>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_7</description>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_7</description>
                <status>0x95</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_7</description>
                <status>0x96</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_7</description>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_8</description>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
                <group>[Channel2]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_8</description>
                <status>0x95</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_8</description>
                <status>0x96</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>MC7000.PadButtons</key>
                <description>CLEAR_PAD_8</description>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
<!-- GET THE LEDs WORKING -->
        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description>Autoloop LED</description>
                <status>0x94</status>
                <midino>0x32</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description>Reloop LED</description>
                <status>0x94</status>
                <midino>0x33</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description>Autoloop LED</description>
                <status>0x95</status>
                <midino>0x32</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description>Reloop LED</description>
                <status>0x95</status>
                <midino>0x33</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>loop_enabled</key>
                <description>Autoloop LED</description>
                <status>0x96</status>
                <midino>0x32</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>loop_enabled</key>
                <description>Reloop LED</description>
                <status>0x96</status>
                <midino>0x33</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>loop_enabled</key>
                <description>Autoloop LED</description>
                <status>0x97</status>
                <midino>0x32</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>loop_enabled</key>
                <description>Reloop LED</description>
                <status>0x97</status>
                <midino>0x33</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        	<output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <description>Play LED</description>
                <status>0x90</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        	<output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <description>Play LED</description>
                <status>0x91</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        	<output>
                <group>[Channel3]</group>
                <key>play_indicator</key>
                <description>Play LED</description>
                <status>0x92</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        	<output>
                <group>[Channel4]</group>
                <key>play_indicator</key>
                <description>Play LED</description>
                <status>0x93</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        	<output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <description>Cue LED</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <description>Cue LED</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>cue_indicator</key>
                <description>Cue LED</description>
                <status>0x92</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>cue_indicator</key>
                <description>Cue LED</description>
                <status>0x93</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <description>Sync LED</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <description>Sync LED</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>sync_enabled</key>
                <description>Sync LED</description>
                <status>0x92</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>sync_enabled</key>
                <description>Sync LED</description>
                <status>0x93</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>slip_enabled</key>
                <description>Slip LED</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>slip_enabled</key>
                <description>Slip LED</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>slip_enabled</key>
                <description>Slip LED</description>
                <status>0x92</status>
                <midino>0x0F</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>slip_enabled</key>
                <description>Slip LED</description>
                <status>0x93</status>
                <midino>0x0F</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>PFL LED</description>
                <status>0x90</status>
                <midino>0x1B</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>PFL LED</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>pfl</key>
                <description>PFL LED</description>
                <status>0x92</status>
                <midino>0x1B</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>pfl</key>
                <description>PFL LED</description>
                <status>0x93</status>
                <midino>0x1B</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>keylock LED</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>keylock LED</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <description>keylock LED</description>
                <status>0x92</status>
                <midino>0x0D</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <description>keylock LED</description>
                <status>0x93</status>
                <midino>0x0D</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>reverseroll</key>
                <description>Censor LED</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>reverseroll</key>
                <description>Censor LED</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>reverseroll</key>
                <description>Censor LED</description>
                <status>0x92</status>
                <midino>0x10</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>reverseroll</key>
                <description>Censor LED</description>
                <status>0x93</status>
                <midino>0x10</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
<!-- PAD LEDs -->

<!-- ASSIGN EFFECT RACK TO DECK -->
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <description>FX 1 to Ch1</description>
                <status>0x98</status>
                <midino>0x05</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <description>FX 1 to Ch2</description>
                <status>0x98</status>
                <midino>0x06</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel3]_enable</key>
                <description>FX 1 to Ch3</description>
                <status>0x98</status>
                <midino>0x07</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel4]_enable</key>
                <description>FX 1 to Ch4</description>
                <status>0x98</status>
                <midino>0x08</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <description>FX 2 to Ch1</description>
                <status>0x99</status>
                <midino>0x05</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <description>FX 2 to Ch2</description>
                <status>0x99</status>
                <midino>0x06</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel3]_enable</key>
                <description>FX 2 to Ch3</description>
                <status>0x99</status>
                <midino>0x07</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel4]_enable</key>
                <description>FX 2 to Ch4</description>
                <status>0x99</status>
                <midino>0x08</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
  <!-- FX1 -->
            <output>
                <group>[EffectRack1_EffectUnit1_Effect1]</group>
                <key>enabled</key>
                <description>FX1 1 on</description>
                <status>0x98</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect2]</group>
                <key>enabled</key>
                <description>FX1 2 on</description>
                <status>0x98</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1_Effect3]</group>
                <key>enabled</key>
                <description>FX1 3 on</description>
                <status>0x98</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect1]</group>
                <key>enabled</key>
                <description>FX2 1 on</description>
                <status>0x99</status>
                <midino>0x00</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect2]</group>
                <key>enabled</key>
                <description>FX2 2 on</description>
                <status>0x99</status>
                <midino>0x01</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2_Effect3]</group>
                <key>enabled</key>
                <description>FX2 3 on</description>
                <status>0x99</status>
                <midino>0x02</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
<!-- FX TAP BUTTON ON FX MASTER AND HEADPHONE -->
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Master]_enable</key>
                <description>FX1 on master</description>
                <status>0x98</status>
                <midino>0x04</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Master]_enable</key>
                <description>FX2 on master</description>
                <status>0x99</status>
                <midino>0x04</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Headphone]_enable</key>
                <status>0x98</status>
                <midino>0x0A</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Headphone]_enable</key>
                <status>0x99</status>
                <midino>0x0A</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_key</key>
                <description>sync_key Ch1</description>
                <status>0x90</status>
                <midino>0x29</midino>
                <on>0x02</on>
                <off>0xFF</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_key</key>
                <description>sync_key Ch2</description>
                <status>0x91</status>
                <midino>0x29</midino>
                <on>0x02</on>
                <off>0xFF</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>sync_key</key>
                <description>sync_key Ch3</description>
                <status>0x92</status>
                <midino>0x29</midino>
                <on>0x02</on>
                <off>0xFF</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>sync_key</key>
                <description>sync_key Ch4</description>
                <status>0x93</status>
                <midino>0x29</midino>
                <on>0x02</on>
                <off>0xFF</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>reset_key</key>
                <description>Default Key</description>
                <status>0x90</status>
                <midino>0x29</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>reset_key</key>
                <description>Default Key</description>
                <status>0x91</status>
                <midino>0x29</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>reset_key</key>
                <description>Default Key</description>
                <status>0x92</status>
                <midino>0x29</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>reset_key</key>
                <description>Default Key</description>
                <status>0x93</status>
                <midino>0x29</midino>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x94</status>
                <midino>0x47</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x95</status>
                <midino>0x47</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x96</status>
                <midino>0x47</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>quantize</key>
                <description>Quantize</description>
                <status>0x97</status>
                <midino>0x47</midino>
                <on>0x02</on>
                <off>0x01</off>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
