<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name><PERSON> DJ-505</name>
        <author><PERSON></author>
        <description>4-deck mapping for Roland DJ-505 controller</description>
        <forums>https://mixxx.discourse.group/t/roland-dj-505/17916</forums>
        <manual>roland_dj_505</manual>
    </info>
    <controller id="DJ-505">
        <scriptfiles>
            <file functionprefix="" filename="lodash.mixxx.js"/>
            <file functionprefix="" filename="midi-components-0.0.js"/>
            <file functionprefix="DJ505" filename="Roland_DJ-505-scripts.js"/>
        </scriptfiles>
        <controls>
            <!-- Left side, Deck 1 -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].play.input</key>
                <description>DECK 1 PLAY/PAUSE</description>
                <status>0x90</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].cue.input</key>
                <description>DECK 1 CUE</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].sync.input</key>
                <description>DECK 1 SYNC</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].sync.input</key>
                <description>DECK 1 SYNC OFF</description>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].play.input</key>
                <description>DECK 1 STUTTER</description>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].cue.input</key>
                <description>DECK 1 CUE REWIND</description>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].tempoFader.inputMSB</key>
                <description>DECK 1 TEMPO FADER (MSB)</description>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].tempoFader.inputLSB</key>
                <description>DECK 1 TEMPO FADER (LSB)</description>
                <status>0xB0</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].keylock.input</key>
                <description>DECK 1 KEY LOCK</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].keylock.input</key>
                <description>DECK 1 TEMPO RANGE</description>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 1, Pad-state buttons (Hot-cue mode, loop mode, sampler mode) -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - HOT CUE</description>
                <status>0x94</status>
                <midino>0x00</midino><!-- [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - FLIP</description>
                <status>0x94</status>
                <midino>0x02</midino><!-- [HOT CUE] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - CUE LOOP</description>
                <status>0x94</status>
                <midino>0x03</midino><!-- [SHIFT] + [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - ROLL</description>
                <status>0x94</status>
                <midino>0x08</midino><!-- [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - LOOP</description>
                <status>0x94</status>
                <midino>0x0D</midino><!-- [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - SLICER</description>
                <status>0x94</status>
                <midino>0x09</midino><!-- [SHIFT] + [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - SLICER LOOP</description>
                <status>0x94</status>
                <midino>0x0A</midino><!-- [SHIFT] + [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - TR</description>
                <status>0x94</status>
                <midino>0x04</midino><!-- [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - TR VELOCITY</description>
                <status>0x94</status>
                <midino>0x06</midino><!-- [TR] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - PATTERN</description>
                <status>0x94</status>
                <midino>0x05</midino><!-- [SHIFT] + [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - SAMPLER</description>
                <status>0x94</status>
                <midino>0x0B</midino><!-- [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - PITCH PLAY</description>
                <status>0x94</status>
                <midino>0x0F</midino><!-- [SAMPLER] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padModeButtonPressed</key>
                <description>DECK 1 PADS - VELOCITY</description>
                <status>0x94</status>
                <midino>0x0C</midino><!-- [SHIFT] + [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 1, Performance Pads -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 1</description>
                <status>0x94</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 2</description>
                <status>0x94</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 3</description>
                <status>0x94</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 4</description>
                <status>0x94</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 5</description>
                <status>0x94</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 6</description>
                <status>0x94</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 7</description>
                <status>0x94</status>
                <midino>0x1a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 8</description>
                <status>0x94</status>
                <midino>0x1b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 1, Performance Pads (shifted) -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 1 (shifted)</description>
                <status>0x94</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 2 (shifted)</description>
                <status>0x94</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 3 (shifted)</description>
                <status>0x94</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 4 (shifted)</description>
                <status>0x94</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 5 (shifted)</description>
                <status>0x94</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 6 (shifted)</description>
                <status>0x94</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 7 (shifted)</description>
                <status>0x94</status>
                <status>0x94</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.padPressed</key>
                <description>DECK 1 PAD 8 (shifted)</description>
                <status>0x94</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 1, Loop Section -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopActive.input</key>
                <description>DECK 1 LOOP ACTIVE</description>
                <status>0x94</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].reloopExit.input</key>
                <description>DECK 1 RELOOP/EXIT</description>
                <status>0x94</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopHalve.input</key>
                <description>DECK 1 LOOP 1/2X</description>
                <status>0x94</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopDouble.input</key>
                <description>DECK 1 LOOP 2X</description>
                <status>0x94</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopShiftBackward.input</key>
                <description>DECK 1 LOOP SHIFT 1/2X</description>
                <status>0x94</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopShiftForward.input</key>
                <description>DECK 1 LOOP SHIFT 2X</description>
                <status>0x94</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopIn.input</key>
                <description>DECK 1 LOOP IN</description>
                <status>0x94</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].loopOut.input</key>
                <description>DECK 1 LOOP OUT</description>
                <status>0x94</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].slotSelect.input</key>
                <description>DECK 1 SLOT SELECT</description>
                <status>0x94</status>
                <midino>0x3b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].autoLoop.input</key>
                <description>DECK 1 AUTO LOOP</description>
                <status>0x94</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- jog wheels -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].wheelTouch</key>
                <description>DECK 1 JOG WHEEL TOUCH</description>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].wheelTurn</key>
                <description>DECK 1 JOG WHEEL TURN</description>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].slipModeButton.input</key>
                <description>DECK 1 VINYL MODE</description>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].slipModeButton.input</key>
                <description>DECK 1 SLIP MODE</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- mixer -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].pregain.inputMSB</key>
                <description>DECK 1 TRIM (MSB)</description>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].pregain.inputLSB</key>
                <description>DECK 1 TRIM (LSB)</description>
                <status>0xB0</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[3].inputMSB</key>
                <description>DECK 1 EQ HI (MSB)</description>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[3].inputLSB</key>
                <description>DECK 1 EQ HI (LSB)</description>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[2].inputMSB</key>
                <description>DECK 1 EQ MID (MSB)</description>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[2].inputLSB</key>
                <description>DECK 1 EQ MID (LSB)</description>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[1].inputMSB</key>
                <description>DECK 1 EQ LOW (MSB)</description>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>DJ505.deck[0].eqKnob[1].inputLSB</key>
                <description>DECK 1 EQ LOW (LSB)</description>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>DJ505.deck[0].filter.input</key>
                <description>DECK 1 FILTER</description>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].pfl.input</key>
                <description>DECK 1 CUE/PFL</description>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.paramButtonPressed</key>
                <description>DECK 1 PARAMETER 1 MINUS</description>
                <status>0x94</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.paramButtonPressed</key>
                <description>DECK 1 PARAMETER 1 PLUS</description>
                <status>0x94</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.paramButtonPressed</key>
                <description>DECK 1 PARAMETER 2 MINUS</description>
                <status>0x94</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].padSection.paramButtonPressed</key>
                <description>DECK 1 PARAMETER 2 PLUS</description>
                <status>0x94</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].tapBPM.input</key>
                <description>DECK 1 TAP</description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].volume.inputMSB</key>
                <description>DECK 1 VOLUME (MSB)</description>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck[0].volume.inputLSB</key>
                <description>DECK 1 VOLUME (MSB)</description>
                <status>0xB0</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Left side, Deck 3 -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].play.input</key>
                <description>DECK 3 PLAY/PAUSE</description>
                <status>0x92</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].cue.input</key>
                <description>DECK 3 CUE</description>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].sync.input</key>
                <description>DECK 3 SYNC</description>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].sync.input</key>
                <description>DECK 3 SYNC OFF</description>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].play.input</key>
                <description>DECK 3 STUTTER</description>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].cue.input</key>
                <description>DECK 3 CUE REWIND</description>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].tempoFader.inputMSB</key>
                <description>DECK 3 TEMPO FADER (MSB)</description>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].tempoFader.inputLSB</key>
                <description>DECK 3 TEMPO FADER (LSB)</description>
                <status>0xB2</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].keylock.input</key>
                <description>DECK 3 KEY LOCK</description>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].keylock.input</key>
                <description>DECK 3 TEMPO RANGE</description>
                <status>0x92</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 3, Pad-state buttons (Hot-cue mode, loop mode, sampler mode) -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - HOT CUE</description>
                <status>0x96</status>
                <midino>0x00</midino><!-- [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - FLIP</description>
                <status>0x96</status>
                <midino>0x02</midino><!-- [HOT CUE] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - CUE LOOP</description>
                <status>0x96</status>
                <midino>0x03</midino><!-- [SHIFT] + [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - ROLL</description>
                <status>0x96</status>
                <midino>0x08</midino><!-- [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - LOOP</description>
                <status>0x96</status>
                <midino>0x0D</midino><!-- [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - SLICER</description>
                <status>0x96</status>
                <midino>0x09</midino><!-- [SHIFT] + [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - SLICER LOOP</description>
                <status>0x96</status>
                <midino>0x0A</midino><!-- [SHIFT] + [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - TR</description>
                <status>0x96</status>
                <midino>0x04</midino><!-- [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - TR VELOCITY</description>
                <status>0x96</status>
                <midino>0x06</midino><!-- [TR] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - PATTERN</description>
                <status>0x96</status>
                <midino>0x05</midino><!-- [SHIFT] + [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - SAMPLER</description>
                <status>0x96</status>
                <midino>0x0B</midino><!-- [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - PITCH PLAY</description>
                <status>0x96</status>
                <midino>0x0F</midino><!-- [SAMPLER] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padModeButtonPressed</key>
                <description>DECK 3 PADS - VELOCITY</description>
                <status>0x96</status>
                <midino>0x0C</midino><!-- [SHIFT] + [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 3, Performance Pads -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 1</description>
                <status>0x96</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 2</description>
                <status>0x96</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 3</description>
                <status>0x96</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 4</description>
                <status>0x96</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 5</description>
                <status>0x96</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 6</description>
                <status>0x96</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 7</description>
                <status>0x96</status>
                <midino>0x1a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 8</description>
                <status>0x96</status>
                <midino>0x1b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 3, Performance Pads (shifted) -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 1 (shifted)</description>
                <status>0x96</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 2 (shifted)</description>
                <status>0x96</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 3 (shifted)</description>
                <status>0x96</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 4 (shifted)</description>
                <status>0x96</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 5 (shifted)</description>
                <status>0x96</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 6 (shifted)</description>
                <status>0x96</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 7 (shifted)</description>
                <status>0x96</status>
                <status>0x96</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.padPressed</key>
                <description>DECK 3 PAD 8 (shifted)</description>
                <status>0x96</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 3, Loop Section -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopActive.input</key>
                <description>DECK 3 LOOP ACTIVE</description>
                <status>0x96</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].reloopExit.input</key>
                <description>DECK 3 RELOOP/EXIT</description>
                <status>0x96</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopHalve.input</key>
                <description>DECK 3 LOOP 1/2X</description>
                <status>0x96</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopDouble.input</key>
                <description>DECK 3 LOOP 2X</description>
                <status>0x96</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopShiftBackward.input</key>
                <description>DECK 3 LOOP SHIFT 1/2X</description>
                <status>0x96</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopShiftForward.input</key>
                <description>DECK 3 LOOP SHIFT 2X</description>
                <status>0x96</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopIn.input</key>
                <description>DECK 3 LOOP IN</description>
                <status>0x96</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].loopOut.input</key>
                <description>DECK 3 LOOP OUT</description>
                <status>0x96</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].slotSelect.input</key>
                <description>DECK 3 SLOT SELECT</description>
                <status>0x96</status>
                <midino>0x3b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].autoLoop.input</key>
                <description>DECK 3 AUTO LOOP</description>
                <status>0x96</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- jog wheels -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].wheelTouch</key>
                <description>DECK 3 JOG WHEEL TOUCH</description>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].wheelTurn</key>
                <description>DECK 3 JOG WHEEL TURN</description>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].slipModeButton.input</key>
                <description>DECK 3 VINYL MODE</description>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].slipModeButton.input</key>
                <description>DECK 3 SLIP MODE</description>
                <status>0x92</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- mixer -->
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].pregain.inputMSB</key>
                <description>DECK 3 TRIM (MSB)</description>
                <status>0xB2</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].pregain.inputLSB</key>
                <description>DECK 3 TRIM (LSB)</description>
                <status>0xB2</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[3].inputMSB</key>
                <description>DECK 3 EQ HI (MSB)</description>
                <status>0xB2</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[3].inputLSB</key>
                <description>DECK 3 EQ HI (LSB)</description>
                <status>0xB2</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[2].inputMSB</key>
                <description>DECK 3 EQ MID (MSB)</description>
                <status>0xB2</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[2].inputLSB</key>
                <description>DECK 3 EQ MID (LSB)</description>
                <status>0xB2</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[1].inputMSB</key>
                <description>DECK 3 EQ LOW (MSB)</description>
                <status>0xB2</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>DJ505.deck[2].eqKnob[1].inputLSB</key>
                <description>DECK 3 EQ LOW (LSB)</description>
                <status>0xB2</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>DJ505.deck[2].filter.input</key>
                <description>DECK 3 FILTER</description>
                <status>0xB2</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].pfl.input</key>
                <description>DECK 3 CUE/PFL</description>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.paramButtonPressed</key>
                <description>DECK 3 PARAMETER 1 MINUS</description>
                <status>0x96</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.paramButtonPressed</key>
                <description>DECK 3 PARAMETER 1 PLUS</description>
                <status>0x96</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.paramButtonPressed</key>
                <description>DECK 3 PARAMETER 2 MINUS</description>
                <status>0x96</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].padSection.paramButtonPressed</key>
                <description>DECK 3 PARAMETER 2 PLUS</description>
                <status>0x96</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].tapBPM.input</key>
                <description>DECK 3 TAP</description>
                <status>0x92</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].volume.inputMSB</key>
                <description>DECK 3 VOLUME (MSB)</description>
                <status>0xB2</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.deck[2].volume.inputLSB</key>
                <description>DECK 3 VOLUME (MSB)</description>
                <status>0xB2</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Right side, Deck 2 -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].play.input</key>
                <description>DECK 2 PLAY/PAUSE</description>
                <status>0x91</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].cue.input</key>
                <description>DECK 2 CUE</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].sync.input</key>
                <description>DECK 2 SYNC</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].sync.input</key>
                <description>DECK 2 SYNC OFF</description>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].play.input</key>
                <description>DECK 2 STUTTER</description>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].cue.input</key>
                <description>DECK 2 CUE REWIND</description>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].tempoFader.inputMSB</key>
                <description>DECK 2 TEMPO FADER (MSB)</description>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].tempoFader.inputLSB</key>
                <description>DECK 2 TEMPO FADER (LSB)</description>
                <status>0xB1</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].keylock.input</key>
                <description>DECK 2 KEY LOCK</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].keylock.input</key>
                <description>DECK 2 TEMPO RANGE</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 2, Pad-state buttons (Hot-cue mode, loop mode, sampler mode) -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - HOT CUE</description>
                <status>0x95</status>
                <midino>0x00</midino><!-- [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - FLIP</description>
                <status>0x95</status>
                <midino>0x02</midino><!-- [HOT CUE] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - CUE LOOP</description>
                <status>0x95</status>
                <midino>0x03</midino><!-- [SHIFT] + [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - ROLL</description>
                <status>0x95</status>
                <midino>0x08</midino><!-- [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - LOOP</description>
                <status>0x95</status>
                <midino>0x0D</midino><!-- [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - SLICER</description>
                <status>0x95</status>
                <midino>0x09</midino><!-- [SHIFT] + [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - SLICER LOOP</description>
                <status>0x95</status>
                <midino>0x0A</midino><!-- [SHIFT] + [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - TR</description>
                <status>0x95</status>
                <midino>0x04</midino><!-- [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - TR VELOCITY</description>
                <status>0x95</status>
                <midino>0x06</midino><!-- [TR] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - PATTERN</description>
                <status>0x95</status>
                <midino>0x05</midino><!-- [SHIFT] + [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - SAMPLER</description>
                <status>0x95</status>
                <midino>0x0B</midino><!-- [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - PITCH PLAY</description>
                <status>0x95</status>
                <midino>0x0F</midino><!-- [SAMPLER] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padModeButtonPressed</key>
                <description>DECK 2 PADS - VELOCITY</description>
                <status>0x95</status>
                <midino>0x0C</midino><!-- [SHIFT] + [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 2, Performance Pads -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 1</description>
                <status>0x95</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 2</description>
                <status>0x95</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 3</description>
                <status>0x95</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 4</description>
                <status>0x95</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 5</description>
                <status>0x95</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 6</description>
                <status>0x95</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 7</description>
                <status>0x95</status>
                <midino>0x1a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 8</description>
                <status>0x95</status>
                <midino>0x1b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 2, Performance Pads (shifted) -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 1 (shifted)</description>
                <status>0x95</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 2 (shifted)</description>
                <status>0x95</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 3 (shifted)</description>
                <status>0x95</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 4 (shifted)</description>
                <status>0x95</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 5 (shifted)</description>
                <status>0x95</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 6 (shifted)</description>
                <status>0x95</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 7 (shifted)</description>
                <status>0x95</status>
                <status>0x95</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.padPressed</key>
                <description>DECK 2 PAD 8 (shifted)</description>
                <status>0x95</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 2, Loop Section -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopActive.input</key>
                <description>DECK 2 LOOP ACTIVE</description>
                <status>0x95</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].reloopExit.input</key>
                <description>DECK 2 RELOOP/EXIT</description>
                <status>0x95</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopHalve.input</key>
                <description>DECK 2 LOOP 1/2X</description>
                <status>0x95</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopDouble.input</key>
                <description>DECK 2 LOOP 2X</description>
                <status>0x95</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopShiftBackward.input</key>
                <description>DECK 2 LOOP SHIFT 1/2X</description>
                <status>0x95</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopShiftForward.input</key>
                <description>DECK 2 LOOP SHIFT 2X</description>
                <status>0x95</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopIn.input</key>
                <description>DECK 2 LOOP IN</description>
                <status>0x95</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].loopOut.input</key>
                <description>DECK 2 LOOP OUT</description>
                <status>0x95</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].slotSelect.input</key>
                <description>DECK 2 SLOT SELECT</description>
                <status>0x95</status>
                <midino>0x3b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].autoLoop.input</key>
                <description>DECK 2 AUTO LOOP</description>
                <status>0x95</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- jog wheels -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].wheelTouch</key>
                <description>DECK 2 JOG WHEEL TOUCH</description>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].wheelTurn</key>
                <description>DECK 2 JOG WHEEL TURN</description>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].slipModeButton.input</key>
                <description>DECK 2 VINYL MODE</description>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].slipModeButton.input</key>
                <description>DECK 2 SLIP MODE</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- mixer -->
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].pregain.inputMSB</key>
                <description>DECK 2 TRIM (MSB)</description>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].pregain.inputLSB</key>
                <description>DECK 2 TRIM (LSB)</description>
                <status>0xB1</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[3].inputMSB</key>
                <description>DECK 2 EQ HI (MSB)</description>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[3].inputLSB</key>
                <description>DECK 2 EQ HI (LSB)</description>
                <status>0xB1</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[2].inputMSB</key>
                <description>DECK 2 EQ MID (MSB)</description>
                <status>0xB1</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[2].inputLSB</key>
                <description>DECK 2 EQ MID (LSB)</description>
                <status>0xB1</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[1].inputMSB</key>
                <description>DECK 2 EQ LOW (MSB)</description>
                <status>0xB1</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>DJ505.deck[1].eqKnob[1].inputLSB</key>
                <description>DECK 2 EQ LOW (LSB)</description>
                <status>0xB1</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>DJ505.deck[1].filter.input</key>
                <description>DECK 2 FILTER</description>
                <status>0xB1</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].pfl.input</key>
                <description>DECK 2 CUE/PFL</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.paramButtonPressed</key>
                <description>DECK 2 PARAMETER 1 MINUS</description>
                <status>0x95</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.paramButtonPressed</key>
                <description>DECK 2 PARAMETER 1 PLUS</description>
                <status>0x95</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.paramButtonPressed</key>
                <description>DECK 2 PARAMETER 2 MINUS</description>
                <status>0x95</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].padSection.paramButtonPressed</key>
                <description>DECK 2 PARAMETER 2 PLUS</description>
                <status>0x95</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].tapBPM.input</key>
                <description>DECK 2 TAP</description>
                <status>0x91</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].volume.inputMSB</key>
                <description>DECK 2 VOLUME (MSB)</description>
                <status>0xB1</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.deck[1].volume.inputLSB</key>
                <description>DECK 2 VOLUME (MSB)</description>
                <status>0xB1</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Right side, Deck 4 -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].play.input</key>
                <description>DECK 4 PLAY/PAUSE</description>
                <status>0x93</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].cue.input</key>
                <description>DECK 4 CUE</description>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].sync.input</key>
                <description>DECK 4 SYNC</description>
                <status>0x93</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].sync.input</key>
                <description>DECK 4 SYNC OFF</description>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].play.input</key>
                <description>DECK 4 STUTTER</description>
                <status>0x93</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].cue.input</key>
                <description>DECK 4 CUE REWIND</description>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].tempoFader.inputMSB</key>
                <description>DECK 4 TEMPO FADER (MSB)</description>
                <status>0xB3</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].tempoFader.inputLSB</key>
                <description>DECK 4 TEMPO FADER (LSB)</description>
                <status>0xB3</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].keylock.input</key>
                <description>DECK 4 KEY LOCK</description>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].keylock.input</key>
                <description>DECK 4 TEMPO RANGE</description>
                <status>0x93</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 4, Pad-state buttons (Hot-cue mode, loop mode, sampler mode) -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - HOT CUE</description>
                <status>0x97</status>
                <midino>0x00</midino><!-- [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - FLIP</description>
                <status>0x97</status>
                <midino>0x02</midino><!-- [HOT CUE] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - CUE LOOP</description>
                <status>0x97</status>
                <midino>0x03</midino><!-- [SHIFT] + [HOT CUE] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - ROLL</description>
                <status>0x97</status>
                <midino>0x08</midino><!-- [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - LOOP</description>
                <status>0x97</status>
                <midino>0x0D</midino><!-- [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - SLICER</description>
                <status>0x97</status>
                <midino>0x09</midino><!-- [SHIFT] + [ROLL] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - SLICER LOOP</description>
                <status>0x97</status>
                <midino>0x0A</midino><!-- [SHIFT] + [ROLL] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - TR</description>
                <status>0x97</status>
                <midino>0x04</midino><!-- [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - TR VELOCITY</description>
                <status>0x97</status>
                <midino>0x06</midino><!-- [TR] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - PATTERN</description>
                <status>0x97</status>
                <midino>0x05</midino><!-- [SHIFT] + [TR] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - SAMPLER</description>
                <status>0x97</status>
                <midino>0x0B</midino><!-- [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - PITCH PLAY</description>
                <status>0x97</status>
                <midino>0x0F</midino><!-- [SAMPLER] (press twice) -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padModeButtonPressed</key>
                <description>DECK 4 PADS - VELOCITY</description>
                <status>0x97</status>
                <midino>0x0C</midino><!-- [SHIFT] + [SAMPLER] -->
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 4, Performance Pads -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 1</description>
                <status>0x97</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 2</description>
                <status>0x97</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 3</description>
                <status>0x97</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 4</description>
                <status>0x97</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 5</description>
                <status>0x97</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 6</description>
                <status>0x97</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 7</description>
                <status>0x97</status>
                <midino>0x1a</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 8</description>
                <status>0x97</status>
                <midino>0x1b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 4, Performance Pads (shifted) -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 1 (shifted)</description>
                <status>0x97</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 2 (shifted)</description>
                <status>0x97</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 3 (shifted)</description>
                <status>0x97</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 4 (shifted)</description>
                <status>0x97</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 5 (shifted)</description>
                <status>0x97</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 6 (shifted)</description>
                <status>0x97</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 7 (shifted)</description>
                <status>0x97</status>
                <status>0x97</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.padPressed</key>
                <description>DECK 4 PAD 8 (shifted)</description>
                <status>0x97</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 4, Loop Section -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopActive.input</key>
                <description>DECK 4 LOOP ACTIVE</description>
                <status>0x97</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].reloopExit.input</key>
                <description>DECK 4 RELOOP/EXIT</description>
                <status>0x97</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopHalve.input</key>
                <description>DECK 4 LOOP 1/2X</description>
                <status>0x97</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopDouble.input</key>
                <description>DECK 4 LOOP 2X</description>
                <status>0x97</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopShiftBackward.input</key>
                <description>DECK 4 LOOP SHIFT 1/2X</description>
                <status>0x97</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopShiftForward.input</key>
                <description>DECK 4 LOOP SHIFT 2X</description>
                <status>0x97</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopIn.input</key>
                <description>DECK 4 LOOP IN</description>
                <status>0x97</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].loopOut.input</key>
                <description>DECK 4 LOOP OUT</description>
                <status>0x97</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].slotSelect.input</key>
                <description>DECK 4 SLOT SELECT</description>
                <status>0x97</status>
                <midino>0x3b</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].autoLoop.input</key>
                <description>DECK 4 AUTO LOOP</description>
                <status>0x97</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- jog wheels -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].wheelTouch</key>
                <description>DECK 4 JOG WHEEL TOUCH</description>
                <status>0x93</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].wheelTurn</key>
                <description>DECK 4 JOG WHEEL TURN</description>
                <status>0xB3</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].slipModeButton.input</key>
                <description>DECK 4 VINYL MODE</description>
                <status>0x93</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].slipModeButton.input</key>
                <description>DECK 4 SLIP MODE</description>
                <status>0x93</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- mixer -->
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].pregain.inputMSB</key>
                <description>DECK 4 TRIM (MSB)</description>
                <status>0xB3</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].pregain.inputLSB</key>
                <description>DECK 4 TRIM (LSB)</description>
                <status>0xB3</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[3].inputMSB</key>
                <description>DECK 4 EQ HI (MSB)</description>
                <status>0xB3</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[3].inputLSB</key>
                <description>DECK 4 EQ HI (LSB)</description>
                <status>0xB3</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[2].inputMSB</key>
                <description>DECK 4 EQ MID (MSB)</description>
                <status>0xB3</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[2].inputLSB</key>
                <description>DECK 4 EQ MID (LSB)</description>
                <status>0xB3</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[1].inputMSB</key>
                <description>DECK 4 EQ LOW (MSB)</description>
                <status>0xB3</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>DJ505.deck[3].eqKnob[1].inputLSB</key>
                <description>DECK 4 EQ LOW (LSB)</description>
                <status>0xB3</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>DJ505.deck[3].filter.input</key>
                <description>DECK 4 FILTER</description>
                <status>0xB3</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].pfl.input</key>
                <description>DECK 4 CUE/PFL</description>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.paramButtonPressed</key>
                <description>DECK 4 PARAMETER 1 MINUS</description>
                <status>0x97</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.paramButtonPressed</key>
                <description>DECK 4 PARAMETER 1 PLUS</description>
                <status>0x97</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.paramButtonPressed</key>
                <description>DECK 4 PARAMETER 2 MINUS</description>
                <status>0x97</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].padSection.paramButtonPressed</key>
                <description>DECK 4 PARAMETER 2 PLUS</description>
                <status>0x97</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].tapBPM.input</key>
                <description>DECK 4 TAP</description>
                <status>0x93</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].volume.inputMSB</key>
                <description>DECK 4 VOLUME (MSB)</description>
                <status>0xB3</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck[3].volume.inputLSB</key>
                <description>DECK 4 VOLUME (LSB)</description>
                <status>0xB3</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Deck 3/4 Buttons -->
            <control>
                <group>[Channel1]</group>
                <key>DJ505.deck3Button.input</key>
                <description>DECK 3 ENABLE</description>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.deck4Button.input</key>
                <description>DECK 4 ENABLE</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- FX 1 -->
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[1].input</key>
                <description>FX 1 ON 1</description>
                <status>0x98</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[2].input</key>
                <description>FX 1 ON 2</description>
                <status>0x98</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[3].input</key>
                <description>FX 1 ON 3</description>
                <status>0x98</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].effectFocusButton.input</key>
                <description>FX 1 TAP</description>
                <status>0x98</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[1].input</key>
                <description>FX 1 FX SELECT 1</description>
                <status>0x98</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[2].input</key>
                <description>FX 1 FX SELECT 2</description>
                <status>0x98</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableButtons[3].input</key>
                <description>FX 1 FX SELECT 3</description>
                <status>0x98</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].effectFocusButton.input</key>
                <description>FX 1 FX MODE</description>
                <status>0x98</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[1].input</key>
                <description>FX 1 KNOB 1</description>
                <status>0xB8</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[2].input</key>
                <description>FX 1 KNOB 2</description>
                <status>0xB8</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[3].input</key>
                <description>FX 1 KNOB 3</description>
                <status>0xB8</status>
                <status>0xB8</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[1].input</key>
                <description>FX 1 KNOB 1 (shifted)</description>
                <status>0xB8</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[2].input</key>
                <description>FX 1 KNOB 2 (shifted)</description>
                <status>0xB8</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].knobs[3].input</key>
                <description>FX 1 KNOB 3 (shifted)</description>
                <status>0xB8</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].dryWetKnob.input</key>
                <description>FX 1 BEATS KNOB</description>
                <status>0xB8</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].dryWetKnob.input</key>
                <description>FX 1 BEATS KNOB PRESS</description>
                <status>0x98</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableOnChannelButtons.Channel1.input</key>
                <description>FX 1 CH ASSIGN 1</description>
                <status>0x98</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableOnChannelButtons.Channel2.input</key>
                <description>FX 1 CH ASSIGN 2</description>
                <status>0x98</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableOnChannelButtons.Channel3.input</key>
                <description>FX 1 CH ASSIGN 3</description>
                <status>0x98</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableOnChannelButtons.Channel4.input</key>
                <description>FX 1 CH ASSIGN 4</description>
                <status>0x98</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[0].enableOnTrsButton.input</key>
                <description>FX 2 CH ASSIGN TR/SAMPLER</description>
                <status>0x98</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- FX 2 -->
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[1].input</key>
                <description>FX 2 ON 1</description>
                <status>0x99</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[2].input</key>
                <description>FX 2 ON 2</description>
                <status>0x99</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[3].input</key>
                <description>FX 2 ON 3</description>
                <status>0x99</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].effectFocusButton.input</key>
                <description>FX 2 TAP</description>
                <status>0x99</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[1].input</key>
                <description>FX 2 FX SELECT 1</description>
                <status>0x99</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[2].input</key>
                <description>FX 2 FX SELECT 2</description>
                <status>0x99</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableButtons[3].input</key>
                <description>FX 2 FX SELECT 3</description>
                <status>0x99</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].effectFocusButton.input</key>
                <description>FX 2 FX MODE</description>
                <status>0x99</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[1].input</key>
                <description>FX 2 KNOB 1</description>
                <status>0xB9</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[2].input</key>
                <description>FX 2 KNOB 2</description>
                <status>0xB9</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[3].input</key>
                <description>FX 2 KNOB 3</description>
                <status>0xB9</status>
                <status>0xB9</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[1].input</key>
                <description>FX 2 KNOB 1 (shifted)</description>
                <status>0xB9</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[2].input</key>
                <description>FX 2 KNOB 2 (shifted)</description>
                <status>0xB9</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].knobs[3].input</key>
                <description>FX 2 KNOB 3 (shifted)</description>
                <status>0xB9</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].dryWetKnob.input</key>
                <description>FX 2 BEATS KNOB</description>
                <status>0xB9</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].dryWetKnob.input</key>
                <description>FX 2 BEATS KNOB PRESS</description>
                <status>0x99</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableOnChannelButtons.Channel1.input</key>
                <description>FX 2 CH ASSIGN 1</description>
                <status>0x99</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableOnChannelButtons.Channel2.input</key>
                <description>FX 2 CH ASSIGN 2</description>
                <status>0x99</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableOnChannelButtons.Channel3.input</key>
                <description>FX 2 CH ASSIGN 3</description>
                <status>0x99</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableOnChannelButtons.Channel4.input</key>
                <description>FX 2 CH ASSIGN 4</description>
                <status>0x99</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.effectUnit[1].enableOnTrsButton.input</key>
                <description>FX 2 CH ASSIGN TR/SAMPLER</description>
                <status>0x99</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- General -->
            <control>
                <group>[Master]</group>
                <key>DJ505.shiftButton</key>
                <description>SHIFT</description>
                <status>0x9F</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>DJ505.leftLoadTrackButton.input</key>
                <description>DECK 1 LOAD</description>
                <status>0x9F</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>DJ505.leftLoadTrackButton.input</key>
                <description>DECK 3 LOAD</description>
                <status>0x9F</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>DJ505.rightLoadTrackButton.input</key>
                <description>DECK 2 LOAD</description>
                <status>0x9F</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>DJ505.rightLoadTrackButton.input</key>
                <description>DECK 4 LOAD</description>
                <status>0x9F</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.sortLibrary</key>
                <description>BPM</description>
                <status>0x9F</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.sortLibrary</key>
                <description>KEY</description>
                <status>0x9F</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.browseEncoder.input</key>
                <description>BROWSER KNOB</description>
                <status>0xBF</status>
                <midino>0x00</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.browseEncoder.input</key>
                <description>BROWSER KNOB (shifted)</description>
                <status>0xBF</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.browseEncoder.input</key>
                <description>BROWSER KNOB PRESS</description>
                <status>0x9F</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.backButton.input</key>
                <description>BACK</description>
                <status>0x9F</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.sortLibrary</key>
                <description>SONG</description>
                <status>0x9F</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.addPrepareButton.input</key>
                <description>ADD PREPARE</description>
                <status>0x9F</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>DJ505.sortLibrary</key>
                <description>ARTIST</description>
                <status>0x9F</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.crossfader.inputMSB</key>
                <description>CROSS FADER (MSB)</description>
                <status>0xBF</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.crossfader.inputLSB</key>
                <description>CROSS FADER (LSB)</description>
                <status>0xBF</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!--
            <control>
                <group>[Master]</group>
                <description>MASTER LEVEL</description>
                <status>0xBF</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <description>BOOTH LEVEL</description>
                <status>0xBE</status>
                <midino>0x7F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <description>CUE/MASTER MIXING</description>
                <status>0xBF</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            -->
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.levelKnob.input</key>
                <description>TR/SAMPLER LEVEL</description>
                <status>0xBF</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.cueButton.input</key>
                <description>TR/SAMPLER CUE/PFL</description>
                <status>0x9F</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Front Section -->
            <control>
                <group>[Mixer Profile]</group>
                <key>DJ505.crossfader.setCurve</key>
                <description>CROSS FADER CURVE</description>
                <status>0xBF</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Mixer Profile]</group>
                <key>DJ505.crossfader.setReverse</key>
                <description>CROSS FADER REVERSE ON/OFF</description>
                <status>0x9F</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.setChannelInput</key>
                <description>CH 1 PC/LINE/PHONO</description>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.setChannelInput</key>
                <description>CH 2 PC/LINE/PHONO</description>
                <status>0xB1</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!--
            <control>
                <group>[Master]</group>
                <description>MIC LEVEL</description>
                <status>0xBE</status>
                <midino>0x7F</midino>
            </control>
            -->

            <!-- TR-S Section -->
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.bpmKnobTurned</key>
                <description>TR-S VALUE</description>
                <status>0xEF</status>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.syncButtonPressed</key>
                <description>TR-S SYNC</description>
                <status>0x9F</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.syncButtonPressed</key>
                <description>TR-S SYNC OFF</description>
                <status>0x9F</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.startStopButtonPressed</key>
                <description>TR-S START</description>
                <status>0xFA</status>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>DJ505.sampler.startStopButtonPressed</key>
                <description>TR-S STOP</description>
                <status>0xFC</status>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S1 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S2 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S3 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S4 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler5]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S5 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler6]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S6 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler7]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S7 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler8]</group>
                <key>DJ505.sampler.customSamplePlayback</key>
                <description>SERATO SAMPLER S8 PLAYBACK</description>
                <status>0x9F</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!--
            <control>
                <group>[Master]</group>
                <description>TR-S (change)</description>
                <status>0x9E</status>
                <midino>0x7F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            -->

        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
