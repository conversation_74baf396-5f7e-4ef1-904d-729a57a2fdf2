<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Soundless joyMIDI</name>
        <author><PERSON><PERSON><PERSON></author>
        <description>Soundless Portable MIDI Controller</description>
        <forums>https://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=13166</forums>
        <manual>soundless_studio_joymidi</manual>
    </info>
    <controller id="Soundless">
        <scriptfiles>
            <file functionprefix="joyMIDI" filename="Soundless_joyMIDI_scripts.js"/>
        </scriptfiles>
        <controls>
            <!-- Master -->
            <control> <!-- fader -->
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <soft-takeover/>
                </options>
            </control>
            <control> <!-- fsr -->
                <group>[Master]</group>
                <key>joyMIDI.fsr</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- joystick left -->
                <group>[Master]</group>
                <key>joyMIDI.joystick</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- joystick right -->
                <group>[Master]</group>
                <key>joyMIDI.joystick</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- joystick down -->
                <group>[Master]</group>
                <key>joyMIDI.joystick</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- joystick up -->
                <group>[Master]</group>
                <key>joyMIDI.joystick</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- G-Sensor left -->
                <group>[Master]</group>
                <key>joyMIDI.gsensor</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- G-Sensor right -->
                <group>[Master]</group>
                <key>joyMIDI.gsensor</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- G-Sensor down -->
                <group>[Master]</group>
                <key>joyMIDI.gsensor</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- G-Sensor up -->
                <group>[Master]</group>
                <key>joyMIDI.gsensor</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck A -->
            <control> <!-- wheel left -->
                <group>[Channel1]</group>
                <key>joyMIDI.wheel</key>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck B -->
            <control> <!-- wheel right -->
                <group>[Channel2]</group>
                <key>joyMIDI.wheel</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck A Page 1 -->
            <control> <!-- shift -->
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sync -->
                <group>[Channel1]</group>
                <key>joyMIDI.syncButton</key>
                <status>0x90</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.syncButton</key>
                <status>0x80</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- cue -->
                <group>[Channel1]</group>
                <key>joyMIDI.cueButton</key>
                <status>0x90</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.cueButton</key>
                <status>0x80</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- play -->
                <group>[Channel1]</group>
                <key>joyMIDI.playButton</key>
                <status>0x90</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.playButton</key>
                <status>0x80</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- scratch -->
                <group>[Channel1]</group>
                <key>joyMIDI.scratchButton</key>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.scratchButton</key>
                <status>0x80</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- key -->
                <group>[Channel1]</group>
                <key>joyMIDI.keyButton</key>
                <status>0x90</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.keyButton</key>
                <status>0x80</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- beatgrid -->
                <group>[Channel1]</group>
                <key>joyMIDI.beatgridButton</key>
                <status>0x90</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.beatgridButton</key>
                <status>0x80</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- volume -->
                <group>[Channel1]</group>
                <key>joyMIDI.volumeButton</key>
                <status>0x90</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.volumeButton</key>
                <status>0x80</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck A Page 2 -->
            <control> <!-- shift -->
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterLow -->
                <group>[Channel1]</group>
                <key>joyMIDI.filterLowButton</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.filterLowButton</key>
                <status>0x80</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterMiddle -->
                <group>[Channel1]</group>
                <key>joyMIDI.filterMiddleButton</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.filterMiddleButton</key>
                <status>0x80</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterHigh -->
                <group>[Channel1]</group>
                <key>joyMIDI.filterHighButton</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.filterHighButton</key>
                <status>0x80</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue1 -->
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue2 -->
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue3 -->
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x3E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue4 -->
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck A Page 3 -->
            <control> <!-- shift -->
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- reloop toggle -->
                <group>[Channel1]</group>
                <key>joyMIDI.reloopToggleButton</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.reloopToggleButton</key>
                <status>0x80</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- loop in -->
                <group>[Channel1]</group>
                <key>joyMIDI.loopInButton</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loopInButton</key>
                <status>0x80</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- loop out -->
                <group>[Channel1]</group>
                <key>joyMIDI.loopOutButton</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loopOutButton</key>
                <status>0x80</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/8 loop -->
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p125Button</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p125Button</key>
                <status>0x80</status>
                <midino>0x4C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/4 loop -->
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p25Button </key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p25Button </key>
                <status>0x80</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/2 loop -->
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p5Button</key>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loop0p5Button</key>
                <status>0x80</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1 loop -->
                <group>[Channel1]</group>
                <key>joyMIDI.loop1Button</key>
                <status>0x90</status>
                <midino>0x4F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loop1Button</key>
                <status>0x80</status>
                <midino>0x4F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck A Page 4 -->
            <control> <!-- shift -->
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- load A -->
                <group>[Channel1]</group>
                <key>joyMIDI.loadButton</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.loadButton</key>
                <status>0x80</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sampler 1 -->
                <group>[Sampler1]</group>
                <key>joyMIDI.sampler1Button</key>
                <status>0x90</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler1]</group>
                <key>joyMIDI.sampler1Button</key>
                <status>0x80</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sampler 2 -->
                <group>[Sampler2]</group>
                <key>joyMIDI.sampler2Button</key>
                <status>0x90</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler2]</group>
                <key>joyMIDI.sampler2Button</key>
                <status>0x80</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- FX 1 -->
                <group>[Channel1]</group>
                <key>joyMIDI.fx1Button</key>
                <status>0x90</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.fx1Button</key>
                <status>0x80</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- FX 2 -->
                <group>[Channel1]</group>
                <key>joyMIDI.fx2Button</key>
                <status>0x90</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>joyMIDI.fx2Button</key>
                <status>0x80</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck B Page 1 -->
            <control> <!-- shift -->
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sync -->
                <group>[Channel2]</group>
                <key>joyMIDI.syncButton</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.syncButton</key>
                <status>0x80</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- cue -->
                <group>[Channel2]</group>
                <key>joyMIDI.cueButton</key>
                <status>0x90</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.cueButton</key>
                <status>0x80</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- play -->
                <group>[Channel2]</group>
                <key>joyMIDI.playButton</key>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.playButton</key>
                <status>0x80</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- scratch -->
                <group>[Channel2]</group>
                <key>joyMIDI.scratchButton</key>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.scratchButton</key>
                <status>0x80</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- key -->
                <group>[Channel2]</group>
                <key>joyMIDI.keyButton</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.keyButton</key>
                <status>0x80</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- beatgrid -->
                <group>[Channel2]</group>
                <key>joyMIDI.beatgridButton</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.beatgridButton</key>
                <status>0x80</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- volume -->
                <group>[Channel2]</group>
                <key>joyMIDI.volumeButton</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.volumeButton</key>
                <status>0x80</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck B Page 2 -->
            <control> <!-- shift -->
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterLow -->
                <group>[Channel2]</group>
                <key>joyMIDI.filterLowButton</key>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.filterLowButton</key>
                <status>0x80</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterMiddle -->
                <group>[Channel2]</group>
                <key>joyMIDI.filterMiddleButton</key>
                <status>0x90</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.filterMiddleButton</key>
                <status>0x80</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- filterHigh -->
                <group>[Channel2]</group>
                <key>joyMIDI.filterHighButton</key>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.filterHighButton</key>
                <status>0x80</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue1 -->
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue2 -->
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue3 -->
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- hotcue4 -->
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.hotcueButton</key>
                <status>0x80</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck B Page 3 -->
            <control> <!-- shift -->
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- reloop toggle -->
                <group>[Channel2]</group>
                <key>joyMIDI.reloopToggleButton</key>
                <status>0x90</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.reloopToggleButton</key>
                <status>0x80</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- loop in -->
                <group>[Channel2]</group>
                <key>joyMIDI.loopInButton</key>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loopInButton</key>
                <status>0x80</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- loop out -->
                <group>[Channel2]</group>
                <key>joyMIDI.loopOutButton</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loopOutButton</key>
                <status>0x80</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/8 loop -->
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p125Button</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p125Button</key>
                <status>0x80</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/4 loop -->
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p25Button </key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p25Button </key>
                <status>0x80</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1/2 loop -->
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p5Button</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loop0p5Button</key>
                <status>0x80</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- 1 loop -->
                <group>[Channel2]</group>
                <key>joyMIDI.loop1Button</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loop1Button</key>
                <status>0x80</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

            <!-- Deck B Page 4 -->
            <control> <!-- shift -->
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.shiftButton</key>
                <status>0x80</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- load B -->
                <group>[Channel2]</group>
                <key>joyMIDI.loadButton</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.loadButton</key>
                <status>0x80</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sampler 3 -->
                <group>[Sampler3]</group>
                <key>joyMIDI.sampler3Button</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler3]</group>
                <key>joyMIDI.sampler3Button</key>
                <status>0x80</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- sampler 4 -->
                <group>[Sampler4]</group>
                <key>joyMIDI.sampler4Button</key>
                <status>0x90</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Sampler4]</group>
                <key>joyMIDI.sampler4Button</key>
                <status>0x80</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- FX 1 -->
                <group>[Channel2]</group>
                <key>joyMIDI.fx1Button</key>
                <status>0x90</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.fx1Button</key>
                <status>0x80</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control> <!-- FX 2 -->
                <group>[Channel2]</group>
                <key>joyMIDI.fx2Button</key>
                <status>0x90</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>joyMIDI.fx2Button</key>
                <status>0x80</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
