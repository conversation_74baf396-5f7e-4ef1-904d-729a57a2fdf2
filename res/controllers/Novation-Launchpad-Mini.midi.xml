<?xml version='1.0' encoding='utf-8'?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="2.0.0+">
    <info>
        <name>Novation Launchpad Mini</name>
        <author>ma<PERSON><PERSON><PERSON></author>
        <description>Multi page mapping for the Novation Launchpad Mini</description>
        <forums>https://mixxx.discourse.group/t/novation-launchpad-mini-mapping-official-forum/14299</forums>
        <manual>novation_launchpad_mini</manual>
    </info>
    <controller id="Novation Launchpad Mini">
        <scriptfiles>
            <file filename="Novation-Launchpad-Mini-scripts.js" functionprefix="NLM"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x68</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x69</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6A</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6B</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6C</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6D</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6E</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0xb0</status>
                <midino>0x6F</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x8</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x68</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x78</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x0</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x1</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x2</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x3</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x4</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x5</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x6</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x7</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x25</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x26</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x27</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x57</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x61</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x62</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x63</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x64</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x65</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x66</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x67</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x70</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x71</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x72</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x73</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x74</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x75</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x76</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>NLM.incomingData</key>
                <status>0x90</status>
                <midino>0x77</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
