<?xml version="1.0" encoding="utf-8"?>
	<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.11.0 beta">
		<info>
			<name>Numark DJ2Go</name>
			<author><PERSON><PERSON><PERSON><PERSON>, mod by <PERSON>val</author>
			<description>11-Sep-2012. Made to be close as possible to how DJ2Go works with VDJ.</description>
                        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=2732&amp;start=140&amp;sid=da4226f3f9a11932881d0e609ee7f2cc</forums>
            <manual>numark_dj2go</manual>
		</info>
		<controller id="Numark DJ2Go MIDI 1" port="">
		<scriptfiles>
            		<file filename="Numark-DJ2Go-scripts.js" functionprefix="NumarkDJ2Go"/>
        	</scriptfiles>
        		<controls>
				<!-- Master vol, headphone vol and crossfader -->
				<control>
					<group>[Master]</group>
					<key>volume</key>
					<status>0xB0</status>
                			<midino>0x17</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
				<control>
					<group>[Master]</group>
					<key>headVolume</key>
					<status>0xB0</status>
                			<midino>0x0B</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
				<control>
					<group>[Master]</group>
					<key>crossfader</key>
					<status>0xB0</status>
                			<midino>0x0A</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
				<!-- Track selection controls -->
                                <control>
                                         <group>[Playlist]</group>
                                         <key>NumarkDJ2Go.backBut</key>
                                         <status>0x80</status>
                                         <midino>0x59</midino>
                                         <options>
                                                  <script-binding/>
                                         </options>
                                </control>
                                <control>
                                         <group>[Playlist]</group>
                                         <key>NumarkDJ2Go.enterBut</key>
                                         <status>0x80</status>
                                         <midino>0x5A</midino>
                                         <options>
                                                  <script-binding/>
                                         </options>
                                         </control>
				<control>
					<group>[Playlist]</group>
					<key>NumarkDJ2Go.shiftBck</key>
					<status>0x90</status>
                			<midino>0x59</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Playlist]</group>
					<key>NumarkDJ2Go.shiftEnt</key>
					<status>0x90</status>
                			<midino>0x5A</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Playlist]</group>
					<key>NumarkDJ2Go.selectKnob</key>
					<status>0xB0</status>
                			<midino>0x1A</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
<!-- Channel 1 -->
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.shiftA</key>
					<status>0x90</status>
                			<midino>0x4B</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.load</key>
					<status>0x80</status>
                			<midino>0x4B</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>volume</key>
					<status>0xB0</status>
                			<midino>0x08</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>

<!-- Original pitch bend control
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.pitchBendPlus</key>
					<status>0x90</status>
                			<midino>0x43</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.pitchBendPlus</key>
					<status>0x80</status>
                			<midino>0x43</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.pitchBendMinus</key>
					<status>0x90</status>
                			<midino>0x44</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.pitchBendMinus</key>
					<status>0x80</status>
                			<midino>0x44</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
-->
<!-- DJCoval's Loop control -->
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.loopOut</key>
					<status>0x90</status>
                			<midino>0x43</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.loopIn</key>
					<status>0x90</status>
                			<midino>0x44</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
<!-- End Loop -->

				<control>
					<group>[Channel1]</group>
					<key>rate</key>
					<status>0xB0</status>
                			<midino>0x0D</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.sync</key>
					<status>0x90</status>
                			<midino>0x40</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.pfl</key>
					<status>0x90</status>
                			<midino>0x65</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.cue</key>
					<status>0x90</status>
                			<midino>0x33</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.cue</key>
					<status>0x80</status>
                			<midino>0x33</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.play</key>
					<status>0x90</status>
                			<midino>0x3B</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.play</key>
					<status>0x80</status>
                			<midino>0x3B</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel1]</group>
					<key>NumarkDJ2Go.wheel</key>
					<status>0xB0</status>
                			<midino>0x19</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
<!-- Channel 2 -->
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.shiftB</key>
					<status>0x90</status>
                			<midino>0x34</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.load</key>
					<status>0x80</status>
                			<midino>0x34</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>volume</key>
					<status>0xB0</status>
                			<midino>0x09</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
<!--				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.pitchBendPlus</key>
					<status>0x90</status>
                			<midino>0x45</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.pitchBendPlus</key>
					<status>0x80</status>
                			<midino>0x45</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.pitchBendMinus</key>
					<status>0x90</status>
                			<midino>0x46</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.pitchBendMinus</key>
					<status>0x80</status>
                			<midino>0x46</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
-->
<!-- DJ Coval's Loop control -->
                                <control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.loopOut</key>
					<status>0x90</status>
                			<midino>0x45</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.loopIn</key>
					<status>0x90</status>
                			<midino>0x46</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
<!-- End Loop-->
				<control>
					<group>[Channel2]</group>
					<key>rate</key>
					<status>0xB0</status>
                			<midino>0x0E</midino>
                			<options>
						<soft-takeover/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.sync</key>
					<status>0x90</status>
                			<midino>0x47</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.pfl</key>
					<status>0x90</status>
                			<midino>0x66</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.cue</key>
					<status>0x90</status>
                			<midino>0x3C</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.cue</key>
					<status>0x80</status>
                			<midino>0x3C</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.play</key>
					<status>0x90</status>
                			<midino>0x42</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.play</key>
					<status>0x80</status>
                			<midino>0x42</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
				<control>
					<group>[Channel2]</group>
					<key>NumarkDJ2Go.wheel</key>
					<status>0xB0</status>
                			<midino>0x18</midino>
                			<options>
						<script-binding/>
					</options>
				</control>
			</controls>
			<outputs></outputs>
    		</controller>
	</MixxxMIDIPreset>
