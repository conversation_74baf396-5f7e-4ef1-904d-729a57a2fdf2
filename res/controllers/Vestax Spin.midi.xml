<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.8.0+">
  <info>
    <name>Vestax Spin</name>
    <author><PERSON>, based on the work of <PERSON></author>
    <description><PERSON>'s controller mapping for Vestax Spin + script</description>
    <manual>vestax_spin</manual>
  </info>
  <controller id="Vestax Spin">
    <scriptfiles>
       <file filename="Vestax-Spin-scripts.js" functionprefix="VestaxSpin"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Channel1]</group>
        <key>rate</key>
        <status>0xB0</status>
        <midino>0x0e</midino>
        <options><Invert/></options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_down</key>
        <status>0x90</status>
        <midino>0x22</midino>
        <options/>
      </control>
      <control>
        <!-- sync -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x46</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_temp_up</key>
        <status>0x90</status>
        <midino>0x23</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reloop_exit</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <options/>
      </control>
      <control>
        <!-- loop- -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x21</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- loop+ -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x42</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <status>0xB0</status>
        <midino>0x17</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <status>0xB0</status>
        <midino>0x15</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options/>
      </control>
      <control>
        <!-- filter -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x24</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0c</midino>
        <options/>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x32</midino>
      </control>
      <control>
        <!-- cue -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x35</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- cup -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x33</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- back -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x36</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- RR -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x37</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- FF -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x38</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel touch -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x2e</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel touch (filter) -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x90</status>
        <midino>0x2f</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel jog -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel scratch -->
        <group>[Channel1]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0xB0</status>
        <midino>0x11</midino>
        <options><Script-Binding/></options>
      </control>


      <control>
        <group>[Channel2]</group>
        <key>rate</key>
        <status>0xB1</status>
        <midino>0x0e</midino>
        <options><Invert/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_down</key>
        <status>0x91</status>
        <midino>0x22</midino>
        <options/>
      </control>
      <control>
        <!-- sync -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x46</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_temp_up</key>
        <status>0x91</status>
        <midino>0x23</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reloop_exit</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <options/>
      </control>
      <control>
        <!-- loop- -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x21</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- loop+ -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x42</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <status>0xB1</status>
        <midino>0x17</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <status>0xB1</status>
        <midino>0x16</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <status>0xB1</status>
        <midino>0x15</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pregain</key>
        <status>0xB1</status>
        <midino>0x14</midino>
        <options/>
      </control>
      <control>
        <!-- filter -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x24</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB1</status>
        <midino>0x0c</midino>
        <options/>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x91</status>
        <midino>0x32</midino>
      </control>
      <control>
        <!-- cue -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x35</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- cup -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x33</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- back -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x36</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- RR -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x37</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- FF -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x38</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel touch -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x2e</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel touch (filter) -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x91</status>
        <midino>0x2f</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel jog -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0xB1</status>
        <midino>0x10</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <!-- wheel scratch -->
        <group>[Channel2]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0xB1</status>
        <midino>0x11</midino>
        <options><Script-Binding/></options>
      </control>


      <!-- channel >1: common controls -->
      <control>
        <group>[Playlist]</group>
        <key>VestaxSpin.handleEvent</key>
        <status>0x92</status>
        <midino>0x26</midino>
        <options><Script-Binding/></options>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectPrevTrack</key>
        <status>0x92</status>
        <midino>0x5c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x92</status>
        <midino>0x60</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x92</status>
        <midino>0x61</midino>
      </control>
      <control>
        <group>[Playlist]</group>
        <key>SelectNextTrack</key>
        <status>0x92</status>
        <midino>0x5d</midino>
      </control>

      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB2</status>
        <midino>0x08</midino>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x92</status>
        <midino>0x29</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x92</status>
        <midino>0x28</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoDepth</key>
        <status>0xB2</status>
        <midino>0x51</midino>
      </control>
      <control>
        <group>[Flanger]</group>
        <key>lfoPeriod</key>
        <status>0xB2</status>
        <midino>0x52</midino>
      </control>

    </controls>


    <!-- LEDs below -->
    <outputs>
      <output>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x32</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_enabled</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x29</midino>
        <on>0x7f</on>
        <minimum>0.2</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2a</midino>
        <on>0x7f</on>
        <minimum>0.4</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2b</midino>
        <on>0x7f</on>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2c</midino>
        <on>0x7f</on>
        <minimum>0.8</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>vu_meter</key>
        <status>0x90</status>
        <midino>0x2d</midino>
        <on>0x7f</on>
        <minimum>1.0</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x92</status>
        <midino>0x29</midino>
        <minimum>0.1</minimum>
      </output>

      <!-- channel 2 -->
      <output>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x91</status>
        <midino>0x32</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_enabled</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <on>0x7f</on>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x29</midino>
        <on>0x7f</on>
        <minimum>0.2</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2a</midino>
        <on>0x7f</on>
        <minimum>0.4</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2b</midino>
        <on>0x7f</on>
        <minimum>0.6</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2c</midino>
        <on>0x7f</on>
        <minimum>0.8</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>vu_meter</key>
        <status>0x91</status>
        <midino>0x2d</midino>
        <on>0x7f</on>
        <minimum>1.0</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x92</status>
        <midino>0x28</midino>
        <minimum>0.1</minimum>
      </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
