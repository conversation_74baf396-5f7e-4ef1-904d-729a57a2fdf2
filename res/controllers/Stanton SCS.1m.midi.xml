<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.7.0+">
    <info>
        <name>Stanton SCS.1m</name>
        <author><PERSON></author>
        <description>This is a complete mapping for a single Stanton SCS.1m mixer controller. Requires scripting.</description>
        <manual>stanton_scs1m</manual>
    </info>
    <controller id="Stanton SCS.1m" port="">
        <scriptfiles>
            <file filename="Stanton-SCS1m-scripts.js" functionprefix="StantonSCS1m"/>
        </scriptfiles>

            <controls>
                <control>
                    <status>0x90</status>
                    <midino>0x1</midino>
                    <group>[Channel2]</group>
                    <key>pfl</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1</midino>
                    <group>[Channel2]</group>
                    <key>pfl</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2</midino>
                    <group>[Channel2]</group>
                    <key>back</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2</midino>
                    <group>[Channel2]</group>
                    <key>back</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x3</midino>
                    <group>[Channel2]</group>
                    <key>fwd</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x3</midino>
                    <group>[Channel2]</group>
                    <key>fwd</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0xc</midino>
                    <group>[Channel1]</group>
                    <key>pfl</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0xc</midino>
                    <group>[Channel1]</group>
                    <key>pfl</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0xd</midino>
                    <group>[Channel1]</group>
                    <key>back</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0xd</midino>
                    <group>[Channel1]</group>
                    <key>back</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0xe</midino>
                    <group>[Channel1]</group>
                    <key>fwd</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0xe</midino>
                    <group>[Channel1]</group>
                    <key>fwd</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x29</midino>
                    <group>[Channel1]</group>
                    <key>StantonSCS1m.playButton1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x29</midino>
                    <group>[Channel1]</group>
                    <key>StantonSCS1m.playButton1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2a</midino>
                    <group>[Channel2]</group>
                    <key>StantonSCS1m.playButton2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2a</midino>
                    <group>[Channel2]</group>
                    <key>StantonSCS1m.playButton2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2d</midino>
                    <group>[Channel1]</group>
                    <key>StantonSCS1m.cueButton1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2d</midino>
                    <group>[Channel1]</group>
                    <key>StantonSCS1m.cueButton1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2e</midino>
                    <group>[Channel2]</group>
                    <key>StantonSCS1m.cueButton2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2e</midino>
                    <group>[Channel2]</group>
                    <key>StantonSCS1m.cueButton2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x00</midino>
                    <group>[Master]</group>
                    <key>crossfader</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x4</midino>
                    <group>[Channel1]</group>
                    <key>rate</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x5</midino>
                    <group>[Channel1]</group>
                    <key>volume</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x6</midino>
                    <group>[Channel2]</group>
                    <key>volume</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x7</midino>
                    <group>[Channel2]</group>
                    <key>rate</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0xa</midino>
                    <group>[Channel1]</group>
                    <key>rate_perm_down_small</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0xa</midino>
                    <group>[Channel1]</group>
                    <key>rate_perm_down_small</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x5</midino>
                    <group>[Channel2]</group>
                    <key>rate_perm_down_small</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x5</midino>
                    <group>[Channel2]</group>
                    <key>rate_perm_down_small</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0xb</midino>
                    <group>[Channel1]</group>
                    <key>rate_perm_up_small</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0xb</midino>
                    <group>[Channel1]</group>
                    <key>rate_perm_up_small</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x6</midino>
                    <group>[Channel2]</group>
                    <key>rate_perm_up_small</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x6</midino>
                    <group>[Channel2]</group>
                    <key>rate_perm_up_small</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2C</midino>
                    <group>[Channel1]</group>
                    <key>rate_temp_down</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2C</midino>
                    <group>[Channel1]</group>
                    <key>rate_temp_down</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2F</midino>
                    <group>[Channel2]</group>
                    <key>rate_temp_down</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2F</midino>
                    <group>[Channel2]</group>
                    <key>rate_temp_down</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x28</midino>
                    <group>[Channel1]</group>
                    <key>rate_temp_up</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x28</midino>
                    <group>[Channel1]</group>
                    <key>rate_temp_up</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x2B</midino>
                    <group>[Channel2]</group>
                    <key>rate_temp_up</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x2B</midino>
                    <group>[Channel2]</group>
                    <key>rate_temp_up</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0xd</midino>
                    <group>[Channel1]</group>
                    <key>pregain</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0xe</midino>
                    <group>[Channel1]</group>
                    <key>filterHigh</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0xf</midino>
                    <group>[Channel1]</group>
                    <key>filterMid</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x10</midino>
                    <group>[Channel1]</group>
                    <key>filterLow</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x18</midino>
                    <group>[Channel2]</group>
                    <key>pregain</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x19</midino>
                    <group>[Channel2]</group>
                    <key>filterHigh</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x16</midino>
                    <group>[Channel2]</group>
                    <key>filterMid</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x12</midino>
                    <group>[Channel2]</group>
                    <key>filterLow</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x1d</midino>
                    <group>[Master]</group>
                    <key>headVolume</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x1e</midino>
                    <group>[Master]</group>
                    <key>headMix</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x1f</midino>
                    <group>[Master]</group>
                    <key>volume</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x10</midino>
                    <group>[Channel1]</group>
                    <key>flanger</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x10</midino>
                    <group>[Channel1]</group>
                    <key>flanger</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x13</midino>
                    <group>[Channel2]</group>
                    <key>flanger</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x13</midino>
                    <group>[Channel2]</group>
                    <key>flanger</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x7E</midino>
                    <group>[Channel1]</group>
                    <key>StantonSCS1m.encoderJog1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x7D</midino>
                    <group>[Channel2]</group>
                    <key>StantonSCS1m.encoderJog2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x11</midino>
                    <group>[Channel1]</group>
                    <key>reverse</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x11</midino>
                    <group>[Channel1]</group>
                    <key>reverse</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x12</midino>
                    <group>[Channel2]</group>
                    <key>reverse</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x12</midino>
                    <group>[Channel2]</group>
                    <key>reverse</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x09</midino>
                    <group>[Channel1]</group>
                    <key>beatsync</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x09</midino>
                    <group>[Channel1]</group>
                    <key>beatsync</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x04</midino>
                    <group>[Channel2]</group>
                    <key>beatsync</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x04</midino>
                    <group>[Channel2]</group>
                    <key>beatsync</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x19</midino>
                    <group>[Channel1]</group>
                    <key>bpm_tap</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x19</midino>
                    <group>[Channel1]</group>
                    <key>bpm_tap</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1D</midino>
                    <group>[Channel2]</group>
                    <key>bpm_tap</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1D</midino>
                    <group>[Channel2]</group>
                    <key>bpm_tap</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x1B</midino>
                    <group>[Flanger]</group>
                    <key>lfoDepth</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x17</midino>
                    <group>[Flanger]</group>
                    <key>lfoDelay</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x14</midino>
                    <group>[Flanger]</group>
                    <key>lfoPeriod</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x15</midino>
                    <group>[Master]</group>
                    <key>balance</key>
                </control>
                <control>
                    <status>0xB0</status>
                    <midino>0x70</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.selectKnob</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x18</midino>
                    <group>[Playlist]</group>
                    <key>StantonSCS1m.cancelButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x18</midino>
                    <group>[Playlist]</group>
                    <key>StantonSCS1m.cancelButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1A</midino>
                    <group>[Playlist]</group>
                    <key>StantonSCS1m.enterButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1A</midino>
                    <group>[Playlist]</group>
                    <key>StantonSCS1m.enterButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Select knob press -->
                    <status>0x90</status>
                    <midino>0x27</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.pressSelectKnob</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Select knob release -->
                    <status>0x80</status>
                    <midino>0x27</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.pressSelectKnob</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1F</midino>
                    <group>[Playlist]</group>
                    <key>SelectPrevPlaylist</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1F</midino>
                    <group>[Playlist]</group>
                    <key>SelectPrevPlaylist</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x21</midino>
                    <group>[Playlist]</group>
                    <key>SelectNextPlaylist</key>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x21</midino>
                    <group>[Playlist]</group>
                    <key>SelectNextPlaylist</key>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.browseButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x20</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.browseButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1E</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.controlButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1E</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.controlButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x90</status>
                    <midino>0x1C</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.setupButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>
                    <status>0x80</status>
                    <midino>0x1C</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.setupButton</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Pitch range knob Ch1 -->
                    <status>0xB0</status>
                    <midino>0x7F</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.pitchRangeKnob1</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Pitch range knob Ch2 -->
                    <status>0xB0</status>
                    <midino>0x7C</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.pitchRangeKnob2</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Fader start toggle -->
                    <status>0x90</status>
                    <midino>0x08</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.faderStartToggle</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Hot cue deck toggle -->
                    <status>0x90</status>
                    <midino>0x22</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.hotCueDeckChange</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>   <!-- Hot cue deck toggle -->
                    <status>0x80</status>
                    <midino>0x22</midino>
                    <group>[Master]</group>
                    <key>StantonSCS1m.hotCueDeckChange</key>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 1   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x90</status>
                    <midino>0x23</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 1   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x80</status>
                    <midino>0x23</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 2   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x90</status>
                    <midino>0x24</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 2   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x80</status>
                    <midino>0x24</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 3   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x90</status>
                    <midino>0x25</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 3   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x80</status>
                    <midino>0x25</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 4   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x90</status>
                    <midino>0x26</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
                <control>    <!--    Preset 4   -->
                    <group>[Master]</group>
                    <key>StantonSCS1m.presetButton</key>
                    <status>0x80</status>
                    <midino>0x26</midino>
                    <options>
                        <Script-Binding/>
                    </options>
                </control>
            </controls>
            <outputs>
                <output>    <!--    Play Ch1    -->
                    <group>[Channel1]</group>
                    <key>play</key>
                    <status>0x90</status>
                    <midino>0x29</midino>
                    <on>0x40</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Play Ch2    -->
                    <group>[Channel2]</group>
                    <key>play</key>
                    <status>0x90</status>
                    <midino>0x2A</midino>
                    <on>0x40</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Cue Ch1    -->
                    <group>[Channel1]</group>
                    <key>cue_default</key>
                    <status>0x90</status>
                    <midino>0x2D</midino>
                    <on>0x40</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Cue Ch2    -->
                    <group>[Channel2]</group>
                    <key>cue_default</key>
                    <status>0x90</status>
                    <midino>0x2E</midino>
                    <on>0x40</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Temp pitch up Ch1    -->
                    <group>[Channel1]</group>
                    <key>rate_temp_up</key>
                    <status>0x90</status>
                    <midino>0x28</midino>
                    <on>0x40</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Temp pitch up Ch2    -->
                    <group>[Channel2]</group>
                    <key>rate_temp_up</key>
                    <status>0x90</status>
                    <midino>0x2B</midino>
                    <on>0x40</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Temp pitch down Ch1    -->
                    <group>[Channel1]</group>
                    <key>rate_temp_down</key>
                    <status>0x90</status>
                    <midino>0x2C</midino>
                    <on>0x40</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Temp pitch down Ch2    -->
                    <group>[Channel2]</group>
                    <key>rate_temp_down</key>
                    <status>0x90</status>
                    <midino>0x2F</midino>
                    <on>0x40</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Headphone cue Ch1    -->
                    <group>[Channel1]</group>
                    <key>pfl</key>
                    <status>0x90</status>
                    <midino>0x0C</midino>
                    <on>0x7F</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Headphone cue Ch2    -->
                    <group>[Channel2]</group>
                    <key>pfl</key>
                    <status>0x90</status>
                    <midino>0x01</midino>
                    <on>0x7F</on>
                    <off>0x20</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Sync Ch1    -->
                    <group>[Channel1]</group>
                    <key>beatsync</key>
                    <status>0x90</status>
                    <midino>0x09</midino>
                    <on>0x7F</on>
                    <off>0x40</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Sync Ch2    -->
                    <group>[Channel2]</group>
                    <key>beatsync</key>
                    <status>0x90</status>
                    <midino>0x04</midino>
                    <on>0x7F</on>
                    <off>0x40</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Flanger Ch1    -->
                    <group>[Channel1]</group>
                    <key>flanger</key>
                    <status>0xB0</status>
                    <midino>0x7F</midino>
                    <on>0x15</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
                <output>    <!--    Flanger Ch2    -->
                    <group>[Channel2]</group>
                    <key>flanger</key>
                    <status>0xB0</status>
                    <midino>0x7C</midino>
                    <on>0x27</on>
                    <off>0x00</off>
                    <minimum>0.1</minimum>
                </output>
            </outputs>
        </controller>
    </MixxxMIDIPreset>
