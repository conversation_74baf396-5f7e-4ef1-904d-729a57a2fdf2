<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="1.11-pre +" schemaVersion="1">
    <info>
        <name>MixVibes U-Mix Control Pro 2</name>
        <author>tomtom (thomas-8)</author>
	<description>V1.0.0 -> Please visit Forum and Wiki for more details.</description>
        <forums>https://mixxx.discourse.group/t/v1-0-mixvibes-u-mix-control-pro-2-mapping/13322</forums>
        <manual>mixvibes_u_mix_control_2</manual>
    </info>
    <controller id="MixVibes">
    <scriptfiles>
    <file filename="MixVibes-U-Mix-Control-Pro-2-scripts.js" functionprefix="ControlPro2"/>
    </scriptfiles>
    <controls>
        <!-- JogWheel-Script -->
	   <control>
                <group>[Channel1]</group>
                <key>jog</key>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <spread64/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>jog</key>
                <status>0xB1</status>
                <midino>0x17</midino>
                <options>
                    <spread64/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>ControlPro2.wheelTouch</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
	    </control>
	    <control>
                <group>[Channel2]</group>
                <key>ControlPro2.wheelTouch</key>
                <status>0x91</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
	    </control>
            <control>
                <group>[Channel1]</group>
                <key>ControlPro2.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>ControlPro2.wheelTurn</key>
                <status>0xB1</status>
                <midino>0x16</midino>
                <options>
                    <Script-Binding/>
                </options>
            </control>
        <!-- Main Controls -->
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB2</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
             <control>
                <group>[Master]</group>
                <key>volume</key>
                <status>0xB2</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB2</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <status>0xB1</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB1</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>bpm_tap</key>
                <status>0x93</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>bpm_tap</key>
                <status>0x94</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
	   <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <normal/>
                </options>
            </control>
    	<!-- EQs -->
	    <control>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <status>0xB1</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <status>0xB1</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <status>0xB1</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
	<!-- EQs-KillSwitch -->
	    <control>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <status>0x91</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <status>0x91</status>
                <midino>0x13</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <status>0x91</status>
                <midino>0x14</midino>
                <options>
                    <normal/>
                </options>
            </control>
	<!-- Standard Controls -->
	<control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
	     <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
	     <control>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xE0</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xE1</status>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>rate_temp_up_small</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel2]</group>
                <key>rate_temp_up_small</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>rate_temp_down_small</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down_small</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
	<!-- HotCues/Locators activate -->
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <status>0x90</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <status>0x91</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
    	<!-- HotCues/Locators deactivate-->
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <status>0x93</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <status>0x94</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <status>0x93</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <status>0x94</status>
                <midino>0x0A</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <status>0x94</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <status>0x94</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
    <!-- Library -->
	    <control>
                <group>[Playlist]</group>
                <key>SelectTrackKnob</key>
                <status>0xB2</status>
                <midino>0x04</midino>
                <options>
                    <selectknob/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
	    <control>
                <group>[PreviewDeck1]</group>
                <key>LoadSelectedTrackAndPlay</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
	<!-- End of Controls -->
            </controls>
        <outputs>
  	<!-- LED Lighting -->
            <output>
                <group>[Master]</group>
                <key>peak_indicator</key>
		<status>0x92</status>
                <midino>0x3</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1.0</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
		<key>pfl</key>
		<status>0x90</status>
		<midino>0x10</midino>
		<on>0x7F</on>
		<off>0x00</off>
		<maximum>1.0</maximum>
		<minimum>0.9</minimum>
	    </output>
	    <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1.0</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
		<key>playposition</key>
		<status>0x92</status>
		<midino>0x5</midino>
		<on>0x7F</on>
		<off>0x00</off>
		<maximum>1.0</maximum>
		<minimum>0.9</minimum>
	    </output>
            <output>
                <group>[Channel2]</group>
                <key>playposition</key>
                <status>0x92</status>
                <midino>0x6</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1.0</maximum>
		<minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel1]</group>
                <key>beat_active</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1.0</maximum>
                <minimum>0.9</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beat_active</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1.0</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x03</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
	    <output>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x91</status>
                <midino>0x03</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
	    <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
                <key>rate_temp_up_small</key>
                <status>0x90</status>
                <midino>0x05</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel2]</group>
                <key>rate_temp_up_small</key>
                <status>0x91</status>
                <midino>0x05</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
                <key>rate_temp_down_small</key>
                <status>0x90</status>
                <midino>0x04</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel2]</group>
                <key>rate_temp_down_small</key>
                <status>0x91</status>
                <midino>0x04</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
                <key>jog</key>
                <status>0x90</status>
                <midino>0x04</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel2]</group>
                <key>jog</key>
                <status>0x91</status>
                <midino>0x04</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel1]</group>
                <key>jog</key>
                <status>0x90</status>
                <midino>0x05</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
		<group>[Channel2]</group>
                <key>jog</key>
                <status>0x91</status>
                <midino>0x05</midino>
		<on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel1]</group>
                <key>bpm_tap</key>
                <status>0x93</status>
                <midino>0x03</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
	    <output>
                <group>[Channel2]</group>
                <key>bpm_tap</key>
                <status>0x94</status>
                <midino>0x03</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
            </output>
       	<!-- LED EQ Kills -->
	    <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <status>0x91</status>
                <midino>0x12</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <status>0x91</status>
                <midino>0x13</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <status>0x91</status>
                <midino>0x14</midino>
                <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
       	<!-- LED HotCues -->
	     <output>
		<group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x09</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x91</status>
                <midino>0x09</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x0A</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x91</status>
                <midino>0x0A</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x0B</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x91</status>
                <midino>0x0B</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x0C</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	     <output>
		<group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x91</status>
                <midino>0x0C</midino>
	        <on>0x7F</on>
                <off>0x00</off>
                <maximum>1</maximum>
                <minimum>0.9</minimum>
             </output>
	</outputs>
    </controller>
</MixxxControllerPreset>
