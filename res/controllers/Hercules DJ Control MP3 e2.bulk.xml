<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="1.11+" schemaVersion="1">
    <info>
        <name>Hercules DJ Control MP3 e2 / MP3 LE / Glow</name>
        <author><PERSON><PERSON></author>
        <description>Hercules DJ Control MP3 e2 / MP3 LE / Glow mapping. These three controllers have identical controls and share the same mapping.</description>
        <manual>hercules_djcontrol_mp3_e2</manual>
        <devices>
            <product protocol="bulk" vendor_id="0x06f8" product_id="0x0b105" in_epaddr="0x82" out_epaddr="0x03" />
            <product protocol="bulk" vendor_id="0x06f8" product_id="0x0b120" in_epaddr="0x82" out_epaddr="0x03" />
        </devices>
    </info>
    <controller id="Hercules DJ Control MP3 e2">
        <scriptfiles>
            <file functionprefix="" filename="common-bulk-midi.js"/>
            <file functionprefix="" filename="Hercules DJ Control MP3 e2-scripts.js"/>
            <file functionprefix="MP3e2" filename="Hercules-mp3e2-compat.js"/>
        </scriptfiles>
    </controller>
</MixxxControllerPreset>
