<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
    <info>
        <name>Hercules DJ Console 4-Mx</name>
        <author>josepma</author>
        <description>Hercules DJ Console 4-Mx 2017-12-16</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=3023</forums>
        <manual>hercules_dj_console_4_mx</manual>
    </info>
    <controller id="DJ">
        <scriptfiles>
            <file functionprefix="Hercules4Mx" filename="Hercules-DJ-Console-4-Mx-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group></group>
                <key></key>
                <description>Button Mic On/Off pressed</description>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.LoadSelectedTrack</key>
                <description>Button Load on Right Deck</description>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.LoadSelectedTrack</key>
                <description>Button Load on Right Deck</description>
                <status>0x91</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.LoadSelectedTrack</key>
                <description>Button Load on Left Deck</description>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.LoadSelectedTrack</key>
                <description>Button Load on Left Deck</description>
                <status>0x91</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Hercules4Mx.deckDStateChange</key>
                <description>Button Deck D (State)</description>
                <status>0x90</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Hercules4Mx.deckCStateChange</key>
                <description>Button Deck C (State)</description>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key></key>
                <description>Button Mic On/Off (state)</description>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.forwardButton</key>
                <description>Right Forward</description>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.forwardButton</key>
                <description>Right Forward</description>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.rewindButton</key>
                <description>Right Rewind</description>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.rewindButton</key>
                <description>Right Rewind</description>
                <status>0x91</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.pBendUpButton</key>
                <description>Right P. Bend +</description>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.pBendDownButton</key>
                <description>Right P. Bend -</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.pBendUpButton</key>
                <description>Right P. Bend +</description>
                <status>0x91</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.pBendDownButton</key>
                <description>Right P. Bend -</description>
                <status>0x91</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.pressEffectShift</key>
                <description>Right button shift pressed</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.pressEffectShift</key>
                <description>Right button shift pressed</description>
                <status>0x91</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Hercules4Mx.navigation</key>
                <description>Button Nav Down</description>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Hercules4Mx.navigation</key>
                <description>Button Nav Up</description>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Hercules4Mx.navigationFolders</key>
                <description>Button Nav Folder</description>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Hercules4Mx.navigationFiles</key>
                <description>Button Nav Files</description>
                <status>0x90</status>
                <midino>0x3E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Hercules4Mx.scratchButton</key>
                <description>Button Scratch</description>
                <status>0x90</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[AutoDJ]</group>
                <key>Hercules4Mx.autoDJButton</key>
                <description>Button Auto (DJ)</description>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.stateEffectShift</key>
                <description>Right button shift State</description>
                <status>0x90</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.stateEffectShift</key>
                <description>Right button shift State</description>
                <status>0x91</status>
                <midino>0x3B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.wheelTouch</key>
                <description>Right Jog wheel pressed</description>
                <status>0x90</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.wheelTouch</key>
                <description>Right Jog wheel pressed</description>
                <status>0x91</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Right Kill bass</description>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Right Kill bass</description>
                <status>0x91</status>
                <midino>0x39</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Right Kill mid</description>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Right Kill mid</description>
                <status>0x91</status>
                <midino>0x38</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Right Kill treble</description>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Right Kill treble</description>
                <status>0x91</status>
                <midino>0x37</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>passthrough</key>
                <description>Button Source 2</description>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>passthrough</key>
                <description>Button Source 2</description>
                <status>0x91</status>
                <midino>0x36</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reset_key</key>
                <description>Right Pitch reset (press both pitch scale to send)</description>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>reset_key</key>
                <description>Right Pitch reset (press both pitch scale to send)</description>
                <status>0x91</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.pScaleDownButton</key>
                <description>Right Pitch scale-</description>
                <status>0x90</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.pScaleDownButton</key>
                <description>Right Pitch scale-</description>
                <status>0x91</status>
                <midino>0x34</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.pScaleUpButton</key>
                <description>Right Pitch scale+</description>
                <status>0x90</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.pScaleUpButton</key>
                <description>Right Pitch scale+</description>
                <status>0x91</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key></key>
                <description>Button Deck D (pressed)</description>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.syncButton</key>
                <description>Right Sync button</description>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.syncButton</key>
                <description>Right Sync button</description>
                <status>0x91</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.stopButton</key>
                <description>Right Stop</description>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.stopButton</key>
                <description>Right Stop</description>
                <status>0x91</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.deckheadphones</key>
                <description>Right Cue Select Deck (PFL headphones)</description>
                <status>0x90</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.deckheadphones</key>
                <description>Right Cue Select Deck (PFL headphones)</description>
                <status>0x91</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.playButton</key>
                <description>Right Play</description>
                <status>0x90</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.playButton</key>
                <description>Right Play</description>
                <status>0x91</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.cueButton</key>
                <description>Right Cue</description>
                <status>0x90</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.cueButton</key>
                <description>Right Cue</description>
                <status>0x91</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 12</description>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 12</description>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 11</description>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 11</description>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 10</description>
                <status>0x90</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 10</description>
                <status>0x91</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 9</description>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 9</description>
                <status>0x91</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 8</description>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 8</description>
                <status>0x91</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 7</description>
                <status>0x90</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Hercules4Mx.onSensitivityChange</key>
                <description>Receives information about the sensitivity setting</description>
                <status>0xB0</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 7</description>
                <status>0x91</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 6</description>
                <status>0x90</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 6</description>
                <status>0x91</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 5</description>
                <status>0x90</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 5</description>
                <status>0x91</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 4</description>
                <status>0x90</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 4</description>
                <status>0x91</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 3</description>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 3</description>
                <status>0x91</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 2</description>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 2</description>
                <status>0x91</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 1</description>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Right Effect button 1</description>
                <status>0x91</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.forwardButton</key>
                <description>Left Forward</description>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.forwardButton</key>
                <description>Left Forward</description>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.rewindButton</key>
                <description>Left Rewind</description>
                <status>0x90</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.rewindButton</key>
                <description>Left Rewind</description>
                <status>0x91</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.pBendUpButton</key>
                <description>Left P.Bend +</description>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.pBendUpButton</key>
                <description>Left P.Bend +</description>
                <status>0x91</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.pBendDownButton</key>
                <description>Left P.Bend -</description>
                <status>0x90</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.pBendDownButton</key>
                <description>Left P.Bend -</description>
                <status>0x91</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.pressEffectShift</key>
                <description>Left button shift pressed</description>
                <status>0x90</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.pressEffectShift</key>
                <description>Left button shift pressed</description>
                <status>0x91</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.stateEffectShift</key>
                <description>Left button shift State</description>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.stateEffectShift</key>
                <description>Left button shift State</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.wheelTouch</key>
                <description>Left Jog wheel pressed</description>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.wheelTouch</key>
                <description>Left Jog wheel pressed</description>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Left Kill bass</description>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Left Kill mid</description>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Left Kill bass</description>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Left Kill mid</description>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Left Kill treble</description>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Left Kill treble</description>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>passthrough</key>
                <description>Button Source 1</description>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>passthrough</key>
                <description>Button Source 1</description>
                <status>0x91</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reset_key</key>
                <description>Left Pitch reset (press both pitch scale to send)</description>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>reset_key</key>
                <description>Left Pitch reset (press both pitch scale to send)</description>
                <status>0x91</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.pScaleDownButton</key>
                <description>Left Pitch scale-</description>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.pScaleDownButton</key>
                <description>Left Pitch scale-</description>
                <status>0x91</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.pScaleUpButton</key>
                <description>Left Pitch scale+</description>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.pScaleUpButton</key>
                <description>Left Pitch scale+</description>
                <status>0x91</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group></group>
                <key></key>
                <description>Button Deck C (pressed)</description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.syncButton</key>
                <description>Left Sync button</description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.syncButton</key>
                <description>Left Sync button</description>
                <status>0x91</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.stopButton</key>
                <description>Left Stop</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.stopButton</key>
                <description>Left Stop</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.deckheadphones</key>
                <description>Left Deck Cue Select (PFL headphones)</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.deckheadphones</key>
                <description>Left Deck Cue Select (PFL headphones)</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.playButton</key>
                <description>Left Play</description>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.playButton</key>
                <description>Left Play</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.cueButton</key>
                <description>Left Cue</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.cueButton</key>
                <description>Left Cue</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 12</description>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.deckRateLsb</key>
                <description>Right Pitch (14bits LSB).</description>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 12</description>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 11</description>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.deckRateLsb</key>
                <description>Left Pitch (14bits LSB).</description>
                <status>0xB0</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 11</description>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 10</description>
                <status>0x90</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.deckRateLsb</key>
                <description>Right Pitch (14bits LSB).</description>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 10</description>
                <status>0x91</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 9</description>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.deckRateLsb</key>
                <description>Right Pitch (14bits LSB).</description>
                <status>0xB0</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 9</description>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 8</description>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.scratchWheel</key>
                <description>Right Jog wheel moved with pressure</description>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 8</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 7</description>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.scratchWheel</key>
                <description>Left Jog wheel moved with pressure</description>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 7</description>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 6</description>
                <status>0x90</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.scratchWheel</key>
                <description>Right Jog wheel moved with pressure</description>
                <status>0xB0</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 6</description>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 5</description>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.scratchWheel</key>
                <description>Left Jog wheel moved with pressure</description>
                <status>0xB0</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 5</description>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 4</description>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 4</description>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 3</description>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <description>Heaphone Cue/Mix</description>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 2</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 3</description>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Hercules4Mx.crossfader</key>
                <description>Crossfader</description>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 2</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 1</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>gain</key>
                <description>Vol Main</description>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.FXButton</key>
                <description>Left Effect button 1</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>Hercules4Mx.deckBass</key>
                <description>Right Bass</description>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>Hercules4Mx.deckMids</key>
                <description>Right Mid</description>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>Hercules4Mx.deckTreble</key>
                <description>Right Treble</description>
                <status>0xB0</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.deckRateMsb</key>
                <description>Right Pitch (MSB)</description>
                <status>0xB0</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.deckGain</key>
                <description>Right Gain</description>
                <status>0xB0</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.deckVolume</key>
                <description>Right Volume Fader</description>
                <status>0xB0</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>Hercules4Mx.effectKnob</key>
                <description>Right effect knob</description>
                <status>0xB0</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Hercules4Mx.jogWheel</key>
                <description>Right Jog wheel moved without pressure</description>
                <status>0xB0</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>Hercules4Mx.deckBass</key>
                <description>Left Bass</description>
                <status>0xB0</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>Hercules4Mx.deckMids</key>
                <description>Left Mid</description>
                <status>0xB0</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>Hercules4Mx.deckTreble</key>
                <description>Left Treble</description>
                <status>0xB0</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.deckRateMsb</key>
                <description>Left Pitch (MSB)</description>
                <status>0xB0</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.deckGain</key>
                <description>Left Gain</description>
                <status>0xB0</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.deckVolume</key>
                <description>Left Volume Fader</description>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>Hercules4Mx.effectKnob</key>
                <description>Left effect knob</description>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Hercules4Mx.jogWheel</key>
                <description>Left Jog wheel moved without pressure</description>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Hercules4Mx.deckBass</key>
                <description>Right Bass</description>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Hercules4Mx.deckMids</key>
                <description>Right Mid</description>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Hercules4Mx.deckTreble</key>
                <description>Right Treble</description>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.deckRateMsb</key>
                <description>Right Pitch (MSB)</description>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.deckGain</key>
                <description>Right Gain</description>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.deckVolume</key>
                <description>Right Volume Fader</description>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>Hercules4Mx.effectKnob</key>
                <description>Right effect knob</description>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Hercules4Mx.jogWheel</key>
                <description>Right Jog wheel moved without pressure</description>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Hercules4Mx.deckBass</key>
                <description>Left Bass</description>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Hercules4Mx.deckMids</key>
                <description>Left Mid</description>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Hercules4Mx.deckTreble</key>
                <description>Left Treble</description>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.deckRateMsb</key>
                <description>Left Pitch (MSB)</description>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.deckGain</key>
                <description>Left Gain</description>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.deckVolume</key>
                <description>Left Volume Fader</description>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>Hercules4Mx.effectKnob</key>
                <description>Left effect knob</description>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Hercules4Mx.jogWheel</key>
                <description>Left Jog wheel moved without pressure</description>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <description>Right Play button</description>
                <status>0x90</status>
                <midino>0x2E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>pfl</key>
                <description>Left Deck Cue Select (PFL headphones)</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>passthrough</key>
                <description>Right Source 2 Button</description>
                <status>0x91</status>
                <midino>0x36</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>stop</key>
                <description>Left Stop button</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>play_indicator</key>
                <description>Right Play button</description>
                <status>0x91</status>
                <midino>0x2E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>passthrough</key>
                <description>Left Source 1 Button</description>
                <status>0x90</status>
                <midino>0x16</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>Left Deck Cue Select (PFL headphones)</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>Left pitch reset led</description>
                <status>0x90</status>
                <midino>0x15</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Left Kill Bass</description>
                <status>0x91</status>
                <midino>0x19</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Left Kill Bass</description>
                <status>0x90</status>
                <midino>0x19</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Left Kill Treble</description>
                <status>0x91</status>
                <midino>0x17</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>stop</key>
                <description>Left Stop button</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Left Kill Treble</description>
                <status>0x90</status>
                <midino>0x17</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel3]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Left Kill Mid</description>
                <status>0x91</status>
                <midino>0x18</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Left Kill Mid</description>
                <status>0x90</status>
                <midino>0x18</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>cue_indicator</key>
                <description>Left Cue button</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <description>Right pitch reset led</description>
                <status>0x91</status>
                <midino>0x35</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <description>Left Cue button</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>stop</key>
                <description>Right Stop button</description>
                <status>0x91</status>
                <midino>0x30</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>pfl</key>
                <description>Right Deck Cue Select (PFL headphones)</description>
                <status>0x91</status>
                <midino>0x2F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>passthrough</key>
                <description>Left Source 1 Button</description>
                <status>0x91</status>
                <midino>0x16</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <description>Left Play button</description>
                <status>0x90</status>
                <midino>0x0E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>Right Deck Cue Select (PFL headphones)</description>
                <status>0x90</status>
                <midino>0x2F</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>play_indicator</key>
                <description>Left Play button</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>passthrough</key>
                <description>Right Source 2 Button</description>
                <status>0x90</status>
                <midino>0x36</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>Right pitch reset led</description>
                <status>0x90</status>
                <midino>0x35</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Right Kill Bass</description>
                <status>0x91</status>
                <midino>0x39</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>stop</key>
                <description>Right Stop button</description>
                <status>0x90</status>
                <midino>0x30</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter1</key>
                <description>Right Kill Bass</description>
                <status>0x90</status>
                <midino>0x39</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>cue_indicator</key>
                <description>Right Cue button</description>
                <status>0x91</status>
                <midino>0x2D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <description>Left pitch reset led</description>
                <status>0x91</status>
                <midino>0x15</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Right Kill Treble</description>
                <status>0x91</status>
                <midino>0x37</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <description>Right Cue button</description>
                <status>0x90</status>
                <midino>0x2D</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel4]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Right Kill Mid</description>
                <status>0x91</status>
                <midino>0x38</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter3</key>
                <description>Right Kill Treble</description>
                <status>0x90</status>
                <midino>0x37</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter2</key>
                <description>Right Kill Mid</description>
                <status>0x90</status>
                <midino>0x38</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
