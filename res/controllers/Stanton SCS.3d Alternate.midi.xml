<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
  <info>
    <name>Stanton SCS.3d (alternate)</name>
    <author>sbalmer</author>
    <description>Alternate mapping for the Stanton SCS.3d controller with somewhat different controls and additional lights.</description>
    <manual>stanton_scs3d</manual>
  </info>
  <controller id="SCS.3d">
    <scriptfiles>
      <file functionprefix="SCS3D" filename="Stanton-SCS3d-alternate-scripts.js"/>
    </scriptfiles>
    <controls>
      <!-- central slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x01</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x01</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x01</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x02</midino><options><script-binding/></options></control>

      <!-- pitch slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x03</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x03</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x03</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x04</midino><options><script-binding/></options></control>

      <!-- gain slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x07</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x07</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x07</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x08</midino><options><script-binding/></options></control>

      <!-- left slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x0C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x0C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x0C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x0D</midino><options><script-binding/></options></control>

      <!-- right slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x0E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x0E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x0E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x0F</midino><options><script-binding/></options></control>

      <!-- mode buttons -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x20</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x20</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x22</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x22</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x24</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x24</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x26</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x26</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x28</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x28</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x2A</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x2A</midino><options><script-binding/></options></control>

      <!-- B11 to B14 -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x2C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x2C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x2E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x2E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x30</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x30</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x32</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x32</midino><options><script-binding/></options></control>

      <!-- buttons in the circle -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x48</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x48</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x4A</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x4A</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x4C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x4C</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x4E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x4E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x4F</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x4F</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x51</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x51</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x53</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x53</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x55</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x55</midino><options><script-binding/></options></control>

      <!-- circle slider -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x62</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x62</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x62</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0xB0</status><midino>0x63</midino><options><script-binding/></options></control>

      <!-- buttons bottom -->
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x6D</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x6D</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x6E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x6E</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x6F</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x6F</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x80</status><midino>0x70</midino><options><script-binding/></options></control>
      <control><key>SCS3D.receive</key><status>0x90</status><midino>0x70</midino><options><script-binding/></options></control>
    </controls>
    <outputs/>
  </controller>
</MixxxControllerPreset>
