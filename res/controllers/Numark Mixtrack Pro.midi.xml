<!--Numark MixTrack Mixxx Mapping v0.1 1/11/2010 <PERSON> mat<PERSON>@magm3.com
Modify by <PERSON><PERSON> 06/27/2012-->
<MixxxMIDIPreset mixxxVersion="1.11.0+" schemaVersion="1">
    <info>
      <name>Numark MixTrack Pro</name>
      <author><PERSON> (<EMAIL>), <PERSON>, and <PERSON><PERSON> <PERSON><PERSON> (<EMAIL>)</author>
      <description>version v1.2 w/brake, backspin, blink beat Leds.</description>
      <forums>https://mixxx.discourse.group/t/numark-mixtrack-pro-with-backspin-and-more/12557</forums>
      <manual>numark_mixtrack_pro</manual>
    </info>
    <controller id="Numark Mix Track Pro MIDI 1">
        <scriptfiles>
            <file functionprefix="NumarkMixTrackPro" filename="Numark-Mixtrack-Pro-scripts.js"/>
        </scriptfiles>

<!-- GENERAL -->

        <controls>
        	<control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Master]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Playlist]</group>
                <key>NumarkMixTrackPro.selectKnob</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x69</midino>
                <group>[Playlist]</group>
                <key>NumarkMixTrackPro.toggleDirectoryMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <options>
                    <normal/>
                </options>
            </control>

<!-- CHANNEL 1 -->

           <control>
                <status>0xb0</status>
                <midino>0x1b</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1c</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1d</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.playbutton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.cuebutton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.beatsync</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.playFromCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x65</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5C</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5B</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5A</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x59</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.toggleDeleteKey</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x61</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x54</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.loopOut</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x55</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.LoadTrack</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.pitch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x44</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>


            <control>
                <status>0x90</status>
                <midino>0x48</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4e</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x51</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x63</midino>
                <group>[Channel1]</group>
                <key>NumarkMixTrackPro.flanger</key>
                <options>
                    <script-binding/>
                </options>
            </control>


<!-- CHANNEL 2 -->
           <control>
                <status>0xb0</status>
                <midino>0x1e</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1f</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x20</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x42</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.playbutton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.cuebutton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.beatsync</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x4C</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.playFromCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x66</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x60</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5f</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5e</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.changeHotCue</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5d</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.toggleDeleteKey</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x62</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.toggleManualLooping</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x56</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.loopIn</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x57</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.loopOut</key>
                <options>
                     <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x58</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.reLoop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
	    <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.LoadTrack</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.pitch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x46</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <normal/>
                </options>
            </control>


            <control>
                <status>0x90</status>
                <midino>0x50</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.toggleScratchMode</key>
                <options>
                    <script-binding/>
                </options>
            </control>
           <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>-
            <control>
                <status>0x90</status>
                <midino>0x4D</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.wheelTouch</key>
                <options>
                    <script-binding/>
                </options>
            </control>
	    <control>
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x64</midino>
                <group>[Channel2]</group>
                <key>NumarkMixTrackPro.flanger</key>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>


<!-- OUTPUTS Channel1 -->

        <outputs>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3b</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x33</midino>
                <on>0x64</on>
                <off>0x00</off>
            </output>

            <output>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel1]</group>
                <key>beatsync_tempo</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x65</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x51</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x63</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x70</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>

<!-- OUTPUTS Channel2  -->

            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x42</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x3c</midino>
                <on>0x64</on>
                <off>0x00</off>
            </output>

            <output>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel2]</group>
                <key>beatsync_tempo</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x47</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>

            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x66</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x52</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x64</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
	    <output>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
                <minimum>-0.1</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x71</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
