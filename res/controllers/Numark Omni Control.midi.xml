<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
    <info>
        <name>Numark Omni Control</name>
        <author><PERSON></author>
        <description>Adaptation from the work of <PERSON><PERSON><PERSON><PERSON> and <PERSON></description>
        <manual>numark_omni_control</manual>
    </info>
    <controller id="Numark OMNI CONTROL MIDI">
        <scriptfiles>
            <file functionprefix="NumarkTotalControl" filename="Numark-Omni-Control-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x38</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x39</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3a</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.tap</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3b</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.setCue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.playFromCue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.tap</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3f</midino>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x40</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x41</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.leftFunction</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x42</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.rightFunction</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x43</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x44</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.setCue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x45</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.leftFunction</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x46</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.rightFunction</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x47</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x48</midino>
                <group>[Playlist]</group>
                <key>NumarkTotalControl.toggleDirectoryMode</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x49</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.loopIn</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4a</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.loopOut</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4b</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4c</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4d</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.loopIn</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4e</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.loopOut</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x50</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x51</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x52</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x53</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x54</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x55</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.finePitch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <description></description>
                <options>
                    <diff/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.finePitch</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xa</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <description></description>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xf</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Master]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.jogWheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.jogWheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Playlist]</group>
                <key>NumarkTotalControl.selectKnob</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x30</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x31</midino>
                <group>[Master]</group>
                <key>NumarkTotalControl.toggleSimpleCue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x32</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.toggleScratchMode</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>NumarkTotalControl.playFromCue</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x35</midino>
                <group>[Master]</group>
                <key>NumarkTotalControl.toggleExtendedLooping</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x36</midino>
                <group>[Channel2]</group>
                <key>NumarkTotalControl.toggleQuantize</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x37</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x55</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>playposition</key>
                <description></description>
                <minimum>0.75</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4f</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beat_active</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x37</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x35</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_point</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x41</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x32</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x52</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x36</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x46</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>rate</key>
                <description></description>
                <minimum>-0.05</minimum>
                <maximum>0.05</maximum>
                <status>0x90</status>
                <midino>0x43</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x53</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4e</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x54</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_point</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4c</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>rate</key>
                <description></description>
                <minimum>-0.05</minimum>
                <maximum>0.05</maximum>
                <status>0x90</status>
                <midino>0x34</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x50</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3e</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x51</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beat_active</key>
                <description></description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x42</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <minimum>0</minimum>
                <maximum>0.1</maximum>
                <status>0x90</status>
                <midino>0x40</midino>
                <on>0x0</on>
                <off>0x64</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>playposition</key>
                <description></description>
                <minimum>0.75</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3f</midino>
                <on>0x64</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
