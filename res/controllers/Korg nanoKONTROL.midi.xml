<MixxxMIDIPreset mixxxVersion="1.8.2+" schemaVersion="1">
    <info>
      <name>Korg nanoKONTROL</name>
      <author>ePoxi</author>
      <description>You must load the scene file first. Download it from the forums.</description>
      <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=1999</forums>
      <manual>korg_nanokontrol</manual>
    </info>
    <controller id="nanoKONTROL MIDI 1">
        <scriptfiles/>
        <controls>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Master]</group>
                <key>headVolume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Master]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x14</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x16</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x18</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x19</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1a</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1c</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1d</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1e</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1f</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x21</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x22</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x23</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x24</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <switch/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x26</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x27</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x28</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x29</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <switch/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
