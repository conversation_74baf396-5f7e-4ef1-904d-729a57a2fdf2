<MixxxMIDIPreset mixxxVersion="1.11.0+" schemaVersion="1">
  <info>
    <name><PERSON> Instruments QuNeo</name>
    <author><PERSON></author>
    <description>This is a fully customized DJ mapping for the QuNeo, complete with beat-jumping and pulsing LED sequencers.</description>
    <forums>https://mixxx.discourse.group/viewtopic.php?f=7&amp;amp;t=4130&amp;amp;sid=bc90140a4dc6e650e06dae0df8562604</forums>-->
    <manual>keith_mc<PERSON>en_quneo</manual>
  </info>
  <controller id="QUNEO">
    <scriptfiles>
      <file functionprefix="KANE_QuNeo" filename="KANE_QuNeo_scripts.js"/>
    </scriptfiles>
    <controls>

      <!-- Hotcues - deleting only available in cuing mode -->
      <control>
    <status>0x90</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue1Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue2Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x02</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue3Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x03</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue4Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x04</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue1Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x05</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue2Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x06</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue3Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x07</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue4Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue1Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue2Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x02</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue3Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x03</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue4Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x04</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue1Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x05</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue2Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x06</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue3Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x07</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue4Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue5Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x09</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue6Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue7Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue8Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue5Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue6Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue7Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x0f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue8Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue5Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x09</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue6Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue7Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue8Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue5Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue6Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue7Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x0f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue8Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x10</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue9Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x11</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue10Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x12</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue11Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x13</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue12Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x14</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue9Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x15</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue10Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x16</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue11Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x17</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue12Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x10</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue9Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x11</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue10Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x12</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue11Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x13</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue12Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x14</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue9Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x15</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue10Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x16</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue11Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x17</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue12Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x18</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue13Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x19</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue14Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue15Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue16Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue13Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue14Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue15Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x1f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue16Deactivate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x18</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue13Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x19</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue14Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue15Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue16Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue13Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue14Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue15Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x1f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue16Activate</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x20</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue1Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x21</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue2Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x22</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue3Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x23</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue4Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x24</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue1Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x25</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue2Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x26</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue3Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x27</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue4Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x20</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue1Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x21</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue2Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x22</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue3Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x23</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue4Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x24</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue1Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x25</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue2Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x26</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue3Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x27</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue4Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x28</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue5Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x29</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue6Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue7Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue8Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue5Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue6Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue7Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x2f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue8Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x28</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue5Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x29</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue6Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue7Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue8Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue5Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue6Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue7Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x2f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue8Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x30</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue9Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x31</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue10Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x32</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue11Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x33</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue12Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x34</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue9Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x35</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue10Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x36</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue11Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x37</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue12Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x30</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue9Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x31</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue10Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x32</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue11Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x33</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue12Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x34</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue9Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x35</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue10Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x36</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue11Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x37</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue12Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x38</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue13Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x39</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue14Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue15Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue16Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue13Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue14Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue15Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x90</status>
    <midino>0x3f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue16Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x38</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue13Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x39</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue14Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue15Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3b</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1Cue16Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue13Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue14Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue15Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x80</status>
    <midino>0x3f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2Cue16Clear</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Channel 1 Looping -->
      <control>
    <status>0x91</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x02</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x03</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x02</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x03</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x04</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.jump1Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x05</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.jump1Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x06</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1reloop</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x07</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.toggle1JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1loop_double</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>loop_double</key>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x09</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1loop_halve</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x09</midino>
    <group>[Channel1]</group>
    <key>loop_halve</key>
      </control>

      <!-- Channel 2 Looping -->
      <control>
    <status>0x91</status>
    <midino>0x18</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x19</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1a</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1b</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x18</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x19</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x1a</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x1b</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1c</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.jump2Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1d</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.jump2Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1e</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.toggle2JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x1f</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2reloop</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x14</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2loop_double</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x14</midino>
    <group>[Channel2]</group>
    <key>loop_double</key>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x15</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2loop_halve</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x15</midino>
    <group>[Channel2]</group>
    <key>loop_halve</key>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x0a</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.toggle1Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x16</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.toggle2Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sync, Cue -->
      <control>
    <status>0x91</status>
    <midino>0x0b</midino>
    <group>[Channel1]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x0b</midino>
    <group>[Channel1]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x0f</midino>
    <group>[Channel1]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x0f</midino>
    <group>[Channel1]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x17</midino>
    <group>[Channel2]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x17</midino>
    <group>[Channel2]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x13</midino>
    <group>[Channel2]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x81</status>
    <midino>0x13</midino>
    <group>[Channel2]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>

      <!-- Kill High, Lows, Mids -->
      <control>
    <status>0x91</status>
    <midino>0x0c</midino>
    <group>[Channel1]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x0d</midino>
    <group>[Channel1]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x0e</midino>
    <group>[Channel1]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x10</midino>
    <group>[Channel2]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x11</midino>
    <group>[Channel2]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x91</status>
    <midino>0x12</midino>
    <group>[Channel2]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>

      <!-- Horizontal sliders -->
      <control>
    <status>0xb5</status>
    <midino>0x00</midino>
    <group>[Master]</group>
    <key>volume</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0xb5</status>
    <midino>0x01</midino>
    <group>[Master]</group>
    <key>headVolume</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0xb5</status>
    <midino>0x02</midino>
    <group>[Flanger]</group>
    <key>lfoPeriod</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0xb5</status>
    <midino>0x03</midino>
    <group>[Flanger]</group>
    <key>lfoDepth</key>
    <options>
      <normal/>
    </options>
      </control>

      <!-- Horizontal Arrow Buttons -->
      <control>
    <status>0x96</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1keylock</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x01</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2keylock</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x02</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1pfl</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x03</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2pfl</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x04</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1slip_enabled</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x05</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2slip_enabled</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x06</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.deck1flanger</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x07</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.deck2flanger</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.quantize1Cues</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x86</status>
    <midino>0x08</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.quantize1CuesOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x09</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.quantize2Cues</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x86</status>
    <midino>0x09</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.quantize2CuesOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x0a</midino>
    <group>[Channel1]</group>
    <key>beats_translate_curpos</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x96</status>
    <midino>0x0b</midino>
    <group>[Channel2]</group>
    <key>beats_translate_curpos</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x86</status>
    <midino>0x0a</midino>
    <group>[Channel1]</group>
    <key>beats_translate_curpos</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x86</status>
    <midino>0x0b</midino>
    <group>[Channel2]</group>
    <key>beats_translate_curpos</key>
    <options>
      <normal/>
    </options>
      </control>

      <!-- Crossfader and up/down buttons -->
      <control>
    <status>0xB3</status>
    <midino>0x00</midino>
    <group>[Master]</group>
    <key>crossfader</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rateNudge1Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rateNudge1Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x02</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rateNudge2Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x03</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rateNudge2Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rateNudge1ForwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x01</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rateNudge1BackwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x02</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rateNudge2ForwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x03</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rateNudge2BackwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x04</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.visualNudge1Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x05</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.visualNudge1Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x06</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.visualNudge2Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x93</status>
    <midino>0x07</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.visualNudge2Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x04</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.visualNudge1ForwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x05</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.visualNudge1BackwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x06</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.visualNudge2ForwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x83</status>
    <midino>0x07</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.visualNudge2BackwardOff</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Vertical Sliders -->
      <control>
    <status>0x94</status>
    <midino>0x00</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.sliderCycle</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb4</status>
    <midino>0x00</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider1Move</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x84</status>
    <midino>0x01</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider1Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb4</status>
    <midino>0x01</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider2Move</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x84</status>
    <midino>0x02</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider2Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb4</status>
    <midino>0x02</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider3Move</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x84</status>
    <midino>0x03</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider3Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb4</status>
    <midino>0x03</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider4Move</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x84</status>
    <midino>0x04</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.verticalSlider4Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Rotaries - Scripts -->
      <control>
    <status>0x92</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rotary1Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.rotary1Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x92</status>
    <midino>0x01</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rotary2Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x01</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.rotary2Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x92</status>
    <midino>0x02</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.rotary3Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x02</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.rotary3Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x92</status>
    <midino>0x03</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.rotary4Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x03</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.rotary4Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x92</status>
    <midino>0x04</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.rotary5Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x04</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.rotary5Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x92</status>
    <midino>0x05</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.rotary6Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x82</status>
    <midino>0x05</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.rotary6Touch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb2</status>
    <midino>0x00</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.wheel1Turn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb2</status>
    <midino>0x01</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.wheel2Turn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Playlist Editing -->
      <control>
    <status>0x97</status>
    <midino>0x13</midino>
    <group>[Playlist]</group>
    <key>KANE_QuNeo.touchPlaylist</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x14</midino>
    <group>[Playlist]</group>
    <key>KANE_QuNeo.touchTracklist</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb7</status>
    <midino>0x00</midino>
    <group>[Playlist]</group>
    <key>KANE_QuNeo.scrollPlaylist</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0xb7</status>
    <midino>0x01</midino>
    <group>[Playlist]</group>
    <key>KANE_QuNeo.scrollTracklist</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x03</midino>
    <group>[Channel1]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x04</midino>
    <group>[Channel2]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x05</midino>
    <group>[Sampler1]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x06</midino>
    <group>[Sampler2]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x07</midino>
    <group>[Sampler3]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x08</midino>
    <group>[Sampler4]</group>
    <key>LoadSelectedTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x09</midino>
    <group>[Playlist]</group>
    <key>SelectPrevPlaylist</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0a</midino>
    <group>[Playlist]</group>
    <key>SelectNextPlaylist</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0b</midino>
    <group>[Playlist]</group>
    <key>SelectPrevTrack</key>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0c</midino>
    <group>[Playlist]</group>
    <key>SelectNextTrack</key>
      </control>

      <!-- Transport Buttons -->
      <control>
    <status>0x97</status>
    <midino>0x00</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.toggleRecord</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x01</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assert13LEDs</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x87</status>
    <midino>0x01</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assertLEDOn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x02</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.togglePlayScratch</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0d</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assert5LEDs</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0e</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assert14LEDs</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x0f</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assert15LEDs</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x10</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assert16LEDs</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x87</status>
    <midino>0x0d</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assertLEDOn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x87</status>
    <midino>0x0e</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assertLEDOn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x87</status>
    <midino>0x0f</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assertLEDOn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x87</status>
    <midino>0x10</midino>
    <group>[Master]</group>
    <key>KANE_QuNeo.assertLEDOn</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Visualizer -->
      <control>
    <status>0x97</status>
    <midino>0x11</midino>
    <group>[Channel1]</group>
    <key>KANE_QuNeo.reset1Beat</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x97</status>
    <midino>0x12</midino>
    <group>[Channel2]</group>
    <key>KANE_QuNeo.reset2Beat</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sampler 1 Looping -->
      <control>
    <status>0x99</status>
    <midino>0x00</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x01</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x02</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x03</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x00</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x01</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x02</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x03</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.deck3JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x04</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.jump3Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x05</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.jump3Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x06</midino>
    <group>[Sampler1]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x06</midino>
    <group>[Sampler1]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x07</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.toggle3JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x08</midino>
    <group>[Sampler1]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x08</midino>
    <group>[Sampler1]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x09</midino>
    <group>[Sampler1]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x09</midino>
    <group>[Sampler1]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x0a</midino>
    <group>[Sampler1]</group>
    <key>KANE_QuNeo.toggle3Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sampler 2 Looping -->
      <control>
    <status>0x99</status>
    <midino>0x18</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x19</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1a</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1b</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x18</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x19</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x1a</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x1b</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.deck4JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1c</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.jump4Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1d</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.jump4Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1e</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.toggle4JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x1f</midino>
    <group>[Sampler2]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x1f</midino>
    <group>[Sampler2]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x14</midino>
    <group>[Sampler2]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x14</midino>
    <group>[Sampler2]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x15</midino>
    <group>[Sampler2]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x15</midino>
    <group>[Sampler2]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x16</midino>
    <group>[Sampler2]</group>
    <key>KANE_QuNeo.toggle4Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sampler 3 Looping -->
      <control>
    <status>0x9b</status>
    <midino>0x00</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x01</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x02</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x03</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x00</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x01</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x02</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x03</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.deck5JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x04</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.jump5Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x05</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.jump5Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x06</midino>
    <group>[Sampler3]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x06</midino>
    <group>[Sampler3]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x07</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.toggle5JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x08</midino>
    <group>[Sampler3]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x08</midino>
    <group>[Sampler3]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x09</midino>
    <group>[Sampler3]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x09</midino>
    <group>[Sampler3]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0a</midino>
    <group>[Sampler3]</group>
    <key>KANE_QuNeo.toggle5Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sampler 4 Looping -->
      <control>
    <status>0x9b</status>
    <midino>0x18</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpLoop1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x19</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpLoop2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1a</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpLoop4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1b</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpLoop8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x18</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpOff1</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x19</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpOff2</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x1a</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpOff4</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x1b</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.deck6JumpOff8</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1c</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.jump6Backward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1d</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.jump6Forward</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1e</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.toggle6JumpSync</key>
    <options>
      <Script-Binding/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x1f</midino>
    <group>[Sampler4]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x1f</midino>
    <group>[Sampler4]</group>
    <key>reloop_exit</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x14</midino>
    <group>[Sampler4]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x14</midino>
    <group>[Sampler4]</group>
    <key>loop_double</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x15</midino>
    <group>[Sampler4]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x15</midino>
    <group>[Sampler4]</group>
    <key>loop_halve</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x16</midino>
    <group>[Sampler4]</group>
    <key>KANE_QuNeo.toggle6Looping</key>
    <options>
      <Script-Binding/>
    </options>
      </control>

      <!-- Sampler Syncs, Cues -->
      <control>
    <status>0x99</status>
    <midino>0x0b</midino>
    <group>[Sampler1]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x0b</midino>
    <group>[Sampler1]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x0f</midino>
    <group>[Sampler1]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x0f</midino>
    <group>[Sampler1]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x17</midino>
    <group>[Sampler2]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x17</midino>
    <group>[Sampler2]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x13</midino>
    <group>[Sampler2]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x89</status>
    <midino>0x13</midino>
    <group>[Sampler2]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0b</midino>
    <group>[Sampler3]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x0b</midino>
    <group>[Sampler3]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0f</midino>
    <group>[Sampler3]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x0f</midino>
    <group>[Sampler3]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x17</midino>
    <group>[Sampler4]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x17</midino>
    <group>[Sampler4]</group>
    <key>beatsync</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x13</midino>
    <group>[Sampler4]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x8b</status>
    <midino>0x13</midino>
    <group>[Sampler4]</group>
    <key>cue_default</key>
    <options>
      <normal/>
    </options>
      </control>

      <!-- Sampler Kill Highs, Lows, Mids -->
      <control>
    <status>0x99</status>
    <midino>0x0c</midino>
    <group>[Sampler1]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x0d</midino>
    <group>[Sampler1]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x0e</midino>
    <group>[Sampler1]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x10</midino>
    <group>[Sampler2]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x11</midino>
    <group>[Sampler2]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x99</status>
    <midino>0x12</midino>
    <group>[Sampler2]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0c</midino>
    <group>[Sampler3]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0d</midino>
    <group>[Sampler3]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x0e</midino>
    <group>[Sampler3]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x10</midino>
    <group>[Sampler4]</group>
    <key>filterHighKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x11</midino>
    <group>[Sampler4]</group>
    <key>filterMidKill</key>
    <options>
      <normal/>
    </options>
      </control>
      <control>
    <status>0x9b</status>
    <midino>0x12</midino>
    <group>[Sampler4]</group>
    <key>filterLowKill</key>
    <options>
      <normal/>
    </options>
      </control>
    </controls>

    <outputs>
      <!-- Remote LED Settings -->

      <!-- Kill Lows, Mids, Highs -->
      <output>
    <group>[Channel1]</group>
    <key>filterHighKill</key>
    <status>0x91</status>  <!-- First byte sent to device -->
    <midino>0x37</midino>  <!-- Second byte -->
    <on>0x7F</on>  <!-- Third byte. If not specified, 0x7F is used. -->
    <off>0x00</off> <!-- Alternate third byte. 0x00 is the default. If set to 0xFF, nothing is sent.-->
    <maximum>1</maximum>  <!-- Optional upper value for the Mixxx control, above which the 'off' value is sent. 1.0 is the default. -->
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>filterMidKill</key>
    <status>0x91</status>
    <midino>0x27</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>1</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>filterLowKill</key>
    <status>0x91</status>
    <midino>0x17</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>1</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>filterHighKill</key>
    <status>0x91</status>
    <midino>0x39</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>1</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>filterMidKill</key>
    <status>0x91</status>
    <midino>0x29</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>1</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>filterLowKill</key>
    <status>0x91</status>
    <midino>0x19</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>1</maximum>
    <minimum>1</minimum>
      </output>

      <!-- Channel 1 Looping -->
      <output>
    <group>[Channel1]</group>
    <key>loop_double</key>
    <status>0x91</status>
    <midino>0x34</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>loop_double</key>
    <status>0x91</status>
    <midino>0x35</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>loop_halve</key>
    <status>0x91</status>
    <midino>0x24</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>loop_halve</key>
    <status>0x91</status>
    <midino>0x25</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>

      <!-- Channel 2 Looping -->
      <output>
    <group>[Channel2]</group>
    <key>loop_double</key>
    <status>0x91</status>
    <midino>0x3a</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>loop_double</key>
    <status>0x91</status>
    <midino>0x3b</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>loop_halve</key>
    <status>0x91</status>
    <midino>0x2a</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>loop_halve</key>
    <status>0x91</status>
    <midino>0x2b</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>

      <!-- Sync, Cue -->
      <output>
    <group>[Channel1]</group>
    <key>beatsync</key>
    <status>0x91</status>
    <midino>0x04</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>cue_default</key>
    <status>0x91</status>
    <midino>0x07</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>beatsync</key>
    <status>0x91</status>
    <midino>0x0a</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>cue_default</key>
    <status>0x91</status>
    <midino>0x09</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>

      <!-- Horizontal Arrows -->
      <!--<output>
    <group>[Channel1]</group>
    <key>keylock</key>
    <status>0x90</status>
    <midino>0x24</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>keylock</key>
    <status>0x90</status>
    <midino>0x25</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>pfl</key>
    <status>0x90</status>
    <midino>0x26</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>pfl</key>
    <status>0x90</status>
    <midino>0x27</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>slip_enabled</key>
    <status>0x90</status>
    <midino>0x28</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>slip_enabled</key>
    <status>0x90</status>
    <midino>0x29</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel1]</group>
    <key>flanger</key>
    <status>0x90</status>
    <midino>0x2a</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>flanger</key>
    <status>0x90</status>
    <midino>0x2b</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>-->
      <output>
    <group>[Channel1]</group>
    <key>beats_translate_curpos</key>
    <status>0x90</status>
    <midino>0x2a</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>
      <output>
    <group>[Channel2]</group>
    <key>beats_translate_curpos</key>
    <status>0x90</status>
    <midino>0x2b</midino>
    <on>0x7F</on>
    <off>0x00</off>
    <maximum>127</maximum>
    <minimum>1</minimum>
      </output>

    </outputs>
  </controller>
</MixxxMIDIPreset>
