<?xml version="1.0" encoding="utf-8"?>
    <MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.10.0+">
	<info>
		<name>Numark V7</name>
		<author><PERSON></author>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=3974</forums>
        <manual>numark_v7</manual>
	</info>

    <controller id="Numark V7" port="">
    <scriptfiles>
            <file filename="Numark-V7-scripts.js" functionprefix="NumarkV7"/>
    </scriptfiles>

    <controls>
            <!--Fx Controls-->
            <control>
                <group>[Channel1]</group>
				<key>flanger</key>
                <status>0x90</status>
				<midino>0x52</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
                <group>[Channel2]</group>
				<key>flanger</key>
                <status>0x90</status>
				<midino>0x59</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSliderACoarse</key>
                <status>0xB0</status>
				<midino>0x57</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSliderAFine</key>
                <status>0xB0</status>
				<midino>0x77</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSliderBCoarse</key>
                <status>0xB0</status>
				<midino>0x59</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSliderBFine</key>
                <status>0xB0</status>
				<midino>0x79</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxParam</key>
                <status>0xB0</status>
				<midino>0x56</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxParam</key>
                <status>0xB0</status>
				<midino>0x58</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSelect</key>
                <status>0xB0</status>
				<midino>0x5A</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSelect</key>
                <status>0xB0</status>
				<midino>0x5B</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSelect</key>
                <status>0x90</status>
				<midino>0x53</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Flanger]</group>
				<key>NumarkV7.FxSelect</key>
                <status>0x90</status>
				<midino>0x5A</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <!--Tap buttons-->
            <control>
				<group>[Channel1]</group>
				<key>bpm_tap</key>
                <status>0x90</status>
				<midino>0x1E</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>bpm_tap</key>
                <status>0x90</status>
				<midino>0x3F</midino>
                <options>
					<Button />
				</options>
            </control>
            <!--Reverse Switch-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.ReverseA</key>
                <status>0x90</status>
				<midino>0x1D</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.ReverseA</key>
                <status>0x90</status>
				<midino>0x1C</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.ReverseB</key>
                <status>0x90</status>
				<midino>0x3D</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.ReverseB</key>
                <status>0x90</status>
				<midino>0x3E</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <!--Motor Off Button-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MotorOffButtonA</key>
                <status>0x90</status>
				<midino>0x21</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MotorOffButtonB</key>
                <status>0x90</status>
				<midino>0x42</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Start/Stop Knobs-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MotorStartKnobA</key>
                <status>0xB0</status>
				<midino>0x46</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.MotorStartKnobB</key>
                <status>0xB0</status>
				<midino>0x4E</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MotorStopKnobA</key>
                <status>0xB0</status>
				<midino>0x47</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.MotorStopKnobB</key>
                <status>0xB0</status>
				<midino>0x4F</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Play/Pause/Sync Buttons-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.PlayA</key>
                <status>0x90</status>
				<midino>0x11</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>cue_default</key>
                <status>0x90</status>
				<midino>0x10</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>beatsync</key>
                <status>0x90</status>
				<midino>0x0F</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.PlayB</key>
                <status>0x90</status>
				<midino>0x32</midino>
                <options>
					<Script-Binding />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>cue_default</key>
                <status>0x90</status>
				<midino>0x31</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>beatsync</key>
                <status>0x90</status>
				<midino>0x30</midino>
                <options>
					<Button />
				</options>
            </control>
            <!--Hot Cues-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.ShiftA</key>
                <status>0x90</status>
				<midino>0x12</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.ShiftB</key>
                <status>0x90</status>
				<midino>0x33</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.Hot1A</key>
                <status>0x90</status>
				<midino>0x13</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.Hot2A</key>
                <status>0x90</status>
				<midino>0x14</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.Hot3A</key>
                <status>0x90</status>
				<midino>0x15</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.Hot4A</key>
                <status>0x90</status>
				<midino>0x16</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.Hot5A</key>
                <status>0x90</status>
				<midino>0x17</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.Hot1B</key>
                <status>0x90</status>
				<midino>0x34</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.Hot2B</key>
                <status>0x90</status>
				<midino>0x35</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.Hot3B</key>
                <status>0x90</status>
				<midino>0x36</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.Hot4B</key>
                <status>0x90</status>
				<midino>0x37</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.Hot5B</key>
                <status>0x90</status>
				<midino>0x38</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Pitch Controls-->
            <control>
				<group>[Channel1]</group>
				<key>rate_temp_down</key>
                <status>0x90</status>
				<midino>0x18</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>rate_temp_up</key>
                <status>0x90</status>
				<midino>0x19</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>rate_temp_down</key>
                <status>0x90</status>
				<midino>0x39</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>rate_temp_up</key>
                <status>0x90</status>
				<midino>0x3A</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.PitchACoarse</key>
                <status>0xB0</status>
				<midino>0x04</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.PitchAFine</key>
                <status>0xB0</status>
				<midino>0x24</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.PitchBCoarse</key>
                <status>0xB0</status>
				<midino>0x05</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.PitchBFine</key>
                <status>0xB0</status>
				<midino>0x25</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.RateRangeA</key>
                <status>0x90</status>
				<midino>0x1A</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.RateRangeB</key>
                <status>0x90</status>
				<midino>0x3B</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>keylock</key>
                <status>0x90</status>
				<midino>0x1B</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>keylock</key>
                <status>0x90</status>
				<midino>0x3C</midino>
                <options>
					<Button />
				</options>
            </control>
            <!--Playlist Bank-->
            <control>
				<group>[Playlist]</group>
				<key>SelectTrackKnob</key>
                <status>0xB0</status>
				<midino>0x44</midino>
                <options>
					<SelectKnob />
				</options>
            </control>
            <control>
				<group>[Playlist]</group>
				<key>LoadSelectedIntoFirstStopped</key>
                <status>0x90</status>
				<midino>0x08</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Playlist]</group>
				<key>SelectNextPlaylist</key>
                <status>0x90</status>
				<midino>0x07</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Playlist]</group>
				<key>SelectPrevPlaylist</key>
                <status>0x90</status>
				<midino>0x06</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>LoadSelectedTrack</key>
                <status>0x90</status>
				<midino>0x0C</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Playlist]</group>
				<key>LoadSelectedIntoFirstStopped</key>
                <status>0x90</status>
				<midino>0x0D</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>LoadSelectedTrack</key>
                <status>0x90</status>
				<midino>0x0E</midino>
                <options>
					<Button />
				</options>
            </control>
            <!--Strip Search-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.StripSearchA</key>
                <status>0xB0</status>
				<midino>0x45</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.StripSearchB</key>
                <status>0xB0</status>
				<midino>0x4D</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Loop Controls-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopModeA</key>
                <status>0x90</status>
				<midino>0x27</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopModeB</key>
                <status>0x90</status>
				<midino>0x48</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>reloop_exit</key>
                <status>0x90</status>
				<midino>0x24</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>reloop_exit</key>
                <status>0x90</status>
				<midino>0x45</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.loop_inA</key>
                <status>0x90</status>
				<midino>0x28</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.loop_outA</key>
                <status>0x90</status>
				<midino>0x29</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.loop_inB</key>
                <status>0x90</status>
				<midino>0x49</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.loop_outB</key>
                <status>0x90</status>
				<midino>0x4A</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>loop_halve</key>
                <status>0x90</status>
				<midino>0x22</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>loop_double</key>
                <status>0x90</status>
				<midino>0x23</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>loop_halve</key>
                <status>0x90</status>
				<midino>0x43</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>loop_double</key>
                <status>0x90</status>
				<midino>0x44</midino>
                <options>
					<Button />
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.SelectA</key>
                <status>0x90</status>
				<midino>0x2A</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.SelectB</key>
                <status>0x90</status>
				<midino>0x4B</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.ReloopA</key>
                <status>0x90</status>
				<midino>0x2B</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.ReloopB</key>
                <status>0x90</status>
				<midino>0x4C</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopShiftUpA</key>
                <status>0x90</status>
				<midino>0x26</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopShiftDownA</key>
                <status>0x90</status>
				<midino>0x25</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopShiftUpB</key>
                <status>0x90</status>
				<midino>0x47</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.LoopShiftDownB</key>
                <status>0x90</status>
				<midino>0x46</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Master section-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.DeckSelectL</key>
                <status>0x90</status>
				<midino>0x5C</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.DeckSelectR</key>
                <status>0x90</status>
				<midino>0x7D</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MasterL</key>
                <status>0x90</status>
				<midino>0x54</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.MasterR</key>
                <status>0x90</status>
				<midino>0x5B</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <!--Vinyl-->
            <control>
				<group>[Channel1]</group>
				<key>NumarkV7.ScratchA</key>
                <status>0xB0</status>
				<midino>0x00</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
            <control>
				<group>[Channel2]</group>
				<key>NumarkV7.ScratchB</key>
                <status>0xB0</status>
				<midino>0x02</midino>
                <options>
					<Script-Binding/>
				</options>
            </control>
        </controls>
	</controller>
h</MixxxMIDIPreset>
