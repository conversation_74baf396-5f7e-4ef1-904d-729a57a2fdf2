<?xml version="1.0" encoding="UTF-8"?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
  <info>
      <name>Numark N4</name>
      <author>Swiftb0y</author>
      <description>4 channel MIDI Mapping for Numark N4</description>
      <manual>numark_n4</manual>
  </info>
  <controller id="N4 MIDI 1">
    <scriptfiles>
      <file filename="common-controller-scripts.js"/>
      <file filename="lodash.mixxx.js"/>
      <file filename="midi-components-0.0.js"/>
      <file functionprefix="NumarkN4" filename="Numark-N4-scripts.js"/>
    </scriptfiles>
    <controls>
      <!-- nav encoder -->
      <control>
        <group>[Library]</group>
        <key>NumarkN4.Mixer.navigationEncoderTick.input</key>
        <status>0xB0</status>
        <midino>0x44</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>NumarkN4.Mixer.navigationEncoderButton.input</key>
        <status>0x80</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>NumarkN4.Mixer.navigationEncoderButton.input</key>
        <status>0x90</status>
        <midino>0x08</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <!-- Crossfader side Assignment -->
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].orientationButtonLeft.input</key>
        <status>0x90</status>
        <midino>0x35</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].orientationButtonRight.input</key>
        <status>0x90</status>
        <midino>0x36</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].orientationButtonLeft.input</key>
        <status>0x90</status>
        <midino>0x37</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].orientationButtonRight.input</key>
        <status>0x90</status>
        <midino>0x38</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].orientationButtonLeft.input</key>
        <status>0x90</status>
        <midino>0x39</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].orientationButtonRight.input</key>
        <status>0x90</status>
        <midino>0x3A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].orientationButtonLeft.input</key>
        <status>0x90</status>
        <midino>0x3B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].orientationButtonRight.input</key>
        <status>0x90</status>
        <midino>0x3C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].orientationButtonLeft.input</key>
        <status>0x80</status>
        <midino>0x35</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].orientationButtonRight.input</key>
        <status>0x80</status>
        <midino>0x36</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].orientationButtonLeft.input</key>
        <status>0x80</status>
        <midino>0x37</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].orientationButtonRight.input</key>
        <status>0x80</status>
        <midino>0x38</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].orientationButtonLeft.input</key>
        <status>0x80</status>
        <midino>0x39</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].orientationButtonRight.input</key>
        <status>0x80</status>
        <midino>0x3A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].orientationButtonLeft.input</key>
        <status>0x80</status>
        <midino>0x3B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].orientationButtonRight.input</key>
        <status>0x80</status>
        <midino>0x3C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnEffect1.input</key>
        <status>0x91</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnEffect1.input</key>
        <status>0x92</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnEffect1.input</key>
        <status>0x93</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnEffect1.input</key>
        <status>0x94</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnEffect2.input</key>
        <status>0x91</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnEffect2.input</key>
        <status>0x92</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnEffect2.input</key>
        <status>0x93</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnEffect2.input</key>
        <status>0x94</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnSample3.input</key>
        <status>0x91</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnSample3.input</key>
        <status>0x92</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnSample3.input</key>
        <status>0x93</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnSample3.input</key>
        <status>0x94</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnSample4.input</key>
        <status>0x91</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnSample4.input</key>
        <status>0x92</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnSample4.input</key>
        <status>0x93</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnSample4.input</key>
        <status>0x94</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnEffect1.input</key>
        <status>0x81</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnEffect1.input</key>
        <status>0x82</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnEffect1.input</key>
        <status>0x83</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnEffect1.input</key>
        <status>0x84</status>
        <midino>0x13</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnEffect2.input</key>
        <status>0x81</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnEffect2.input</key>
        <status>0x82</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnEffect2.input</key>
        <status>0x83</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnEffect2.input</key>
        <status>0x84</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnSample3.input</key>
        <status>0x81</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnSample3.input</key>
        <status>0x82</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnSample3.input</key>
        <status>0x83</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnSample3.input</key>
        <status>0x84</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.btnSample4.input</key>
        <status>0x81</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.btnSample4.input</key>
        <status>0x82</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.btnSample4.input</key>
        <status>0x83</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.btnSample4.input</key>
        <status>0x84</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
      </control>


      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[0].input</key>
        <status>0x91</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[0].input</key>
        <status>0x92</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[0].input</key>
        <status>0x93</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[0].input</key>
        <status>0x94</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[1].input</key>
        <status>0x91</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[1].input</key>
        <status>0x92</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[1].input</key>
        <status>0x93</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[1].input</key>
        <status>0x94</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[2].input</key>
        <status>0x91</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[2].input</key>
        <status>0x92</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[2].input</key>
        <status>0x93</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[2].input</key>
        <status>0x94</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[3].input</key>
        <status>0x91</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[3].input</key>
        <status>0x92</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[3].input</key>
        <status>0x93</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[3].input</key>
        <status>0x94</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[0].input</key>
        <status>0x81</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[0].input</key>
        <status>0x82</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[0].input</key>
        <status>0x83</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[0].input</key>
        <status>0x84</status>
        <midino>0x27</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[1].input</key>
        <status>0x81</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[1].input</key>
        <status>0x82</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[1].input</key>
        <status>0x83</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[1].input</key>
        <status>0x84</status>
        <midino>0x28</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[2].input</key>
        <status>0x81</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[2].input</key>
        <status>0x82</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[2].input</key>
        <status>0x83</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[2].input</key>
        <status>0x84</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.hotcueButtons[3].input</key>
        <status>0x81</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.hotcueButtons[3].input</key>
        <status>0x82</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.hotcueButtons[3].input</key>
        <status>0x83</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.hotcueButtons[3].input</key>
        <status>0x84</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.encFxParam1.input</key>
        <status>0xB1</status>
        <midino>0x57</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.encFxParam1.input</key>
        <status>0xB2</status>
        <midino>0x57</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.encFxParam1.input</key>
        <status>0xB3</status>
        <midino>0x57</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.encFxParam1.input</key>
        <status>0xB4</status>
        <midino>0x57</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.encFxParam2.input</key>
        <status>0xB1</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.encFxParam2.input</key>
        <status>0xB2</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.encFxParam2.input</key>
        <status>0xB3</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.encFxParam2.input</key>
        <status>0xB4</status>
        <midino>0x58</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.encSample3.input</key>
        <status>0xB1</status>
        <midino>0x59</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.encSample3.input</key>
        <status>0xB2</status>
        <midino>0x59</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.encSample3.input</key>
        <status>0xB3</status>
        <midino>0x59  </midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.encSample3.input</key>
        <status>0xB4</status>
        <midino>0x59</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].topContainer.encSample4.input</key>
        <status>0xB1</status>
        <midino>0x5A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].topContainer.encSample4.input</key>
        <status>0xB2</status>
        <midino>0x5A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].topContainer.encSample4.input</key>
        <status>0xB3</status>
        <midino>0x5A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].topContainer.encSample4.input</key>
        <status>0xB4</status>
        <midino>0x5A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>NumarkN4.Mixer.pflVuMeter</key>
        <status>0xB0</status>
        <midino>0x5C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].loadButton.input</key>
        <status>0x91</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].loadButton.input</key>
        <status>0x92</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].loadButton.input</key>
        <status>0x93</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].loadButton.input</key>
        <status>0x94</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].loadButton.input</key>
        <status>0x81</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].loadButton.input</key>
        <status>0x82</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].loadButton.input</key>
        <status>0x83</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].loadButton.input</key>
        <status>0x84</status>
        <midino>0x06</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pflButton.input</key>
        <status>0x90</status>
        <midino>0x31</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pflButton.input</key>
        <status>0x90</status>
        <midino>0x32</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pflButton.input</key>
        <status>0x90</status>
        <midino>0x33</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pflButton.input</key>
        <status>0x90</status>
        <midino>0x34</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pflButton.input</key>
        <status>0x80</status>
        <midino>0x31</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pflButton.input</key>
        <status>0x80</status>
        <midino>0x32</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pflButton.input</key>
        <status>0x80</status>
        <midino>0x33</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pflButton.input</key>
        <status>0x80</status>
        <midino>0x34</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].cueButton.input</key>
        <status>0x91</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].cueButton.input</key>
        <status>0x92</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].cueButton.input</key>
        <status>0x93</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].cueButton.input</key>
        <status>0x94</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].cueButton.input</key>
        <status>0x81</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].cueButton.input</key>
        <status>0x82</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].cueButton.input</key>
        <status>0x83</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].cueButton.input</key>
        <status>0x84</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].searchButton.input</key>
        <status>0x91</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].searchButton.input</key>
        <status>0x92</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].searchButton.input</key>
        <status>0x93</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].searchButton.input</key>
        <status>0x94</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].searchButton.input</key>
        <status>0x81</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].searchButton.input</key>
        <status>0x82</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].searchButton.input</key>
        <status>0x83</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].searchButton.input</key>
        <status>0x84</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Mixer Profile]</group>
        <key>NumarkN4.Mixer.changeCrossfaderContour.input</key>
        <status>0x90</status>
        <midino>0x4B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Mixer Profile]</group>
        <key>NumarkN4.Mixer.changeCrossfaderContour.input</key>
        <status>0x80</status>
        <midino>0x4B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Mixer.channelInputSwitcherL.input</key>
        <status>0x90</status>
        <midino>0x49</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Mixer.channelInputSwitcherR.input</key>
        <status>0x90</status>
        <midino>0x4A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Mixer.channelInputSwitcherL.input</key>
        <status>0x80</status>
        <midino>0x49</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Mixer.channelInputSwitcherR.input</key>
        <status>0x80</status>
        <midino>0x4A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>NumarkN4.Mixer.deckChangeL.input</key>
        <status>0xB0</status>
        <midino>0x50</midino>
        <options>
          <script-binding />
        </options>
        <description>handles deck assignment led feedback right</description>
      </control>
      <control>
        <group>[Master]</group>
        <key>NumarkN4.Mixer.deckChangeR.input</key>
        <status>0xB0</status>
        <midino>0x51</midino>
        <options>
          <script-binding />
        </options>
        <description>handles deck assignment led feedback left</description>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].playButton.input</key>-
        <status>0x91</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].playButton.input</key>
        <status>0x92</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].playButton.input</key>-
        <status>0x93</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].playButton.input</key>
        <status>0x94</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].playButton.input</key>-
        <status>0x81</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].playButton.input</key>
        <status>0x82</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].playButton.input</key>-
        <status>0x83</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].playButton.input</key>
        <status>0x84</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].syncButton.input</key>
        <status>0x91</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].syncButton.input</key>
        <status>0x92</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].syncButton.input</key>
        <status>0x93</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].syncButton.input</key>
        <status>0x94</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].syncButton.input</key>
        <status>0x81</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].syncButton.input</key>
        <status>0x82</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].syncButton.input</key>
        <status>0x83</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].syncButton.input</key>
        <status>0x84</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].tapButton.input</key>
        <status>0x91</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].tapButton.input</key>
        <status>0x92</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].tapButton.input</key>
        <status>0x93</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].tapButton.input</key>
        <status>0x94</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].tapButton.input</key>
        <status>0x81</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].tapButton.input</key>
        <status>0x82</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].tapButton.input</key>
        <status>0x83</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].tapButton.input</key>
        <status>0x84</status>
        <midino>0x1E</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].keylockButton.input</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].keylockButton.input</key>
        <status>0x92</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].keylockButton.input</key>
        <status>0x93</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].keylockButton.input</key>
        <status>0x94</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].keylockButton.input</key>
        <status>0x81</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].keylockButton.input</key>
        <status>0x82</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].keylockButton.input</key>
        <status>0x83</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].keylockButton.input</key>
        <status>0x84</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pitchRange.input</key>
        <status>0x91</status>
        <midino>0x1A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pitchRange.input</key>
        <status>0x92</status>
        <midino>0x1A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pitchRange.input</key>
        <status>0x93</status>
        <midino>0x1A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pitchRange.input</key>
        <status>0x94</status>
        <midino>0x1A</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].jogWheelScratchEnable.input</key>
        <status>0x91</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].jogWheelScratchEnable.input</key>
        <status>0x81</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].jogWheelScratchEnable.input</key>
        <status>0x92</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].jogWheelScratchEnable.input</key>
        <status>0x82</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].jogWheelScratchEnable.input</key>
        <status>0x93</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].jogWheelScratchEnable.input</key>
        <status>0x83</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].jogWheelScratchEnable.input</key>
        <status>0x94</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].jogWheelScratchEnable.input</key>
        <status>0x84</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].jogWheelTurn.input</key>
        <status>0xB1</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].jogWheelTurn.input</key>
        <status>0xB2</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].jogWheelTurn.input</key>
        <status>0xB3</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].jogWheelTurn.input</key>
        <status>0xB4</status>
        <midino>0x20</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].shiftButton.input</key>
        <status>0x91</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].shiftButton.input</key>
        <status>0x81</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].shiftButton.input</key>
        <status>0x92</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].shiftButton.input</key>
        <status>0x82</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].shiftButton.input</key>
        <status>0x93</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].shiftButton.input</key>
        <status>0x83</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].shiftButton.input</key>
        <status>0x94</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].shiftButton.input</key>
        <status>0x84</status>
        <midino>0x12</midino>
        <options>
          <script-binding />
        </options>
      </control>

      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pitchBendMinus.input</key>
        <status>0x91</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus left on</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pitchBendMinus.input</key>
        <status>0x92</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right on</description>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pitchBendMinus.input</key>
        <status>0x81</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right off</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pitchBendMinus.input</key>
        <status>0x82</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right off</description>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pitchBendPlus.input</key>
        <status>0x91</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus left on</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pitchBendPlus.input</key>
        <status>0x92</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus right on</description>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].pitchBendPlus.input</key>
        <status>0x81</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus left off</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].pitchBendPlus.input</key>
        <status>0x82</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus right off</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pitchBendMinus.input</key>
        <status>0x93</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus left on</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pitchBendMinus.input</key>
        <status>0x94</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right on</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pitchBendMinus.input</key>
        <status>0x83</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right off</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pitchBendMinus.input</key>
        <status>0x84</status>
        <midino>0x18</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendMinus right off</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pitchBendPlus.input</key>
        <status>0x93</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus left on</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pitchBendPlus.input</key>
        <status>0x94</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus right on</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].pitchBendPlus.input</key>
        <status>0x83</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus left off</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].pitchBendPlus.input</key>
        <status>0x84</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>pitchBendPlus right off</description>
      </control>
      <!-- Faders -->
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x07</midino>
        <options>
          <fourteen-bit-msb/>
          <Soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x27</midino>
        <options>
          <fourteen-bit-lsb/>
          <Soft-takeover/>
        </options>
      </control>
      <!--mixer knobs-->
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[1].inputLSB</key>
        <status>0xB0</status>
        <midino>0x29</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_LOW LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[1].inputMSB</key>
        <status>0xB0</status>
        <midino>0x09</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_LOW MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[1].inputLSB</key>
        <status>0xB0</status>
        <midino>0x2E</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_LOW LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[1].inputMSB</key>
        <status>0xB0</status>
        <midino>0x0E</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_LOW MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[1].inputLSB</key>
        <status>0xB0</status>
        <midino>0x34</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_LOW LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[1].inputMSB</key>
        <status>0xB0</status>
        <midino>0x14</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_LOW MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[1].inputLSB</key>
        <status>0xB0</status>
        <midino>0x39</midino>
        <options>
          <script-binding />

        </options>
        <description>Channel4 EQ_LOW LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[1].inputMSB</key>
        <status>0xB0</status>
        <midino>0x19</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 EQ_LOW MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[2].inputLSB</key>
        <status>0xB0</status>
        <midino>0x2A</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_MID LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[2].inputMSB</key>
        <status>0xB0</status>
        <midino>0x0A</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_MID MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[2].inputLSB</key>
        <status>0xB0</status>
        <midino>0x2F</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_MID LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[2].inputMSB</key>
        <status>0xB0</status>
        <midino>0x0F</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_MID MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[2].inputLSB</key>
        <status>0xB0</status>
        <midino>0x35</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_MID LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[2].inputMSB</key>
        <status>0xB0</status>
        <midino>0x15</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_MID MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[2].inputLSB</key>
        <status>0xB0</status>
        <midino>0x3A</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 EQ_MID LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[2].inputMSB</key>
        <status>0xB0</status>
        <midino>0x1A</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 EQ_MID MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[3].inputLSB</key>
        <status>0xB0</status>
        <midino>0x2B</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_HIGH LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].eqKnobs[3].inputMSB</key>
        <status>0xB0</status>
        <midino>0x0B</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 EQ_HIGH MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[3].inputLSB</key>
        <status>0xB0</status>
        <midino>0x30</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_HIGH LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].eqKnobs[3].inputMSB</key>
        <status>0xB0</status>
        <midino>0x10</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 EQ_HIGH MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[3].inputLSB</key>
        <status>0xB0</status>
        <midino>0x36</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_HIGH LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].eqKnobs[3].inputMSB</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 EQ_HIGH MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[3].inputLSB</key>
        <status>0xB0</status>
        <midino>0x3B</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 EQ_HIGH LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].eqKnobs[3].inputMSB</key>
        <status>0xB0</status>
        <midino>0x1B</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 EQ_HIGH MSB</description>
      </control>
      <!--gain knobs-->
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].gainKnob.inputLSB</key>
        <status>0xB0</status>
        <midino>0x2C</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 GAIN LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>NumarkN4.Decks[1].gainKnob.inputMSB</key>
        <status>0xB0</status>
        <midino>0x0C</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel1 GAIN MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].gainKnob.inputLSB</key>
        <status>0xB0</status>
        <midino>0x31</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 GAIN LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>NumarkN4.Decks[2].gainKnob.inputMSB</key>
        <status>0xB0</status>
        <midino>0x11</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel2 GAIN MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].gainKnob.inputLSB</key>
        <status>0xB0</status>
        <midino>0x37</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 GAIN LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel3]_Effect1]</group>
        <key>NumarkN4.Decks[3].gainKnob.inputMSB</key>
        <status>0xB0</status>
        <midino>0x17</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel3 GAIN MSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].gainKnob.inputLSB</key>
        <status>0xB0</status>
        <midino>0x3C</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 GAIN LSB</description>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel4]_Effect1]</group>
        <key>NumarkN4.Decks[4].gainKnob.inputMSB</key>
        <status>0xB0</status>
        <midino>0x1C</midino>
        <options>
          <script-binding />
        </options>
        <description>Channel4 GAIN MSB</description>
      </control>
      <!--volume slider-->
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x28</midino>
        <options>
          <fourteen-bit-lsb/>
          <Soft-takeover/>
        </options>
        <description>Channel1 Volume LSB</description>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x08</midino>
        <options>
          <fourteen-bit-msb/>
          <Soft-takeover/>
        </options>
        <description>Channel1 Volume MSB</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x2D</midino>
        <options>
          <fourteen-bit-lsb/>
          <Soft-takeover/>
        </options>
        <description>Channel2 Volume LSB</description>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x0D</midino>
        <options>
          <fourteen-bit-msb/>
          <Soft-takeover/>
        </options>
        <description>Channel2 Volume MSB</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x33</midino>
        <options>
          <fourteen-bit-lsb/>
          <Soft-takeover/>
        </options>
        <description>Channel3 Volume LSB</description>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x13</midino>
        <options>
          <fourteen-bit-msb/>
          <Soft-takeover/>
        </options>
        <description>Channel3 Volume MSB</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x38</midino>
        <options>
          <fourteen-bit-lsb/>
          <Soft-takeover/>
        </options>
        <description>Channel4 Volume LSB</description>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>volume</key>
        <status>0xB0</status>
        <midino>0x18</midino>
        <options>
          <fourteen-bit-msb/>
          <Soft-takeover/>
        </options>
        <description>Channel4 Volume MSB</description>
      </control>
      <!-- Bpm Slider -->
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].bpmSlider.inputMSB</key>
        <status>0xB1</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>NumarkN4.Decks[1].bpmSlider.inputLSB</key>
        <status>0xB1</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].bpmSlider.inputMSB</key>
        <status>0xB2</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>NumarkN4.Decks[2].bpmSlider.inputLSB</key>
        <status>0xB2</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].bpmSlider.inputMSB</key>
        <status>0xB3</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>NumarkN4.Decks[3].bpmSlider.inputLSB</key>
        <status>0xB3</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].bpmSlider.inputMSB</key>
        <status>0xB4</status>
        <midino>0x01</midino>
        <options>
          <script-binding />
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>NumarkN4.Decks[4].bpmSlider.inputLSB</key>
        <status>0xB4</status>
        <midino>0x21</midino>
        <options>
          <script-binding />
        </options>
      </control>
    </controls>
  </controller>
</MixxxControllerPreset>
