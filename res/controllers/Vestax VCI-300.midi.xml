<?xml version="1.0" encoding="utf-8"?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
	<info>
		<name>Vestax VCI-300</name>
		<author><PERSON><PERSON>z</author>
		<forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=4021</forums>
        <manual>vestax_vci_300</manual>
	</info>
	<controller id="Vestax VCI-300">
		<scriptfiles>
			<file filename="Vestax-VCI-300-scripts.js" functionprefix="VestaxVCI300"/>
		</scriptfiles>
		<controls>
<!-- Master Controls -->
			<control>
				<group>[Master]</group>
				<midino>0x18</midino>
				<status>0xB0</status>
				<key>crossfader</key>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x1A</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onCrossfaderCurve</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x1B</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onLinefaderCurve</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x19</midino>
				<status>0xB0</status>
				<key>headMix</key>
				<options>
					<invert/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x52</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onScrollButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Navigation Controls -->
			<control>
				<group>[Master]</group>
				<midino>0x4A</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCratesButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x4B</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onFilesButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x4C</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onBrowseButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x4D</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onNavigationUpButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x4E</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onNavigationDownButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x4F</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onNavigationBackButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x50</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onNavigationFwdButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Master]</group>
				<midino>0x51</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onNavigationTabButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Monitor Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x36</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPFLButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x49</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPFLButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Shift Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x31</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onShiftButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x44</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onShiftButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Cue/Play Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x24</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCueButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x37</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCueButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x25</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPlayButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x38</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPlayButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x32</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCensorFilterButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x45</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCensorFilterButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Volume Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x0E</midino>
				<status>0xB0</status>
				<key>pregain</key>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x14</midino>
				<status>0xB0</status>
				<key>pregain</key>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x0C</midino>
				<status>0xB0</status>
				<key>volume</key>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x12</midino>
				<status>0xB0</status>
				<key>volume</key>
			</control>
<!-- Deck Filter Controls -->
			<control>
				<group>[EqualizerRack1_[Channel1]_Effect1]</group>
				<midino>0x0F</midino>
				<status>0xB0</status>
				<key>parameter3</key>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x10</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onFilterMidKnob</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel1]_Effect1]</group>
				<midino>0x11</midino>
				<status>0xB0</status>
				<key>parameter1</key>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel2]_Effect1]</group>
				<midino>0x15</midino>
				<status>0xB0</status>
				<key>parameter3</key>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x16</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onFilterMidKnob</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EqualizerRack1_[Channel2]_Effect1]</group>
				<midino>0x17</midino>
				<status>0xB0</status>
				<key>parameter1</key>
			</control>
<!-- Deck Pitch/Sync Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x0D</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onPitchHighValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x2D</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onPitchLowValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x13</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onPitchHighValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x33</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onPitchLowValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x26</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onKeyLockButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x39</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onKeyLockButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x29</midino>
				<status>0x80</status>
				<key>sync_enabled</key>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x29</midino>
				<status>0x90</status>
				<key>sync_enabled</key>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3C</midino>
				<status>0x80</status>
				<key>sync_enabled</key>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3C</midino>
				<status>0x90</status>
				<key>sync_enabled</key>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x27</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPitchShiftDownButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x28</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPitchShiftUpButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3A</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPitchShiftDownButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3B</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onPitchShiftUpButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Jog Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x2D</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onScratchButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x53</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onJogTouch</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x1C</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onJogHighValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x3C</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onJogLowValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x40</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onScratchButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x54</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onJogTouch</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x1D</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onJogHighValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3D</midino>
				<status>0xB0</status>
				<key>VestaxVCI300.onJogLowValue</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Hotcue/Loop Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x2A</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue1InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x2B</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue2InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x2C</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue3InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x2E</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut1LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x2F</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut2LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x30</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut3LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3D</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue1InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3E</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue2InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x3F</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onCue3InButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x41</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut1LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x42</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut2LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x43</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onOut3LoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
<!-- Deck Beatloop Controls -->
			<control>
				<group>[Channel1]</group>
				<midino>0x33</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onAutoLoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x34</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onHalfPrevButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<midino>0x35</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onDoubleNextButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x46</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onAutoLoopButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x47</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onHalfPrevButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<midino>0x48</midino>
				<status>0x90</status>
				<key>VestaxVCI300.onDoubleNextButton</key>
				<options>
					<script-binding/>
				</options>
			</control>
		</controls>

		<outputs>
			<!-- PFL -->
			<output>
				<group>[Channel1]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x2D</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>pfl</key>
				<status>0x90</status>
				<midino>0x31</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<!-- Keylock -->
			<output>
				<group>[Channel1]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x24</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>keylock</key>
				<status>0x90</status>
				<midino>0x33</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<!-- Cue -->
			<output>
				<group>[Channel1]</group>
				<key>cue_indicator</key>
				<status>0x90</status>
				<midino>0x71</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>cue_indicator</key>
				<status>0x90</status>
				<midino>0x73</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<!-- Play -->
			<output>
				<group>[Channel1]</group>
				<key>play_indicator</key>
				<status>0x90</status>
				<midino>0x72</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>play_indicator</key>
				<status>0x90</status>
				<midino>0x74</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<!-- Hotcues [Channel1] -->
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_enabled</key>
				<status>0x90</status>
				<midino>0x41</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_1_enabled</key>
				<status>0x90</status>
				<midino>0x47</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_enabled</key>
				<status>0x90</status>
				<midino>0x43</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_2_enabled</key>
				<status>0x90</status>
				<midino>0x49</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_enabled</key>
				<status>0x90</status>
				<midino>0x45</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>hotcue_3_enabled</key>
				<status>0x90</status>
				<midino>0x4B</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<!-- Hotcues [Channel2] -->
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_enabled</key>
				<status>0x90</status>
				<midino>0x4D</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_1_enabled</key>
				<status>0x90</status>
				<midino>0x53</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_enabled</key>
				<status>0x90</status>
				<midino>0x4F</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_2_enabled</key>
				<status>0x90</status>
				<midino>0x55</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_enabled</key>
				<status>0x90</status>
				<midino>0x51</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>hotcue_3_enabled</key>
				<status>0x90</status>
				<midino>0x57</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<!-- Cue Loop [Channel1] -->
			<output>
				<group>[Channel1]</group>
				<key>loop_start_position</key>
				<status>0x90</status>
				<midino>0x48</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0</minimum>
				<maximum>2147483647</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_start_position</key>
				<status>0x90</status>
				<midino>0x42</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>-2</minimum>
				<maximum>-2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_end_position</key>
				<status>0x90</status>
				<midino>0x4A</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0</minimum>
				<maximum>2147483647</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_end_position</key>
				<status>0x90</status>
				<midino>0x44</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>-2</minimum>
				<maximum>-2</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_enabled</key>
				<status>0x90</status>
				<midino>0x4C</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>loop_enabled</key>
				<status>0x90</status>
				<midino>0x46</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<!-- Cue Loop [Channel2] -->
			<output>
				<group>[Channel2]</group>
				<key>loop_start_position</key>
				<status>0x90</status>
				<midino>0x54</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0</minimum>
				<maximum>2147483647</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_start_position</key>
				<status>0x90</status>
				<midino>0x4E</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>-2</minimum>
				<maximum>-2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_end_position</key>
				<status>0x90</status>
				<midino>0x56</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0</minimum>
				<maximum>2147483647</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_end_position</key>
				<status>0x90</status>
				<midino>0x50</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>-2</minimum>
				<maximum>-2</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_enabled</key>
				<status>0x90</status>
				<midino>0x58</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>loop_enabled</key>
				<status>0x90</status>
				<midino>0x52</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<!-- disabled -->
				<minimum>0.5</minimum>
				<maximum>0.5</maximum>
			</output>
			<!-- VU meter: 1st green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x64</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.09</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x70</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.09</minimum>
			</output>
			<!-- VU meter: 2nd green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x63</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.18</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6F</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.18</minimum>
			</output>
			<!-- VU meter: 3rd green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x62</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.27</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6E</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.27</minimum>
			</output>
			<!-- VU meter: 4th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x61</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.36</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6D</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.36</minimum>
			</output>
			<!-- VU meter: 5th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x60</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.45</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6C</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.45</minimum>
			</output>
			<!-- VU meter: 6th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5F</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.54</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6B</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.54</minimum>
			</output>
			<!-- VU meter: 7th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5E</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.63</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x6A</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.63</minimum>
			</output>
			<!-- VU meter: 8th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5D</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.72</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x69</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.72</minimum>
			</output>
			<!-- VU meter: 9th green -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5C</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.80</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x68</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.80</minimum>
			</output>
			<!-- VU meter: 1st red -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5B</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.88</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x67</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.88</minimum>
			</output>
			<!-- VU meter: 2nd red -->
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x5A</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.94</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x66</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.94</minimum>
			</output>
			<!-- VU meter: 3rd red (peak) -->
			<output>
				<group>[Channel1]</group>
				<key>peak_indicator</key>
				<status>0x90</status>
				<midino>0x59</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>peak_indicator</key>
				<status>0x90</status>
				<midino>0x65</midino>
				<on>0x7F</on>
				<off>0x00</off>
				<minimum>0.5</minimum>
			</output>
		</outputs>
	</controller>
</MixxxControllerPreset>
