<?xml version="1.0" encoding="utf-8"?>
<MixxxControllerPreset schemaVersion="1" mixxxVersion="1.11+">
    <info>
        <name>EKS Otus HID</name>
        <author><PERSON><PERSON></author>
        <description>HID mapping for EKS Otus controllers</description>
        <manual>eks_otus</manual>
        <devices>
            <product protocol="hid" vendor_id="0x1157" product_id="0x300" usage_page="0xffa0" usage="0x1" interface_number="0x3" />
        </devices>
    </info>
    <controller id="EKS Otus">

        <scriptfiles>
            <file filename="common-hid-packet-parser.js" functionprefix=""/>
            <file filename="EKS-Otus.js" functionprefix="EksOtus"/>
        </scriptfiles>
    </controller>
</MixxxControllerPreset>
