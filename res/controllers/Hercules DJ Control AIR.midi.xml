<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
    <info>
      <name>Hercules DJ Control AIR</name>
      <author>r<PERSON><PERSON></author>
      <description>Mapping for Hercules DJ Control AIR</description>
      <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=3263</forums>
        <manual>hercules_djcontrol_air</manual>
    </info>
    <controller id="DJ Control Air">
        <scriptfiles>
            <file functionprefix="HerculesAir" filename="Hercules-DJ-Control-AIR-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x39</midino>
                <group>[Master]</group>
                <key>HerculesAir.headMix</key>
                <description>Head Mix Button</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3a</midino>
                <group>[Master]</group>
                <key>HerculesAir.headCue</key>
                <description>Head Cue Button</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x30</midino>
                <group>[Channel1]</group>
                <key>HerculesAir.jog</key>
                <description>Jog Wheel Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x31</midino>
                <group>[Channel2]</group>
                <key>HerculesAir.jog</key>
                <description>Jog Wheel Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x32</midino>
                <group>[Channel1]</group>
                <key>HerculesAir.wheelTurn</key>
                <description>Jog Scratch Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description>Flanger Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x33</midino>
                <group>[Channel2]</group>
                <key>HerculesAir.wheelTurn</key>
                <description>Jog Scratch Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>beats_translate_curpos</key>
                <description>Move Beatgrid to align with current playposition Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x34</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <description>Pitchslider Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>quantize</key>
                <description>Quantize Loops/Hot-Cues Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x35</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <description>Pitchslider Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>Keylock Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x36</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>Volume Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Sampler1]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler1 Start/Stop Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x37</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description>Treble Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Sampler2]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler2 Start/Stop Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x38</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description>Mid Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Sampler3]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler3 Start/Stop Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x39</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description>Bass Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Sampler4]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler4 Start/Stop Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3a</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <description>Crossfader</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x9</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description>Begin Loop Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3b</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>Volume Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xa</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description>End Loop Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3c</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description>Treble Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <description>Loop Double Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3d</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description>Mid Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xc</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description>ReLoop Exit Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3e</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description>Bass Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>rate_perm_down</key>
                <description>Rate Perm Down Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>rate_perm_up</key>
                <description>Rate Perm Up Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3f</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <description>Flanger Depth</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description>&lt;&lt; Deck A</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description>>> Deck A</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>Cue Button Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description>Play/Pause Deck A</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description>Beat Sync A</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x14</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>Listen Deck A</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <description>Load Track Deck A</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>HerculesAir.scratch_enable</key>
                <description>Jog Touch Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description>Flanger Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>beats_translate_curpos</key>
                <description>Move Beatgrid to align with current playposition Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>quantize</key>
                <description>Quantize Loops/Hot-Cues Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>Keylock Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[Sampler1]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler1 Start/Stop Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1c</midino>
                <group>[Sampler2]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler2 Start/Stop Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[Sampler3]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler3 Start/Stop Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1e</midino>
                <group>[Sampler4]</group>
                <key>HerculesAir.sampler</key>
                <description>Sampler4 Start/Stop Deck A</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1f</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description>Begin Loop Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x20</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description>End Loop Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x21</midino>
                <group>[Channel2]</group>
                <key>loop_double</key>
                <description>Loop Double Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x22</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description>ReLoop Exit Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x23</midino>
                <group>[Channel2]</group>
                <key>rate_perm_down</key>
                <description>Rate Perm Down Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>rate_perm_up</key>
                <description>Rate Perm Up Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x25</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description>&lt;&lt; Deck B</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x26</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description>>> Deck B</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x27</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>Cue Button Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x28</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description>Play/Pause Deck B</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x29</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description>Beat Sync B</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2a</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>Listen Deck B</description>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2b</midino>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <description>Load Track Deck B</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2c</midino>
                <group>[Channel2]</group>
                <key>HerculesAir.scratch_enable</key>
                <description>Jog Touch Deck B</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2e</midino>
                <group>[Master]</group>
                <key>HerculesAir.shift</key>
                <description>Use Magic Button as Shift Button</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2d</midino>
                <group>[Master]</group>
                <key>HerculesAir.spinback</key>
                <description>Use Scratch Button to toggle spinback effect</description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Playlist]</group>
                <key>SelectPrevTrack</key>
                <description>Playlist Up</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x34</midino>
                <group>[Playlist]</group>
                <key>SelectNextTrack</key>
                <description>Playlist Down</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x35</midino>
                <group>[Playlist]</group>
                <key>SelectNextPlaylist</key>
                <description>Playlist Files</description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x36</midino>
                <group>[Playlist]</group>
                <key>SelectPrevPlaylist</key>
                <description>Playlist Folders</description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Sampler3]</group>
                <key>play</key>
                <description>Sampler 3 LED Deck A</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x7</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler3]</group>
                <key>play</key>
                <description>Sampler 3 LED Deck B</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1d</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beat_active</key>
                <description>Sync LED Deck A</description>
                <minimum>0</minimum>
                <maximum>1</maximum>
                <status>0x0</status>
                <midino>0x13</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>quantize</key>
                <description>Quantize Loops/Hot-Cues Deck B LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x19</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description>Listen Button Deck A LED</description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x14</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>play</key>
                <description>Sampler 2 LED Deck A</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x6</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler2]</group>
                <key>play</key>
                <description>Sampler 2 LED Deck B</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1c</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>Keylock Deck B LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1a</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description>FX button LED Deck A</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x0</status>
                <midino>0x1</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler1]</group>
                <key>play</key>
                <description>Sampler 1 LED Deck A</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x5</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler1]</group>
                <key>play</key>
                <description>Sampler 1 LED Deck B</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1b</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>Keylock Deck A LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x4</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description>FX button LED Deck B</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x0</status>
                <midino>0x17</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description>ReLoop Exit Deck A LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0xc</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <description>Play Button LED Deck B</description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x28</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description>ReLoop Exit Deck B LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x22</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beats_translate_curpos</key>
                <description>Move Beatgrid to align with current playposition Deck A LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x2</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beats_translate_curpos</key>
                <description>Move Beatgrid to align with current playposition Deck B LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x18</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <description>Play Button LED Deck A</description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x12</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beat_active</key>
                <description>Sync LED Deck A</description>
                <minimum>0</minimum>
                <maximum>1</maximum>
                <status>0x0</status>
                <midino>0x29</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description>Listen Button Deck B LED</description>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x2a</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>quantize</key>
                <description>Quantize Loops/Hot-Cues Deck A LED</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x3</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler4]</group>
                <key>play</key>
                <description>Sampler 4 LED Deck A</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x8</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Sampler4]</group>
                <key>play</key>
                <description>Sampler 4 LED Deck B</description>
                <minimum>0.1</minimum>
                <maximum>1</maximum>
                <status>0x90</status>
                <midino>0x1e</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
