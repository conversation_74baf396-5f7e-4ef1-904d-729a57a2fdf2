<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Reloop Digital Jockey 2 Interface Edition</name>
        <author>DJ aK</author>
        <description>Controller mapping for Reloop Digital Jockey 2 Interface Edition</description>
        <forums>https://mixxx.discourse.group/t/reloop-digital-jockey-2-mapping-by-dj-ak/23971</forums>
        <manual>reloop_digital_jockey_2_interface_edition</manual>
    </info>
    <controller id="DIGITALJOCKEY2">
        <scriptfiles>
            <file functionprefix="" filename="lodash.mixxx.js"/>
            <file functionprefix="" filename="midi-components-0.0.js"/>
            <file functionprefix="RDJ2" filename="Reloop-Digital-Jockey-2-IE.scripts.js"/>
        </scriptfiles>

        <controls>
        <!-- Master -->
            <control>
                <group>[Master]</group>
                <key>gain</key>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headGain</key>
                <status>0xB0</status>
                <midino>0x10</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB0</status>
                <midino>0x11</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x12</midino>
                <options>
                    <normal/>
                </options>
            </control>

        <!-- Left Deck -->
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.playButton.input</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.playButton.input</key>
                <status>0x90</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.cueButton.input</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.cueButton.input</key>
                <status>0x90</status>
                <midino>0x36</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_play</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel1]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.syncButton.input</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.syncButton.input</key>
                <status>0x90</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.bendPlusButton.input</key>
                <status>0x90</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Library]</group>
                <key>GoToItem</key>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.bendMinusButton.input</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Library]</group>
                <key>GoToItem</key>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.onJogTouch</key>
                <status>0x90</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.onJogSpin</key>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.onJogSpin</key>
                <status>0xB0</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SEARCH -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SEARCH SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SCRATCH -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SCRATCH SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x39</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- FX DRY/WET -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- FX DRY/WET SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x3A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- LOW knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- LOW kill knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter1</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- MID knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x04</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- MID kill knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter2</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH knob SHIFT -->
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH kill knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>RDJ2.leftDeck.highKillQuickEffectButton.input</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- HIGH kill knob SHIFT -->
                <group>[QuickEffectRack1_[Channel1]_Effect1]</group>
                <key>RDJ2.leftDeck.highKillQuickEffectButton.input</key>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- volume fader -->
                <group>[Channel1]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- pregain -->
                <group>[Channel1]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xE0</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftShiftButton.input</key>
                <status>0x90</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loadButton.input</key>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loadButton.input</key>
                <status>0x90</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Right Deck -->
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.playButton.input</key>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.playButton.input</key>
                <status>0x90</status>
                <midino>0x73</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.cueButton.input</key>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.cueButton.input</key>
                <status>0x90</status>
                <midino>0x72</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_play</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel2]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x71</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.syncButton.input</key>
                <status>0x90</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- shifted -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.syncButton.input</key>
                <status>0x90</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.bendPlusButton.input</key>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>      <!-- shifted -->
                <group>[Library]</group>
                <key>GoToItem</key>
                <status>0x90</status>
                <midino>0x5E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.bendMinusButton.input</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>      <!-- shifted -->
                <group>[Library]</group>
                <key>GoToItem</key>
                <status>0x90</status>
                <midino>0x5D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.onJogTouch</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.onJogSpin</key>
                <status>0xB0</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.onJogSpin</key>
                <status>0xB0</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SEARCH -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SEARCH SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x74</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SCRATCH -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SCRATCH SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x75</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- FX DRY/WET -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- FX DRY/WET SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.jogModeSelector.input</key>
                <status>0x90</status>
                <midino>0x76</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- LOW knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter1</key>
                <status>0xB0</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- LOW kill knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter1</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- MID knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter2</key>
                <status>0xB0</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- MID kill knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter2</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>parameter3</key>
                <status>0xB0</status>
                <midino>0x3F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH knob SHIFT -->
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x5D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- HIGH kill knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>RDJ2.rightDeck.highKillQuickEffectButton.input</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- HIGH kill knob SHIFT -->
                <group>[QuickEffectRack1_[Channel2]_Effect1]</group>
                <key>RDJ2.rightDeck.highKillQuickEffectButton.input</key>
                <status>0x90</status>
                <midino>0x6E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- volume fader -->
                <group>[Channel2]</group>
                <key>volume</key>
                <status>0xB0</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- pregain -->
                <group>[Channel2]</group>
                <key>pregain</key>
                <status>0xB0</status>
                <midino>0x3E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xE1</status>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x3E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightShiftButton.input</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loadButton.input</key>
                <status>0x90</status>
                <midino>0x4F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loadButton.input</key>
                <status>0x90</status>
                <midino>0x6D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Left EFX -->
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.fx1.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.fx1.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.enableButtons[3].input</key>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.fx1AssignmentButton.input</key>
                <status>0x90</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.fx1.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.dryWetKnob.input</key>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopsizeKnob.input</key>
                <status>0xB0</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.fx1.effectFocusButton.input</key>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.fx2AssignmentButton.input</key>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Right EFX -->
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.fx2.enableButtons[1].input</key>
                <status>0x90</status>
                <midino>0x66</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.fx2.enableButtons[2].input</key>
                <status>0x90</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.enableButtons[3].input</key>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.fx1AssignmentButton.input</key>
                <status>0x90</status>
                <midino>0x68</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[1].input</key>
                <status>0xB0</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[2].input</key>
                <status>0xB0</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.fx2.knobs[3].input</key>
                <status>0xB0</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.dryWetKnob.input</key>
                <status>0xB0</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopsizeKnob.input</key>
                <status>0xB0</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.fx2.effectFocusButton.input</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.fx2AssignmentButton.input</key>
                <status>0x90</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Left Loop Unit -->
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopInButton.input</key>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopInButton.input</key>
                <status>0x90</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopOutButton.input</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopOutButton.input</key>
                <status>0x90</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.autoLoopButton.input</key>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.autoLoopButton.input</key>
                <status>0x90</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopActiveButton.input</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel1]</group>
                <key>RDJ2.leftDeck.loopActiveButton.input</key>
                <status>0x90</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Right Loop Unit -->
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopInButton.input</key>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopInButton.input</key>
                <status>0x90</status>
                <midino>0x69</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopOutButton.input</key>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopOutButton.input</key>
                <status>0x90</status>
                <midino>0x6A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.autoLoopButton.input</key>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.autoLoopButton.input</key>
                <status>0x90</status>
                <midino>0x6B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopActiveButton.input</key>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Channel2]</group>
                <key>RDJ2.rightDeck.loopActiveButton.input</key>
                <status>0x90</status>
                <midino>0x6C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>

        <!-- Library -->
            <control>
                <group>[Master]</group>
                <key>RDJ2.trax.traxButton.input</key>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- LEFT SHIFT (ONLY)-->
                <group>[Master]</group>
                <key>RDJ2.trax.traxButton.input</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Library]</group>
                <key>RDJ2.trax.traxKnob.input</key>
                <status>0xB0</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>   <!-- SHIFT -->
                <group>[Library]</group>
                <key>RDJ2.trax.traxKnob.input</key>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>

        <outputs>
        <!-- Left Deck -->
            <output>    <!-- LOW kill knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter1</key>
                <status>0x90</status>
                <midino>0x16</midino>
                <minimum>0.5</minimum>
            </output>
            <output>    <!-- MID kill knob -->
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter2</key>
                <status>0x90</status>
                <midino>0x15</midino>
                <minimum>0.5</minimum>
            </output>
            <!--<output>     HIGH kill knob (control moved to script)
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>button_parameter3</key>
                <status>0x90</status>
                <midino>0x14</midino>
                <minimum>0.5</minimum>
            </output>-->
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <minimum>0.5</minimum>
            </output>

        <!-- Right Deck -->
            <output>    <!-- LOW kill knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter1</key>
                <status>0x90</status>
                <midino>0x52</midino>
                <minimum>0.5</minimum>
            </output>
            <output>    <!-- MID kill knob -->
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter2</key>
                <status>0x90</status>
                <midino>0x51</midino>
                <minimum>0.5</minimum>
            </output>
            <!--<output>     HIGH kill knob (control moved to script)
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>button_parameter3</key>
                <status>0x90</status>
                <midino>0x50</midino>
                <minimum>0.5</minimum>
            </output>-->
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x3E</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x41</midino>
                <minimum>0.5</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x53</midino>
                <minimum>0.5</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
