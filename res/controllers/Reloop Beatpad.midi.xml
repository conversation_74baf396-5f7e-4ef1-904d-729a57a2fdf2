<?xml version="1.0" encoding="utf-8"?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
    <info>
        <name>Reloop Beatpad</name>
        <author><PERSON><PERSON><PERSON> (<PERSON>)</author>
        <description>Controller mapping for the Reloop Beatpad. Have fun !!!</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=7581</forums>
        <manual>reloop_beatpad</manual>
    </info>
    <controller id="Reloop">
        <scriptfiles>
            <file functionprefix="ReloopBeatpad" filename="Reloop-Beatpad-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FilterMid</key>
                <description>FILTER MID 1</description>
                <status>0x90</status>
                <midino>0x75</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FilterMid</key>
                <description>FILTER MID 2 </description>
                <status>0x91</status>
                <midino>0x75</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_gotoandstop</key>
                <description>LEVEL FADER DOWN 1 (fader start)</description>
                <status>0x90</status>
                <midino>0x73</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_gotoandstop</key>
                <description>LEVEL FADER DOWN 2 (fader start)</description>
                <status>0x91</status>
                <midino>0x73</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FXSelectPush</key>
                <description>FX SELECT PUSH 1</description>
                <status>0x90</status>
                <midino>0x72</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FXSelectPush</key>
                <description>FX SELECT PUSH 2</description>
                <status>0x91</status>
                <midino>0x72</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelSeekTouch</key>
                <description>JOG_SEEK_TOUCH 1</description>
                <status>0x90</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelScratchTouch</key>
                <description>JOG_SCRATCH_TOUCH 2</description>
                <status>0x91</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelBendTouch</key>
                <description>JOG_BEND_TOUCH 1</description>
                <status>0x90</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelBendTouch</key>
                <description>JOG_BEND_TOUCH 2</description>
                <status>0x91</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelScratchTouch</key>
                <description>JOG_SCRATCH_TOUCH 1</description>
                <status>0x90</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelSeekTouch</key>
                <description>JOG_SEEK_TOUCH 2</description>
                <status>0x91</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoadBtn</key>
                <description>LOAD 1</description>
                <status>0x90</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoadBtn</key>
                <description>LOAD 2</description>
                <status>0x91</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.pflBtn</key>
                <description>PFL 1</description>
                <status>0x90</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.pflBtn</key>
                <description>PFL 2</description>
                <status>0x91</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.PlayBtn</key>
                <description>PLAY 1</description>
                <status>0x90</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.PlayBtn</key>
                <description>PLAY 2</description>
                <status>0x91</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.JumpBtn</key>
                <description>JUMP 1</description>
                <status>0x90</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.JumpBtn</key>
                <description>JUMP 2</description>
                <status>0x91</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>SET 1</description>
                <status>0x90</status>
                <midino>0x5E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>SET 2</description>
                <status>0x91</status>
                <midino>0x5E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SyncBtn</key>
                <description>SYNC 1</description>
                <status>0x90</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SyncBtn</key>
                <description>SYNC 2</description>
                <status>0x91</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD4 1</description>
                <status>0x90</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD4 2</description>
                <status>0x91</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD3 1</description>
                <status>0x90</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD2 1</description>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD3 2</description>
                <status>0x91</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD2 2</description>
                <status>0x91</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD1 1</description>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerPad</key>
                <description>SAMPLERPAD1 2</description>
                <status>0x91</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD4 1</description>
                <status>0x90</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD4 2</description>
                <status>0x91</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD3 1</description>
                <status>0x90</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD3 2</description>
                <status>0x91</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD2 1</description>
                <status>0x90</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD2 2</description>
                <status>0x91</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD1 1</description>
                <status>0x90</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXPad</key>
                <description>INSTANTFXPAD1 2</description>
                <status>0x91</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD4 1</description>
                <status>0x90</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD4 2</description>
                <status>0x91</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD3 1</description>
                <status>0x90</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD3 2</description>
                <status>0x91</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD2 1</description>
                <status>0x90</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD2 2</description>
                <status>0x91</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD1 1</description>
                <status>0x90</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopPad</key>
                <description>BOUNCELOOPPAD1 2</description>
                <status>0x91</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_activate</key>
                <description>CUEPAD4 1</description>
                <status>0x90</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_activate</key>
                <description>CUEPAD4 2</description>
                <status>0x91</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_activate</key>
                <description>CUEPAD3 1</description>
                <status>0x90</status>
                <midino>0x4E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_activate</key>
                <description>CUEPAD3 2</description>
                <status>0x91</status>
                <midino>0x4E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_activate</key>
                <description>CUEPAD2 1</description>
                <status>0x90</status>
                <midino>0x4D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_activate</key>
                <description>CUEPAD2 2</description>
                <status>0x91</status>
                <midino>0x4D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_activate</key>
                <description>CUEPAD1 1</description>
                <status>0x90</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_activate</key>
                <description>CUEPAD1 2</description>
                <status>0x91</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerBtn</key>
                <description>SAMPLER 1 </description>
                <status>0x90</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerBtn</key>
                <description>SAMPLER 2 </description>
                <status>0x91</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXBtn</key>
                <description>INSTANTFX 1</description>
                <status>0x90</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXBtn</key>
                <description>INSTANTFX 2</description>
                <status>0x91</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.BounceBtn</key>
                <description>BOUNCELOOP 1</description>
                <status>0x90</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.BounceBtn</key>
                <description>BOUNCELOOP 2</description>
                <status>0x91</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.CueBtn</key>
                <description>CUE 1 (Hotcues)</description>
                <status>0x90</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.CueBtn</key>
                <description>CUE 2 (Hotcues)</description>
                <status>0x91</status>
                <midino>0x48</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FX_ONBtn</key>
                <description>FX_ON MODE 1</description>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FX_ONBtn</key>
                <description>FX_ON MODE 2</description>
                <status>0x91</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.JogSeekBtn</key>
                <description>JOG SEEK MODE 1</description>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelSeek</key>
                <description>JOG_SEEK 1</description>
                <status>0xB0</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.JogSeekBtn</key>
                <description>JOG SEEK MODE 2</description>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.JogScratchBtn</key>
                <description>JOG SCRATCH MODE 1</description>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelScratch</key>
                <description>JOG_SCRATCH 2</description>
                <status>0xB1</status>
                <midino>0x65</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelBend</key>
                <description>JOG_BEND 1</description>
                <status>0xB0</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.JogScratchBtn</key>
                <description>JOG SCRATCH MODE 2</description>
                <status>0x91</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopBtn</key>
                <description>LOOP MODE 1</description>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopSizeBtn</key>
                <description>LOOPSIZEPUSH 1</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelBend</key>
                <description>JOG_BEND 2</description>
                <status>0xB1</status>
                <midino>0x64</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelScratch</key>
                <description>JOG_SCRATCH 1</description>
                <status>0xB0</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopBtn</key>
                <description>LOOP MODE 2</description>
                <status>0x91</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopSizeBtn</key>
                <description>LOOPSIZEPUSH 2</description>
                <status>0x91</status>
                <midino>0x43</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_perm_up</key>
                <description>PITCHBEND+ 1</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelSeek</key>
                <description>JOG_SEEK 2</description>
                <status>0xB1</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>LoadSelectedIntoFirstStopped</key>
                <description>TRACK_PUSH</description>
                <status>0x94</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_perm_up</key>
                <description>PITCHBEND+ 2</description>
                <status>0x91</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND- 1</description>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <description>PITCHBEND- 2</description>
                <status>0x91</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Recording]</group>
                <key>ReloopBeatpad.RecBtn</key>
                <description>REC Btn</description>
                <status>0x94</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pitch_set_default</key>
                <description>SHIFT_FILTER_MID 1</description>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pitch_set_default</key>
                <description>SHIFT_FILTER_MID 2</description>
                <status>0x91</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sFXSelectPush</key>
                <description>SHIFT_FXSELECT_PUSH 1</description>
                <status>0x90</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sFXSelectPush</key>
                <description>SHIFT_FXSELECT_PUSH 2</description>
                <status>0x91</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>VOLUME 1 (Level fader)</description>
                <status>0xB0</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>VOLUME 2 (Level fader)</description>
                <status>0xB1</status>
                <midino>0x4F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description>EQ_LOW 1</description>
                <status>0xB0</status>
                <midino>0x4E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description>EQ_LOW 2</description>
                <status>0xB1</status>
                <midino>0x4E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description>EQ_MID 1</description>
                <status>0xB0</status>
                <midino>0x4D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description>EQ_MID 2</description>
                <status>0xB1</status>
                <midino>0x4D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description>EQ_HIGH 1</description>
                <status>0xB0</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description>EQ_HIGH 2</description>
                <status>0xB1</status>
                <midino>0x4C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>GAIN 1</description>
                <status>0xB0</status>
                <midino>0x4B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>GAIN 2</description>
                <status>0xB1</status>
                <midino>0x4B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FXParam</key>
                <description>FX PARAM 1</description>
                <status>0xB0</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FilterKnob</key>
                <description>FILTER 1</description>
                <status>0xB0</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FXParam</key>
                <description>FX PARAM 2</description>
                <status>0xB1</status>
                <midino>0x4A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FilterKnob</key>
                <description>FILTER 2</description>
                <status>0xB1</status>
                <midino>0x49</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelScratchTouch</key>
                <description>JOG_iCut_TOUCH 2</description>
                <status>0x91</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelScratchTouch</key>
                <description>JOG_iCUT_TOUCH 1</description>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FXSelectKnob</key>
                <description>FX SELECT 1</description>
                <status>0xB0</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <description>CROSSFADER</description>
                <status>0xB4</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FXSelectKnob</key>
                <description>FX SELECT 2</description>
                <status>0xB1</status>
                <midino>0x42</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopSizeKnob</key>
                <description>LOOPSIZE 1</description>
                <status>0xB0</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.spflBtn</key>
                <description>SHIFT+PFL 1</description>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopSizeKnob</key>
                <description>LOOPSIZE 2</description>
                <status>0xB1</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.spflBtn</key>
                <description>SHIFT+PFL 2</description>
                <status>0x91</status>
                <midino>0x21</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.ReverseRoll</key>
                <description>CENSOR 1</description>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headGain</key>
                <description>CUE_MIX</description>
                <status>0xB4</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.ReverseRoll</key>
                <description>CENSOR 2</description>
                <status>0x91</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.Brake</key>
                <description>SHIFT_JUMP 1 (BRAKE 1)</description>
                <status>0x90</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>SelectTrackKnob</key>
                <description>BROWSER_ENC</description>
                <status>0xB4</status>
                <midino>0x42</midino>
                <options>
                    <spread64/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.Brake</key>
                <description>SHIFT_JUMP 2 (BRAKE 2)</description>
                <status>0x91</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description>KEYLOCK 1</description>
                <status>0x90</status>
                <midino>0x1E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <description>KEYLOCK 2</description>
                <status>0x91</status>
                <midino>0x1E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.StartBtn</key>
                <description>SHIFT_SYNC 1</description>
                <status>0x90</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.StartBtn</key>
                <description>SHIFT_SYNC 2</description>
                <status>0x91</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>(Channel1]</group>
                <key>ReloopBeatpad.ShiftBtn</key>
                <description>L_SHIFT</description>
                <status>0x90</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>(Channel2]</group>
                <key>ReloopBeatpad.ShiftBtn</key>
                <description>R_SHIFT</description>
                <status>0x91</status>
                <midino>0x1C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD4 1</description>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD4 2</description>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD3 1</description>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD3 2</description>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD2 1</description>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD1 1</description>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD2 2</description>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sSamplerPad</key>
                <description>SHIFT_SAMPLERPAD1 2</description>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD4 1</description>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD4 2</description>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD3 1</description>
                <status>0x90</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD3 2</description>
                <status>0x91</status>
                <midino>0x16</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD2 1</description>
                <status>0x90</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD2 2</description>
                <status>0x91</status>
                <midino>0x15</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD1 1</description>
                <status>0x90</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sInstantFXPad</key>
                <description>SHIFT_INSTANTFXPAD1 2</description>
                <status>0x91</status>
                <midino>0x14</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD4 1</description>
                <status>0x90</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD4 2</description>
                <status>0x91</status>
                <midino>0x13</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD3 1</description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD3 2</description>
                <status>0x91</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD2 1</description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD2 2</description>
                <status>0x91</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD1 1</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sLoopPad</key>
                <description>SHIFT_BOUNCELOOPPAD1 2</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT_CUEPAD4 1</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_4_clear</key>
                <description>SHIFT_CUEPAD4 2</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT_CUEPAD3 1</description>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_3_clear</key>
                <description>SHIFT_CUEPAD3 2</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT_CUEPAD2 1</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_2_clear</key>
                <description>SHIFT_CUEPAD2 2</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT_CUEPAD1 1</description>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>hotcue_1_clear</key>
                <description>SHIFT_CUEPAD1 2</description>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.SamplerBtn</key>
                <description>SHIFT_SAMPLER 1 </description>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.SamplerBtn</key>
                <description>SHIFT_SAMPLER 2 </description>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.InstantFXBtnk</key>
                <description>SHIFT_INSTANTFX 1</description>
                <status>0x90</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.InstantFXBtn</key>
                <description>SHIFT_INSTANTFX 2</description>
                <status>0x91</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.BounceBtn</key>
                <description>SHIFT_BOUNCELOOP 1 </description>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.BounceBtn</key>
                <description>SHIFT_BOUNCELOOP 2 </description>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.CueBtn</key>
                <description>SHIFT_CUE 1 (Hotcues)</description>
                <status>0x90</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.CueBtn</key>
                <description>SHIFT_CUE 2 (Hotcues)</description>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.WheelScratch</key>
                <description>JOG_iCUT 2</description>
                <status>0xB1</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.WheelScratch</key>
                <description>JOG_iCUT 1</description>
                <status>0xB0</status>
                <midino>0x23</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sLoopSizeBtn</key>
                <description>SHIFT_LOOPSIZEPUSH 1</description>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatjump_1_forward</key>
                <description>SHIFT_PITCHBEND+ 1</description>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ToggleSelectedSidebarItem</key>
                <description>SHIFT_TRACK_PUSH</description>
                <status>0x94</status>
                <midino>0x06</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sLoopSizeBtn</key>
                <description>SHIFT_LOOPSIZEPUSH 2</description>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatjump_1_forward</key>
                <description>SHIFT_PITCHBEND+ 2</description>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beatjump_1_backward</key>
                <description>SHIFT_PITCHBEND- 1</description>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beatjump_1_backward</key>
                <description>SHIFT_PITCHBEND- 2</description>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Recording]</group>
                <key>ReloopBeatpad.sRecBtn</key>
                <description>REC Btn</description>
                <status>0x94</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>volume</key>
                <description>SHIFT_VOLUME 1 (Level fader)</description>
                <status>0xB0</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>volume</key>
                <description>SHIFT_VOLUME 2 (Level fader)</description>
                <status>0xB1</status>
                <midino>0x0F</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description>SHIFT_EQ_LOW 1</description>
                <status>0xB0</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description>SHIFT_EQ_LOW 2</description>
                <status>0xB1</status>
                <midino>0x0E</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description>SHIFT_EQ_MID 1</description>
                <status>0xB0</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description>SHIFT_EQ_MID 2</description>
                <status>0xB1</status>
                <midino>0x0D</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description>SHIFT_EQ_HIGH 1</description>
                <status>0xB0</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description>SHIFT_EQ_HIGH 2</description>
                <status>0xB1</status>
                <midino>0x0C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description>SHIFT_GAIN 1</description>
                <status>0xB0</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description>SHIFT_GAIN 2</description>
                <status>0xB1</status>
                <midino>0x0B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.FXParamShift</key>
                <description>SHIFT_FXPARAM 1</description>
                <status>0xB0</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.FXParamShift</key>
                <description>SHIFT_FXPARAM 2</description>
                <status>0xB1</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pitch</key>
                <description>SHIFT_FILTER 1</description>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pitch</key>
                <description>SHIFT_FILTER 2</description>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.sFXSelectKnob</key>
                <description>SHIFT_FXSELECT 1</description>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.sFXSelectKnob</key>
                <description>SHIFT_FXSELECT 2</description>
                <status>0xB1</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>ReloopBeatpad.LoopSizeKnob</key>
                <description>SHIFT_LOOPSIZE 1</description>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>ReloopBeatpad.LoopSizeKnob</key>
                <description>SHIFT_LOOPSIZE 2</description>
                <status>0xB1</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headGain</key>
                <description>SHIFT_CUE_MIX</description>
                <status>0xB4</status>
                <midino>0x03</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>ReloopBeatpad.SelectPlayList</key>
                <description>SHIFT_BROWSER_ENC</description>
                <status>0xB4</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <description>PITCH 1</description>
                <status>0xE0</status>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <description>PITCH 2</description>
                <status>0xE1</status>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>ReloopBeatpad.InboundSysex</key>
                <description>SYSEX</description>
                <status>0xF0</status>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
