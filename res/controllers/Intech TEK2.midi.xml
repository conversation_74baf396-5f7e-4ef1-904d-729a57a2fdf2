<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.3.0" schemaVersion="1">
    <info>
        <name>Intech TEK2</name>
    </info>
    <controller id="TEK2">
        <scriptfiles>
            <file functionprefix="" filename="Intech TEK2.scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Channel1]</group>
                <key>TEK2.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>TEK2.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>TEK2.wheelTouch</key>
                <status>0x90</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>TEK2.wheelTouch</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x20</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x21</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description>MIDI Learned from 2 messages.</description>
                <status>0x90</status>
                <midino>0x23</midino>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
