<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset schemaVersion="1" mixxxVersion="2.3.0+">
  <info>
    <name>Numark Mixtrack Pro FX</name>
    <author>h67ma, bad1dea5, photoenix</author>
    <description>Mapping for the Numark Mixtrack Pro FX</description>
    <forums>https://mixxx.discourse.group/t/numark-mixtrack-pro-fx/19561</forums>
    <manual>numark_mixtrack_pro_fx</manual>
  </info>
  <controller id="MixTrack">
    <scriptfiles>
      <file functionprefix="" filename="lodash.mixxx.js"/>
      <file functionprefix="" filename="midi-components-0.0.js"/>
      <file functionprefix="MixtrackProFX" filename="Numark-Mixtrack-Pro-FX-scripts.js"/>
    </scriptfiles>
    <controls>
      <!-- Main gain -->
      <control>
        <group>[Master]</group>
        <key>MixtrackProFX.gains.mainGain.input</key>
        <status>0xBE</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Crossfader and 1st deck "fader cuts" -->
      <!-- "fader cuts" moves the crossfader rapidly, sending the same midi codes as crossfader -->
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xBF</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <!-- 2nd deck "fader cuts" -->
      <!-- "fader cuts" on 2nd deck send different midi codes than on 1st deck -->
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB1</status>
        <midino>0x08</midino>
        <options>
          <invert/>
        </options>
      </control>
      <!-- Gain -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].gain.input</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].gain.input</key>
        <status>0xB1</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- EQ -->
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>MixtrackProFX.deck[0].treble.input</key>
        <status>0xB0</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>MixtrackProFX.deck[1].treble.input</key>
        <status>0xB1</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>MixtrackProFX.deck[0].mid.input</key>
        <status>0xB0</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>MixtrackProFX.deck[1].mid.input</key>
        <status>0xB1</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel1]_Effect1]</group>
        <key>MixtrackProFX.deck[0].bass.input</key>
        <status>0xB0</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EqualizerRack1_[Channel2]_Effect1]</group>
        <key>MixtrackProFX.deck[1].bass.input</key>
        <status>0xB1</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Filter -->
      <control>
        <group>[QuickEffectRack1_[Channel1]]</group>
        <key>MixtrackProFX.deck[0].filter.input</key>
        <status>0xB0</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]]</group>
        <key>MixtrackProFX.deck[1].filter.input</key>
        <status>0xB1</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Channel volume -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].volume.input</key>
        <status>0xB0</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].volume.input</key>
        <status>0xB1</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitch.inputMSB</key>
        <status>0xB0</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitch.inputMSB</key>
        <status>0xB1</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitch.inputLSB</key>
        <status>0xB0</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitch.inputLSB</key>
        <status>0xB1</status>
        <midino>0x29</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sync (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].syncButton.input</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].syncButton.input</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sync (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].syncButton.input</key>
        <status>0x80</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].syncButton.input</key>
        <status>0x81</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sync (shift press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].syncButton.input</key>
        <status>0x90</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].syncButton.input</key>
        <status>0x91</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sync (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].syncButton.input</key>
        <status>0x80</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].syncButton.input</key>
        <status>0x81</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- CUE buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].cueButton.input</key>
        <status>0x90</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].cueButton.input</key>
        <status>0x91</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- CUE buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].cueButton.input</key>
        <status>0x80</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].cueButton.input</key>
        <status>0x81</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- CUE buttons (shift press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].cueButton.input</key>
        <status>0x90</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].cueButton.input</key>
        <status>0x91</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- CUE buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].cueButton.input</key>
        <status>0x80</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].cueButton.input</key>
        <status>0x81</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Play buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].playButton.input</key>
        <status>0x90</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].playButton.input</key>
        <status>0x91</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Play buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].playButton.input</key>
        <status>0x80</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].playButton.input</key>
        <status>0x81</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Play buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].playButton.input</key>
        <status>0x90</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].playButton.input</key>
        <status>0x91</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Play buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].playButton.input</key>
        <status>0x80</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].playButton.input</key>
        <status>0x81</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Shift buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].shiftButton.input</key>
        <status>0x90</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].shiftButton.input</key>
        <status>0x91</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Shift buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].shiftButton.input</key>
        <status>0x80</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].shiftButton.input</key>
        <status>0x81</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Scratch toggle buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].scratchToggle.input</key>
        <status>0x90</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].scratchToggle.input</key>
        <status>0x91</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Scratch toggle buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].scratchToggle.input</key>
        <status>0x80</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].scratchToggle.input</key>
        <status>0x81</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Bleep buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].scratchToggle.input</key>
        <status>0x90</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].scratchToggle.input</key>
        <status>0x91</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Bleep buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].scratchToggle.input</key>
        <status>0x80</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].scratchToggle.input</key>
        <status>0x81</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pad modes (press) -->
      <!-- Hotcue -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Autoloop -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Fader Cuts -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sample 1 -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pad modes (release) -->
      <!-- Hotcue -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Autoloop -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x0D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Fader Cuts -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sample 1 -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pad modes (shift) -->
      <!-- Beatjump -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sample 2 -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x94</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x95</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pad modes (shift release) -->
      <!-- Beatjump -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Sample 2 -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.modeButtonPress</key>
        <status>0x84</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.modeButtonPress</key>
        <status>0x85</status>
        <midino>0x0F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pads (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x15</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x15</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pads (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x15</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x15</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x17</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x18</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x19</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pads (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x94</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x95</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pads (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1D</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x1F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x20</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].padSection.padPress</key>
        <status>0x84</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].padSection.padPress</key>
        <status>0x85</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Loop buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loop.input</key>
        <status>0x94</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loop.input</key>
        <status>0x95</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Loop buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loop.input</key>
        <status>0x84</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loop.input</key>
        <status>0x85</status>
        <midino>0x40</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Loop buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loop.input</key>
        <status>0x94</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loop.input</key>
        <status>0x95</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Loop buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loop.input</key>
        <status>0x84</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loop.input</key>
        <status>0x85</status>
        <midino>0x41</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- 1/2 loop buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopHalf.input</key>
        <status>0x94</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopHalf.input</key>
        <status>0x95</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- 1/2 loop buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopHalf.input</key>
        <status>0x84</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopHalf.input</key>
        <status>0x85</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- 1/2 loop buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopHalf.input</key>
        <status>0x94</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopHalf.input</key>
        <status>0x95</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- 1/2 loop buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopHalf.input</key>
        <status>0x84</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopHalf.input</key>
        <status>0x85</status>
        <midino>0x36</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- x2 loop buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopDouble.input</key>
        <status>0x94</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopDouble.input</key>
        <status>0x95</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- x2 loop buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopDouble.input</key>
        <status>0x84</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopDouble.input</key>
        <status>0x85</status>
        <midino>0x35</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- x2 loop buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopDouble.input</key>
        <status>0x94</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopDouble.input</key>
        <status>0x95</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- x2 loop buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loopDouble.input</key>
        <status>0x84</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loopDouble.input</key>
        <status>0x85</status>
        <midino>0x37</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Jogwheels -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.wheelTurn</key>
        <status>0xB0</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.wheelTurn</key>
        <status>0xB1</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Jogwheels touch -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.wheelTouch</key>
        <status>0x90</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.wheelTouch</key>
        <status>0x91</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob turn -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knob.input</key>
        <status>0xBF</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob turn (shift) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knob.input</key>
        <status>0xBF</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob button (press) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knobButton.input</key>
        <status>0x9F</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob button (release) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knobButton.input</key>
        <status>0x8F</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob button (shift) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knobButton.input</key>
        <status>0x9F</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Library browse knob button (shift release) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.browse.knobButton.input</key>
        <status>0x8F</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Load buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loadButton.input</key>
        <status>0x9F</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loadButton.input</key>
        <status>0x9F</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Load buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].loadButton.input</key>
        <status>0x8F</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].loadButton.input</key>
        <status>0x8F</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX dry/wet knob -->
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>MixtrackProFX.effect[0].dryWetKnob.input</key>
        <status>0xB8</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>MixtrackProFX.effect[1].dryWetKnob.input</key>
        <status>0xB8</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX param knob -->
      <control>
        <group>[EffectRack1_EffectUnit1]</group>
        <key>MixtrackProFX.effect[0].effectParam.input</key>
        <status>0xB8</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2]</group>
        <key>MixtrackProFX.effect[1].effectParam.input</key>
        <status>0xB8</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Tap button (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].tap.input</key>
        <status>0x98</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].tap.input</key>
        <status>0x99</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Tap button (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].tap.input</key>
        <status>0x88</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].tap.input</key>
        <status>0x89</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX HPF button (press) -->
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>MixtrackProFX.effect[0].prevEffect.input</key>
        <status>0x98</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX HPF button (release) -->
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>MixtrackProFX.effect[0].prevEffect.input</key>
        <status>0x88</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX LPF button (press) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.deck[0].setBeatgrid.input</key>
        <status>0x98</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX LPF button (release) -->
      <control>
        <group>[Library]</group>
        <key>MixtrackProFX.deck[0].setBeatgrid.input</key>
        <status>0x88</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX flanger button (press) -->
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>MixtrackProFX.effect[1].prevEffect.input</key>
        <status>0x98</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX flanger button (release) -->
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>MixtrackProFX.effect[1].prevEffect.input</key>
        <status>0x88</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX echo button (press) -->
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>MixtrackProFX.effect[0].nextEffect.input</key>
        <status>0x99</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX echo button (release) -->
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>MixtrackProFX.effect[0].nextEffect.input</key>
        <status>0x89</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX reverb button (press) -->
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].setBeatgrid.input</key>
        <status>0x99</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX reverb button (release) -->
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].setBeatgrid.input</key>
        <status>0x89</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX phaser button (press) -->
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>MixtrackProFX.effect[1].nextEffect.input</key>
        <status>0x99</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX phaser button (release) -->
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>MixtrackProFX.effect[1].nextEffect.input</key>
        <status>0x89</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- FX enable switch -->
      <control>
        <group>[EffectRack1_EffectUnit1_Effect1]</group>
        <key>MixtrackProFX.effect[0].enableSwitch.input</key>
        <status>0xB8</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1_EffectUnit2_Effect1]</group>
        <key>MixtrackProFX.effect[1].enableSwitch.input</key>
        <status>0xB9</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- PFL buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pflButton.input</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pflButton.input</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- PFL buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pflButton.input</key>
        <status>0x80</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pflButton.input</key>
        <status>0x81</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Cue gain knob -->
      <control>
        <group>[Master]</group>
        <key>MixtrackProFX.gains.cueGain.input</key>
        <status>0xBE</status>
        <midino>0x2F</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Cue mix knob -->
      <control>
        <group>[Master]</group>
        <key>MixtrackProFX.gains.cueMix.input</key>
        <status>0xBE</status>
        <midino>0x27</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend down buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendDown.input</key>
        <status>0x90</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendDown.input</key>
        <status>0x91</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend down buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendDown.input</key>
        <status>0x80</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendDown.input</key>
        <status>0x81</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend down buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendDown.input</key>
        <status>0x90</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendDown.input</key>
        <status>0x91</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend down buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendDown.input</key>
        <status>0x80</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendDown.input</key>
        <status>0x81</status>
        <midino>0x2C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend up buttons (press) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendUp.input</key>
        <status>0x90</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendUp.input</key>
        <status>0x91</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend up buttons (release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendUp.input</key>
        <status>0x80</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendUp.input</key>
        <status>0x81</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend up buttons (shift) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendUp.input</key>
        <status>0x90</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendUp.input</key>
        <status>0x91</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Pitch bend up buttons (shift release) -->
      <control>
        <group>[Channel1]</group>
        <key>MixtrackProFX.deck[0].pitchBendUp.input</key>
        <status>0x80</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>MixtrackProFX.deck[1].pitchBendUp.input</key>
        <status>0x81</status>
        <midino>0x2B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
    </controls>
  </controller>
</MixxxControllerPreset>
