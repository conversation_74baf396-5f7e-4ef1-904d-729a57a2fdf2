<MixxxMIDIPreset mixxxVersion="1.10.1+" schemaVersion="1">
    <info>
        <name>Pioneer CDJ-350 CH2</name>
        <author><PERSON><PERSON><PERSON> and <PERSON></author>
        <description>This is an incomplete mapping for a single Pioneer CDJ 350.</description>
        <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=1917</forums>
        <manual>pioneer_cdj_350</manual>
    </info>
    <controller id="USB Audio Device">
        <scriptfiles>
            <file functionprefix="PioneerCDJ350" filename="Pioneer-CDJ-350-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.jog_wheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x26</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>end</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>start</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1d</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <description></description>
                <options>
                    <invert/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2e</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.toggle_playlist</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x26</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x32</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.exit_playlist</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x33</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.select_push</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.tempo_btn</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x7</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x8</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4f</midino>
                <group>[Playlist]</group>
                <key>PioneerCDJ350.select_turn</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel1]</group>
                <key>loop_double</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel1]</group>
                <key>loop_halve</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x30</midino>
                <group>[Channel1]</group>
                <key>PioneerCDJ350.jog_wheel</key>
                <description></description>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>keylock</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxMIDIPreset>
