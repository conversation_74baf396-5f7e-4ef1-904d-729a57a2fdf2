<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="1.6.2+">
  <info>
    <name>Mixman DM2 (Windows)</name>
    <author><PERSON>.</author>
    <description>MIDI Mapping for Mixman DM2 (Windows)</description>
    <manual>mixman_dm2</manual>
  </info>
  <controller id="Mixman DM2 (Windows)" port="Port">
    <controls>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xB0</status>
        <midino>0x02</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x24</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x26</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x2b</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x2c</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down_small</key>
        <status>0x90</status>
        <midino>0x35</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_down_small</key>
        <status>0x80</status>
        <midino>0x35</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up_small</key>
        <status>0x90</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>rate_perm_up_small</key>
        <status>0x80</status>
        <midino>0x36</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x2f</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x30</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x30</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x32</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x34</midino>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>wheel</key>
        <status>0xB0</status>
        <midino>0x01</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x80</status>
        <midino>0x3c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x90</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>reverse</key>
        <status>0x80</status>
        <midino>0x3e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x90</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down</key>
        <status>0x80</status>
        <midino>0x43</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x90</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up</key>
        <status>0x80</status>
        <midino>0x44</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down_small</key>
        <status>0x90</status>
        <midino>0x4d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_down_small</key>
        <status>0x80</status>
        <midino>0x4d</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up_small</key>
        <status>0x90</status>
        <midino>0x4e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>rate_perm_up_small</key>
        <status>0x80</status>
        <midino>0x4e</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x90</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_set</key>
        <status>0x80</status>
        <midino>0x47</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x90</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_preview</key>
        <status>0x80</status>
        <midino>0x48</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x90</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_goto</key>
        <status>0x80</status>
        <midino>0x4a</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x90</status>
        <midino>0x4c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>flanger</key>
        <status>0x80</status>
        <midino>0x4c</midino>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>wheel</key>
        <status>0xB0</status>
        <midino>0x03</midino>
      </control>
    </controls>
  </controller>
</MixxxMIDIPreset>
