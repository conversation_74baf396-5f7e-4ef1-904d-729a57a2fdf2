<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.0.0+" schemaVersion="1">
    <info>
        <name>Reloop Jockey 3 ME</name>
        <author>Chris2000SP</author>
        <description>v1.0.3 Mapping for 2.0.x</description>
        <forums>https://mixxx.discourse.group/t/create-a-mapping-for-reloop-jockey-3-me/13703</forums>
        <manual>reloop_jockey_3_master_edition</manual>
    </info>
    <controller id="Reloop">
        <scriptfiles>
            <file functionprefix="Jockey3ME" filename="Reloop-Jockey-3-ME-scripts.js"/>
        </scriptfiles>
        <controls>

            <!-- Master Section -->
            <control>
                <group>[Master]</group>
                <key>Jockey3ME.crossfaderCurve</key>
                <status>0xB0</status>
                <midino>0x38</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>Jockey3ME.crossfader</key>
                <status>0xB0</status>
                <midino>0x37</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Jockey3ME.TraxEncoderTurn</key>
                <status>0xB0</status>
                <midino>0x35</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>Jockey3ME.ShiftTraxEncoderTurn</key>
                <status>0xB0</status>
                <midino>0x74</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Playlist]</group>
				<key>ToggleSelectedSidebarItem</key>
				<status>0x90</status>
				<midino>0x74</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[Master]</group>
                <key>headMix</key>
                <status>0xB0</status>
                <midino>0x36</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>balance</key>
                <status>0xB0</status>
                <midino>0x33</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <description>TraxPush to Browse</description>
                <status>0x90</status>
                <midino>0x35</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[Microphone]</group>
				<key>talkover</key>
				<description>Shift Sync</description>
				<status>0x90</status>
				<midino>0x56</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Microphone]</group>
				<key>talkover</key>
				<description>Shift Sync</description>
				<status>0x91</status>
				<midino>0x56</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Microphone]</group>
				<key>talkover</key>
				<description>Shift Sync</description>
				<status>0x92</status>
				<midino>0x56</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Microphone]</group>
				<key>talkover</key>
				<description>Shift Sync</description>
				<status>0x93</status>
				<midino>0x56</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Microphone]</group>
				<key>pregain</key>
				<description>Shift Filter Controls Microphone Gain</description>
				<status>0xB0</status>
				<midino>0x71</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[PreviewDeck1]</group>
				<key>LoadSelectedTrackAndPlay</key>
				<description>Load And Play on Preview</description>
				<status>0x90</status>
				<midino>0x5B</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[PreviewDeck1]</group>
				<key>stop</key>
				<description>Stop Preview</description>
				<status>0x91</status>
				<midino>0x5B</midino>
				<options>
					<normal/>
				</options>
			</control>
            <!-- Effect Chain 1 -->
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>Jockey3ME.effectSelect</key>
                <description>FX Selection</description>
                <status>0xB0</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB0</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB0</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB0</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x90</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x90</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x90</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1]</group>
				<key>Jockey3ME.effectSuperKnob</key>
				<description>FX SuperKnob on Jogwheel Mode Pitchbend</description>
				<status>0xB0</status>
				<midino>0x25</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 3</description>
                <status>0xB0</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 2</description>
                <status>0xB0</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 1</description>
                <status>0xB0</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>Jockey3ME.effectDryWet</key>
                <description>FX Param Mix</description>
                <status>0xB0</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x02</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x03</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x04</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Button Enable FX to Deck A</description>
                <status>0x90</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Button Enable FX to Deck B</description>
                <status>0x90</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Button Enable FX to Deck C</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Button Enable FX to Deck D</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Effect Chain 2 -->
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>Jockey3ME.effectSelect</key>
                <description>FX Selection</description>
                <status>0xB1</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB1</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB1</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB1</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x91</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x91</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x91</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2]</group>
				<key>Jockey3ME.effectSuperKnob</key>
				<description>FX SuperKnob on Jogwheel Mode Pitchbend</description>
				<status>0xB1</status>
				<midino>0x25</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 3</description>
                <status>0xB1</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 2</description>
                <status>0xB1</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 1</description>
                <status>0xB1</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>Jockey3ME.effectDryWet</key>
                <description>FX Param Mix</description>
                <status>0xB1</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>enabled</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x02</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x03</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x04</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Button Enable FX to Deck A</description>
                <status>0x91</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Button Enable FX to Deck B</description>
                <status>0x91</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Button Enable FX to Deck C</description>
                <status>0x91</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Button Enable FX to Deck D</description>
                <status>0x91</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Effect Chain 3 -->
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>Jockey3ME.effectSelect</key>
                <description>FX Selection</description>
                <status>0xB2</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB2</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB2</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB2</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x92</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x92</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x92</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3]</group>
				<key>Jockey3ME.effectSuperKnob</key>
				<description>FX SuperKnob on Jogwheel Mode Pitchbend</description>
				<status>0xB2</status>
				<midino>0x25</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 3</description>
                <status>0xB2</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 2</description>
                <status>0xB2</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 1</description>
                <status>0xB2</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>Jockey3ME.effectDryWet</key>
                <description>FX Param Mix</description>
                <status>0xB2</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>enabled</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x02</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x03</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x04</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Button Enable FX to Deck A</description>
                <status>0x92</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Button Enable FX to Deck B</description>
                <status>0x92</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Button Enable FX to Deck C</description>
                <status>0x92</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Button Enable FX to Deck D</description>
                <status>0x92</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Effect Chain 4 -->
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>Jockey3ME.effectSelect</key>
                <description>FX Selection</description>
                <status>0xB3</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB3</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB3</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelect</key>
				<description>FX Selection in Chain and Linking</description>
				<status>0xB3</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x93</status>
				<midino>0x5D</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x93</status>
				<midino>0x5E</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSelectPush</key>
				<description>FX Sel.X Push Linking to SuperKnob on Jogmode Pitchbend</description>
				<status>0x93</status>
				<midino>0x5F</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4]</group>
				<key>Jockey3ME.effectSuperKnob</key>
				<description>FX SuperKnob on Jogwheel Mode Pitchbend</description>
				<status>0xB3</status>
				<midino>0x25</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 3</description>
                <status>0xB3</status>
                <midino>0x20</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 2</description>
                <status>0xB3</status>
                <midino>0x1F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>Jockey3ME.effectParam</key>
                <description>FX Param 1</description>
                <status>0xB3</status>
                <midino>0x1E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>Jockey3ME.effectDryWet</key>
                <description>FX Param Mix</description>
                <status>0xB3</status>
                <midino>0x1D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>enabled</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x02</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x03</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x04</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Button Enable FX to Deck A</description>
                <status>0x93</status>
                <midino>0x40</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Button Enable FX to Deck B</description>
                <status>0x93</status>
                <midino>0x41</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Button Enable FX to Deck C</description>
                <status>0x93</status>
                <midino>0x42</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Button Enable FX to Deck D</description>
                <status>0x93</status>
                <midino>0x43</midino>
                <options>
                    <normal/>
                </options>
            </control>

            <!-- [Channel1] Deck A -->
            <!-- Transport Section -->
            <control>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>play</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>cue_gotoandplay</key>
                <status>0x90</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>LoadSelectedTrack</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>quantize</key>
                <description>Shift CUE Sets Quantize</description>
                <status>0x90</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beats_translate_curpos</key>
                <description>Shift Minus Moves Beatgrid line to PlayPos</description>
                <status>0x90</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hotcue Section -->
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 1</description>
                <status>0x90</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 2</description>
                <status>0x90</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 3</description>
                <status>0x90</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 4</description>
                <status>0x90</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 5</description>
                <status>0x90</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 6</description>
                <status>0x90</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 7</description>
                <status>0x90</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 8</description>
                <status>0x90</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.hotcue_clear</key>
                <description>HotCue Delete</description>
                <status>0x90</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Loop Section -->
            <control>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[Channel1]</group>
				<key>repeat</key>
				<status>0x90</status>
				<midino>0x06</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_1_forward</key>
				<status>0x90</status>
				<midino>0x07</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>beatjump_1_backward</key>
				<status>0x90</status>
				<midino>0x08</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[Channel1]</group>
                <key>beatloop_4_activate</key>
                <description>Push Length to enable 4 Beatloop</description>
                <status>0x90</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.loop_double_halve</key>
                <description>If Length Pushed, Turn to size the Loop</description>
                <status>0xB0</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0xB0</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0x90</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beats_adjust_faster</key>
                <status>0x90</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>beats_adjust_slower</key>
                <status>0x90</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel1]]</group>
                <key>super1</key>
                <status>0xB0</status>
                <midino>0x32</midino>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <!-- Pitch and Jog -->
            <control>
                <group>[Channel1]</group>
                <key>rate</key>
                <status>0xE0</status>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <status>0x90</status>
                <midino>0x2A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <status>0x90</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.wheelTurn</key>
                <status>0xB0</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel1]</group>
				<key>Jockey3ME.wheelTurn</key>
				<description>Default Jog is Pitchbend</description>
				<status>0xB0</status>
				<midino>0x28</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.wheelTouch</key>
                <status>0x90</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel1]</group>
				<key>Jockey3ME.wheelTouch</key>
				<description>Track Search Stop</description>
				<status>0x90</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel1]</group>
				<key>Jockey3ME.trackSearch</key>
				<status>0xB0</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <!-- Mixer Section -->
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.DeckSwitch</key>
                <status>0x90</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.MixerVol</key>
                <description>Pregain</description>
                <status>0xB0</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>High EQ</description>
                <status>0xB0</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>Mid EQ</description>
                <status>0xB0</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel1]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>Low EQ</description>
                <status>0xB0</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>Jockey3ME.MixerVol</key>
                <description>Volume Fader</description>
                <status>0xB0</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- [Channel2] Deck B -->
            <!-- Transport Section -->
            <control>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <status>0x91</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>play</key>
                <status>0x91</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>cue_gotoandplay</key>
                <status>0x91</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>LoadSelectedTrack</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x91</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x59</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>quantize</key>
                <description>Shift CUE Sets Quantize</description>
                <status>0x91</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beats_translate_curpos</key>
                <description>Shift Minus Moves Beatgrid line to PlayPos</description>
                <status>0x91</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hotcue Section -->
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 1</description>
                <status>0x91</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 2</description>
                <status>0x91</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 3</description>
                <status>0x91</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 4</description>
                <status>0x91</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 5</description>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 6</description>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 7</description>
                <status>0x91</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 8</description>
                <status>0x91</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.hotcue_clear</key>
                <description>HotCue Delete</description>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Loop Section -->
            <control>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <status>0x91</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <status>0x91</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[Channel2]</group>
				<key>repeat</key>
				<status>0x91</status>
				<midino>0x06</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_1_forward</key>
				<status>0x91</status>
				<midino>0x07</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>beatjump_1_backward</key>
				<status>0x91</status>
				<midino>0x08</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[Channel2]</group>
                <key>beatloop_4_activate</key>
                <description>Push Length to enable 4 Beatloop</description>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.loop_double_halve</key>
                <description>If Length Pushed, Turn to size the Loop</description>
                <status>0xB1</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0xB1</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beats_adjust_faster</key>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>beats_adjust_slower</key>
                <status>0x91</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel2]]</group>
                <key>super1</key>
                <status>0xB1</status>
                <midino>0x32</midino>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <!-- Pitch and Jog -->
            <control>
                <group>[Channel2]</group>
                <key>rate</key>
                <status>0xE1</status>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <status>0x91</status>
                <midino>0x2A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <status>0x91</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.wheelTurn</key>
                <status>0xB1</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel2]</group>
				<key>Jockey3ME.wheelTurn</key>
				<description>Default Jog is Pitchbend</description>
				<status>0xB1</status>
				<midino>0x28</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.wheelTouch</key>
                <status>0x91</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel2]</group>
				<key>Jockey3ME.wheelTouch</key>
				<description>Track Search Stop</description>
				<status>0x91</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel2]</group>
				<key>Jockey3ME.trackSearch</key>
				<status>0xB1</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <!-- Mixer Section -->
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.DeckSwitch</key>
                <status>0x90</status>
                <midino>0x3F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.MixerVol</key>
                <status>0xB0</status>
                <midino>0x6C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>EQ High</description>
                <status>0xB0</status>
                <midino>0x6D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>EQ Mid</description>
                <status>0xB0</status>
                <midino>0x6E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[EqualizerRack1_[Channel2]_Effect1]</group>
                <key>Jockey3ME.EQ</key>
                <description>EQ Low</description>
                <status>0xB0</status>
                <midino>0x6F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>Jockey3ME.MixerVol</key>
                <status>0xB0</status>
                <midino>0x70</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- [Channel3] Deck C -->
            <!-- Transport Section -->
            <control>
                <group>[Channel3]</group>
                <key>cue_default</key>
                <status>0x92</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>play</key>
                <status>0x92</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>cue_gotoandplay</key>
                <status>0x92</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>LoadSelectedTrack</key>
                <status>0x92</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>sync_enabled</key>
                <status>0x92</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x92</status>
                <midino>0x59</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>quantize</key>
                <description>Shift CUE Sets Quantize</description>
                <status>0x92</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beats_translate_curpos</key>
                <description>Shift Minus Moves Beatgrid line to PlayPos</description>
                <status>0x92</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hotcue Section -->
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 1</description>
                <status>0x92</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 2</description>
                <status>0x92</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 3</description>
                <status>0x92</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 4</description>
                <status>0x92</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 5</description>
                <status>0x92</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 6</description>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 7</description>
                <status>0x92</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 8</description>
                <status>0x92</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.hotcue_clear</key>
                <description>HotCue Delete</description>
                <status>0x92</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Loop Section -->
            <control>
                <group>[Channel3]</group>
                <key>loop_in</key>
                <status>0x92</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>loop_out</key>
                <status>0x92</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>reloop_exit</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[Channel3]</group>
				<key>repeat</key>
				<status>0x92</status>
				<midino>0x06</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel3]</group>
				<key>beatjump_1_forward</key>
				<status>0x92</status>
				<midino>0x07</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel3]</group>
				<key>beatjump_1_backward</key>
				<status>0x92</status>
				<midino>0x08</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[Channel3]</group>
                <key>beatloop_4_activate</key>
                <description>Push Length to enable 4 Beatloop</description>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.loop_double_halve</key>
                <description>If Length Pushed, Turn to size the Loop</description>
                <status>0xB2</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0xB2</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beats_adjust_faster</key>
                <status>0x92</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>beats_adjust_slower</key>
                <status>0x92</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel3]]</group>
                <key>super1</key>
                <status>0xB2</status>
                <midino>0x32</midino>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <!-- Pitch and Jog -->
            <control>
                <group>[Channel3]</group>
                <key>rate</key>
                <status>0xE2</status>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_up</key>
                <status>0x92</status>
                <midino>0x2A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>rate_temp_down</key>
                <status>0x92</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.wheelTurn</key>
                <status>0xB2</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel3]</group>
				<key>Jockey3ME.wheelTurn</key>
				<description>Default Jog is Pitchbend</description>
				<status>0xB2</status>
				<midino>0x28</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[Channel3]</group>
                <key>Jockey3ME.wheelTouch</key>
                <status>0x92</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel3]</group>
				<key>Jockey3ME.wheelTouch</key>
				<description>Track Search Stop</description>
				<status>0x91</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel3]</group>
				<key>Jockey3ME.trackSearch</key>
				<status>0xB1</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <!-- Mixer Section -->
            <control>
                <group>[Channel3]</group>
                <key>pfl</key>
                <status>0x92</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- [Channel4] Deck D -->
            <!-- Transport Section -->
            <control>
                <group>[Channel4]</group>
                <key>cue_default</key>
                <status>0x93</status>
                <midino>0x19</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>play</key>
                <status>0x93</status>
                <midino>0x1A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>cue_gotoandplay</key>
                <status>0x93</status>
                <midino>0x18</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>LoadSelectedTrack</key>
                <status>0x93</status>
                <midino>0x1B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>sync_enabled</key>
                <status>0x93</status>
                <midino>0x17</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x93</status>
                <midino>0x59</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>quantize</key>
                <description>Shift CUE Sets Quantize</description>
                <status>0x93</status>
                <midino>0x58</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beats_translate_curpos</key>
                <description>Shift Minus Moves Beatgrid line to PlayPos</description>
                <status>0x93</status>
                <midino>0x68</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <!-- Hotcue Section -->
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 1</description>
                <status>0x93</status>
                <midino>0x0B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 2</description>
                <status>0x93</status>
                <midino>0x0C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 3</description>
                <status>0x93</status>
                <midino>0x0D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 4</description>
                <status>0x93</status>
                <midino>0x0E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 5</description>
                <status>0x93</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 6</description>
                <status>0x93</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 7</description>
                <status>0x93</status>
                <midino>0x11</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_activate</key>
                <description>HotCue 8</description>
                <status>0x93</status>
                <midino>0x12</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.hotcue_clear</key>
                <description>HotCue Delete</description>
                <status>0x93</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- Loop Section -->
            <control>
                <group>[Channel4]</group>
                <key>loop_in</key>
                <status>0x93</status>
                <midino>0x44</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>loop_out</key>
                <status>0x93</status>
                <midino>0x45</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>reloop_exit</key>
                <status>0x93</status>
                <midino>0x05</midino>
                <options>
                    <normal/>
                </options>
            </control>
			<control>
				<group>[Channel4]</group>
				<key>repeat</key>
				<status>0x93</status>
				<midino>0x06</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel4]</group>
				<key>beatjump_1_forward</key>
				<status>0x93</status>
				<midino>0x07</midino>
				<options>
					<normal/>
				</options>
			</control>
			<control>
				<group>[Channel4]</group>
				<key>beatjump_1_backward</key>
				<status>0x93</status>
				<midino>0x08</midino>
				<options>
					<normal/>
				</options>
			</control>
            <control>
                <group>[Channel4]</group>
                <key>beatloop_4_activate</key>
                <description>Push Length to enable 4 Beatloop</description>
                <status>0x93</status>
                <midino>0x2B</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.loop_double_halve</key>
                <description>If Length Pushed, Turn to size the Loop</description>
                <status>0xB3</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0xB3</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.loop_move</key>
                <description>todo</description>
                <status>0x93</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beats_adjust_faster</key>
                <status>0x93</status>
                <midino>0x46</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>beats_adjust_slower</key>
                <status>0x93</status>
                <midino>0x47</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[QuickEffectRack1_[Channel4]]</group>
                <key>super1</key>
                <status>0xB3</status>
                <midino>0x32</midino>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <!-- Pitch and Jog -->
            <control>
                <group>[Channel4]</group>
                <key>rate</key>
                <status>0xE3</status>
                <options>
                    <Soft-takeover/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_up</key>
                <status>0x93</status>
                <midino>0x2A</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>rate_temp_down</key>
                <status>0x93</status>
                <midino>0x29</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.wheelTurn</key>
                <status>0xB3</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel4]</group>
				<key>Jockey3ME.wheelTurn</key>
				<description>Default Jog is Pitchbend</description>
				<status>0xB3</status>
				<midino>0x28</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <control>
                <group>[Channel4]</group>
                <key>Jockey3ME.wheelTouch</key>
                <status>0x93</status>
                <midino>0x22</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
			<control>
				<group>[Channel4]</group>
				<key>Jockey3ME.wheelTouch</key>
				<description>Track Search Stop</description>
				<status>0x93</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
			<control>
				<group>[Channel4]</group>
				<key>Jockey3ME.trackSearch</key>
				<status>0xB3</status>
				<midino>0x26</midino>
				<options>
					<script-binding/>
				</options>
			</control>
            <!-- Mixer Section -->
            <control>
                <group>[Channel4]</group>
                <key>pfl</key>
                <status>0x93</status>
                <midino>0x1C</midino>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>

        <!-- Leds -->
        <outputs>
			<output>
				<group>[Microphone]</group>
				<key>talkover</key>
				<status>0x90</status>
				<midino>0x56</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Microphone]</group>
				<key>talkover</key>
				<status>0x91</status>
				<midino>0x56</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Microphone]</group>
				<key>talkover</key>
				<status>0x92</status>
				<midino>0x56</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Microphone]</group>
				<key>talkover</key>
				<status>0x93</status>
				<midino>0x56</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x00</on>
				<off>0x00</off>
				<maximum>0.09</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x01</on>
				<off>0x00</off>
				<minimum>0.1</minimum>
				<maximum>0.19</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x02</on>
				<off>0x01</off>
				<minimum>0.2</minimum>
				<maximum>0.29</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x03</on>
				<off>0x02</off>
				<minimum>0.3</minimum>
				<maximum>0.39</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x04</on>
				<off>0x03</off>
				<minimum>0.4</minimum>
				<maximum>0.49</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x05</on>
				<off>0x04</off>
				<minimum>0.5</minimum>
				<maximum>0.59</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x06</on>
				<off>0x05</off>
				<minimum>0.6</minimum>
				<maximum>0.69</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x07</on>
				<off>0x06</off>
				<minimum>0.7</minimum>
				<maximum>0.79</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x08</on>
				<off>0x07</off>
				<minimum>0.8</minimum>
				<maximum>0.89</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>vu_meter</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x09</on>
				<off>0x08</off>
				<minimum>0.9</minimum>
				<maximum>0.99</maximum>
			</output>
			<output>
				<group>[Channel1]</group>
				<key>peak_indicator</key>
				<status>0x90</status>
				<midino>0x21</midino>
				<on>0x0A</on>
				<off>0x09</off>
				<minimum>0.1</minimum>
				<maximum>1.0</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x00</on>
				<off>0x00</off>
				<maximum>0.09</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x01</on>
				<off>0x00</off>
				<minimum>0.1</minimum>
				<maximum>0.19</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x02</on>
				<off>0x01</off>
				<minimum>0.2</minimum>
				<maximum>0.29</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x03</on>
				<off>0x02</off>
				<minimum>0.3</minimum>
				<maximum>0.39</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x04</on>
				<off>0x03</off>
				<minimum>0.4</minimum>
				<maximum>0.49</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x05</on>
				<off>0x04</off>
				<minimum>0.5</minimum>
				<maximum>0.59</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x06</on>
				<off>0x05</off>
				<minimum>0.6</minimum>
				<maximum>0.69</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x07</on>
				<off>0x06</off>
				<minimum>0.7</minimum>
				<maximum>0.79</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x08</on>
				<off>0x07</off>
				<minimum>0.8</minimum>
				<maximum>0.89</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>vu_meter</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x09</on>
				<off>0x08</off>
				<minimum>0.9</minimum>
				<maximum>0.99</maximum>
			</output>
			<output>
				<group>[Channel2]</group>
				<key>peak_indicator</key>
				<status>0x91</status>
				<midino>0x21</midino>
				<on>0x0A</on>
				<off>0x09</off>
				<minimum>0.1</minimum>
				<maximum>1.0</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x00</on>
				<off>0x00</off>
				<maximum>0.09</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x01</on>
				<off>0x00</off>
				<minimum>0.1</minimum>
				<maximum>0.19</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x02</on>
				<off>0x01</off>
				<minimum>0.2</minimum>
				<maximum>0.29</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x03</on>
				<off>0x02</off>
				<minimum>0.3</minimum>
				<maximum>0.39</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x04</on>
				<off>0x03</off>
				<minimum>0.4</minimum>
				<maximum>0.49</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x05</on>
				<off>0x04</off>
				<minimum>0.5</minimum>
				<maximum>0.59</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x06</on>
				<off>0x05</off>
				<minimum>0.6</minimum>
				<maximum>0.69</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x07</on>
				<off>0x06</off>
				<minimum>0.7</minimum>
				<maximum>0.79</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x08</on>
				<off>0x07</off>
				<minimum>0.8</minimum>
				<maximum>0.89</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>vu_meter</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x09</on>
				<off>0x08</off>
				<minimum>0.9</minimum>
				<maximum>0.99</maximum>
			</output>
			<output>
				<group>[Channel3]</group>
				<key>peak_indicator</key>
				<status>0x92</status>
				<midino>0x21</midino>
				<on>0x0A</on>
				<off>0x09</off>
				<minimum>0.1</minimum>
				<maximum>1.0</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x00</on>
				<off>0x00</off>
				<maximum>0.09</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x01</on>
				<off>0x00</off>
				<minimum>0.1</minimum>
				<maximum>0.19</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x02</on>
				<off>0x01</off>
				<minimum>0.2</minimum>
				<maximum>0.29</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x03</on>
				<off>0x02</off>
				<minimum>0.3</minimum>
				<maximum>0.39</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x04</on>
				<off>0x03</off>
				<minimum>0.4</minimum>
				<maximum>0.49</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x05</on>
				<off>0x04</off>
				<minimum>0.5</minimum>
				<maximum>0.59</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x06</on>
				<off>0x05</off>
				<minimum>0.6</minimum>
				<maximum>0.69</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x07</on>
				<off>0x06</off>
				<minimum>0.7</minimum>
				<maximum>0.79</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x08</on>
				<off>0x07</off>
				<minimum>0.8</minimum>
				<maximum>0.89</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>vu_meter</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x09</on>
				<off>0x08</off>
				<minimum>0.9</minimum>
				<maximum>0.99</maximum>
			</output>
			<output>
				<group>[Channel4]</group>
				<key>peak_indicator</key>
				<status>0x93</status>
				<midino>0x21</midino>
				<on>0x0A</on>
				<off>0x09</off>
				<minimum>0.1</minimum>
				<maximum>1.0</maximum>
			</output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>enabled</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect1]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x02</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect2]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x03</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit1_Effect3]</group>
				<key>enabled</key>
				<status>0x90</status>
				<midino>0x04</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>enabled</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect1]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x02</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect2]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x03</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit2_Effect3]</group>
				<key>enabled</key>
				<status>0x91</status>
				<midino>0x04</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>enabled</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect1]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x02</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect2]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x03</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit3_Effect3]</group>
				<key>enabled</key>
				<status>0x92</status>
				<midino>0x04</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>enabled</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect1]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x02</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect2]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x03</midino>
				<minimum>0.1</minimum>
			</output>
			<output>
				<group>[EffectRack1_EffectUnit4_Effect3]</group>
				<key>enabled</key>
				<status>0x93</status>
				<midino>0x04</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Led</description>
                <status>0x90</status>
                <midino>0x40</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Led</description>
                <status>0x90</status>
                <midino>0x41</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Led</description>
                <status>0x90</status>
                <midino>0x42</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit1]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Led</description>
                <status>0x90</status>
                <midino>0x43</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Led</description>
                <status>0x91</status>
                <midino>0x40</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Led</description>
                <status>0x91</status>
                <midino>0x41</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Led</description>
                <status>0x91</status>
                <midino>0x42</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit2]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Led</description>
                <status>0x91</status>
                <midino>0x43</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Led</description>
                <status>0x92</status>
                <midino>0x40</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Led</description>
                <status>0x92</status>
                <midino>0x41</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Led</description>
                <status>0x92</status>
                <midino>0x42</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit3]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Led</description>
                <status>0x92</status>
                <midino>0x43</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel1]_enable</key>
                <description>Preset1 Led</description>
                <status>0x93</status>
                <midino>0x40</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel2]_enable</key>
                <description>Preset2 Led</description>
                <status>0x93</status>
                <midino>0x41</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel3]_enable</key>
                <description>Preset3 Led</description>
                <status>0x93</status>
                <midino>0x42</midino>
                <minimum>
                    0.1
                </minimum>
            </output>
            <output>
                <group>[EffectRack1_EffectUnit4]</group>
                <key>group_[Channel4]_enable</key>
                <description>Preset4 Led</description>
                <status>0x93</status>
                <midino>0x43</midino>
                <minimum>
                    0.1
                </minimum>
            </output>


            <!-- [Channel 1] Deck A -->
            <!-- Transport Section -->

            <output>
                <group>[Channel1]</group>
                <key>play_indicator</key>
                <status>0x90</status>
                <midino>0x1A</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_indicator</key>
                <status>0x90</status>
                <midino>0x19</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>sync_enabled</key>
                <status>0x90</status>
                <midino>0x17</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>keylock</key>
                <status>0x90</status>
                <midino>0x59</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- HotCue Section -->
            <output>
                <group>[Channel1]</group>
                <key>hotcue_1_enabled</key>
                <status>0x90</status>
                <midino>0x0B</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_2_enabled</key>
                <status>0x90</status>
                <midino>0x0C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_3_enabled</key>
                <status>0x90</status>
                <midino>0x0D</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_4_enabled</key>
                <status>0x90</status>
                <midino>0x0E</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_5_enabled</key>
                <status>0x90</status>
                <midino>0x0F</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_6_enabled</key>
                <status>0x90</status>
                <midino>0x10</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_7_enabled</key>
                <status>0x90</status>
                <midino>0x11</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>hotcue_8_enabled</key>
                <status>0x90</status>
                <midino>0x12</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Loop Section -->
            <output>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <status>0x90</status>
                <midino>0x44</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <status>0x90</status>
                <midino>0x45</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <status>0x90</status>
                <midino>0x05</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[Channel1]</group>
				<key>repeat</key>
				<status>0x90</status>
				<midino>0x06</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[Channel1]</group>
                <key>quantize</key>
                <status>0x90</status>
                <midino>0x58</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Mixer Section -->
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <status>0x90</status>
                <midino>0x1C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>beat_active</key>
                <status>0x90</status>
                <midino>0x1B</midino>
                <minimum>0.1</minimum>
            </output>


            <!-- [Channel2] -->
            <!-- Transport Section -->

            <output>
                <group>[Channel2]</group>
                <key>play_indicator</key>
                <status>0x91</status>
                <midino>0x1A</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_indicator</key>
                <status>0x91</status>
                <midino>0x19</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>sync_enabled</key>
                <status>0x91</status>
                <midino>0x17</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>keylock</key>
                <status>0x91</status>
                <midino>0x59</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- HotCues -->
            <output>
                <group>[Channel2]</group>
                <key>hotcue_1_enabled</key>
                <status>0x91</status>
                <midino>0x0B</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_2_enabled</key>
                <status>0x91</status>
                <midino>0x0C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_3_enabled</key>
                <status>0x91</status>
                <midino>0x0D</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_4_enabled</key>
                <status>0x91</status>
                <midino>0x0E</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_5_enabled</key>
                <status>0x91</status>
                <midino>0x0F</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_6_enabled</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_7_enabled</key>
                <status>0x91</status>
                <midino>0x11</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>hotcue_8_enabled</key>
                <status>0x91</status>
                <midino>0x12</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Loop Section -->
            <output>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <status>0x91</status>
                <midino>0x44</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <status>0x91</status>
                <midino>0x45</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[Channel2]</group>
				<key>repeat</key>
				<status>0x91</status>
				<midino>0x06</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[Channel2]</group>
                <key>quantize</key>
                <status>0x91</status>
                <midino>0x58</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Mixer Section -->
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <status>0x91</status>
                <midino>0x1C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>beat_active</key>
                <status>0x91</status>
                <midino>0x1B</midino>
                <minimum>0.1</minimum>
            </output>


            <!-- [Channel 3] Deck C -->
            <!-- Transport Section -->

            <output>
                <group>[Channel3]</group>
                <key>play_indicator</key>
                <status>0x92</status>
                <midino>0x1A</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>cue_indicator</key>
                <status>0x92</status>
                <midino>0x19</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>sync_enabled</key>
                <status>0x92</status>
                <midino>0x17</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>keylock</key>
                <status>0x92</status>
                <midino>0x59</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- HotCue Section -->
            <output>
                <group>[Channel3]</group>
                <key>hotcue_1_enabled</key>
                <status>0x92</status>
                <midino>0x0B</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_2_enabled</key>
                <status>0x92</status>
                <midino>0x0C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_3_enabled</key>
                <status>0x92</status>
                <midino>0x0D</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_4_enabled</key>
                <status>0x92</status>
                <midino>0x0E</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_5_enabled</key>
                <status>0x92</status>
                <midino>0x0F</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_6_enabled</key>
                <status>0x92</status>
                <midino>0x10</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_7_enabled</key>
                <status>0x92</status>
                <midino>0x11</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>hotcue_8_enabled</key>
                <status>0x92</status>
                <midino>0x12</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Loop Section -->
            <output>
                <group>[Channel3]</group>
                <key>loop_in</key>
                <status>0x92</status>
                <midino>0x44</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>loop_out</key>
                <status>0x92</status>
                <midino>0x45</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>reloop_exit</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[Channel3]</group>
				<key>repeat</key>
				<status>0x92</status>
				<midino>0x06</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[Channel3]</group>
                <key>quantize</key>
                <status>0x92</status>
                <midino>0x58</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Mixer Section -->
            <output>
                <group>[Channel3]</group>
                <key>pfl</key>
                <status>0x92</status>
                <midino>0x1C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel3]</group>
                <key>beat_active</key>
                <status>0x92</status>
                <midino>0x1B</midino>
                <minimum>0.1</minimum>
            </output>


            <!-- [Channel 4] Deck D -->
            <!-- Transport Section -->

            <output>
                <group>[Channel4]</group>
                <key>play_indicator</key>
                <status>0x93</status>
                <midino>0x1A</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>cue_indicator</key>
                <status>0x93</status>
                <midino>0x19</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>sync_enabled</key>
                <status>0x93</status>
                <midino>0x17</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>keylock</key>
                <status>0x93</status>
                <midino>0x59</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- HotCue Section -->
            <output>
                <group>[Channel4]</group>
                <key>hotcue_1_enabled</key>
                <status>0x93</status>
                <midino>0x0B</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_2_enabled</key>
                <status>0x93</status>
                <midino>0x0C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_3_enabled</key>
                <status>0x93</status>
                <midino>0x0D</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_4_enabled</key>
                <status>0x93</status>
                <midino>0x0E</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_5_enabled</key>
                <status>0x93</status>
                <midino>0x0F</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_6_enabled</key>
                <status>0x93</status>
                <midino>0x10</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_7_enabled</key>
                <status>0x93</status>
                <midino>0x11</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>hotcue_8_enabled</key>
                <status>0x93</status>
                <midino>0x12</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Loop Section -->
            <output>
                <group>[Channel4]</group>
                <key>loop_in</key>
                <status>0x93</status>
                <midino>0x44</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>loop_out</key>
                <status>0x93</status>
                <midino>0x45</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>reloop_exit</key>
                <status>0x93</status>
                <midino>0x05</midino>
                <minimum>0.1</minimum>
            </output>
			<output>
				<group>[Channel4]</group>
				<key>repeat</key>
				<status>0x93</status>
				<midino>0x06</midino>
				<minimum>0.1</minimum>
			</output>
            <output>
                <group>[Channel4]</group>
                <key>quantize</key>
                <status>0x93</status>
                <midino>0x58</midino>
                <minimum>0.1</minimum>
            </output>
            <!-- Mixer Section -->
            <output>
                <group>[Channel4]</group>
                <key>pfl</key>
                <status>0x93</status>
                <midino>0x1C</midino>
                <minimum>0.1</minimum>
            </output>
            <output>
                <group>[Channel4]</group>
                <key>beat_active</key>
                <status>0x93</status>
                <midino>0x1B</midino>
                <minimum>0.1</minimum>
            </output>
        </outputs>
    </controller>
</MixxxControllerPreset>
