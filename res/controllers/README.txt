Quick-start: Adding/changing controller mappings & advanced behaviors
---------------------------------------------------------------------

http://mixxx.org/forums/viewtopic.php?f=3&t=949

   - Are you feeling sad because your controller is not supported well
     or at all in Mixxx?
   - Take heart, my friend! Mixxx offers you various ways to get your
     controller working right away on your own!

Mixxx Controller/MIDI Mapping Documentation
-------------------------------------------

http://mixxx.org/wiki/doku.php/#controller_midi_mapping_documentation

    - Find out how to create or change a mapping file for your controller.
    - Read how you can take complete control over your controller and
      create advanced behaviors including easy wheel scratching.
    - Browse the list of Mixxx's controls you can manipulate with your
      controller via the mapping file and/or a script.
