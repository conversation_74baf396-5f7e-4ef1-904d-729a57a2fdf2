<?xml version='1.0' encoding='utf-8'?>
<MixxxMIDIPreset mixxxVersion="2.1.0" schemaVersion="1">
  <info>
    <name>Reloop Terminal Mix 2/4</name>
    <author><PERSON> (1.11), ronso0 (2.1 update)</author>
    <description>A complete 4-deck preset for a single Reloop Terminal Mix 2 or 4.</description>
    <manual>reloop_terminal_mix_series</manual>
  </info>
  <controller id="TerminalMix">
    <scriptfiles>
      <file functionprefix="" filename="lodash.mixxx.js"/>
      <file functionprefix="" filename="midi-components-0.0.js"/>
      <file filename="Reloop Terminal Mix 2-4.js" functionprefix="TerminalMix"/>
    </scriptfiles>
    <controls>
<!-- Center Mixer -->
      <control>
        <group>[Master]</group>
        <key>headMix</key>
        <description>Master</description>
        <status>0xB0</status>
        <midino>0x33</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>TerminalMix.crossFader</key>
        <description>crossfader</description>
        <status>0xB0</status>
        <midino>0x3A</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Sampler volume knob -->
        <group>[Sampler1]</group>
        <key>TerminalMix.samplerVolume</key>
        <status>0xB0</status>
        <midino>0x34</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Trax knob turn -->
        <group>[Library]</group>
        <key>TerminalMix.traxKnobTurn</key>
        <description>Library</description>
        <status>0xB0</status>
        <midino>0x39</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Trax knob press -->
        <group>[Library]</group>
        <key>GoToItem</key>
        <description>Library</description>
        <status>0x90</status>
        <midino>0x39</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control> <!-- Back button -->
        <group>[Library]</group>
        <key>TerminalMix.backButton</key>
        <description>Library</description>
        <status>0x90</status>
        <midino>0x37</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
<!-- Cross-fader assign switches -->
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.cfAssignL</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x3B</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.cfAssignM</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x3C</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.cfAssignR</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x3D</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.cfAssignL</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x3E</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.cfAssignM</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x3F</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.cfAssignR</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x40</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>

      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.cfAssignL</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x41</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.cfAssignM</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x42</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.cfAssignR</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x43</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.cfAssignL</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x44</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.cfAssignM</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x45</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.cfAssignR</key>
        <description>crossfader</description>
        <status>0x90</status>
        <midino>0x46</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>

<!-- *** Deck 1 *** -->
      <control>   <!-- With vinyl button active -->
        <group>[Channel1]</group>
        <key>TerminalMix.wheelTouch</key>
        <description>wheel</description>
        <status>0x90</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- With vinyl button NOT active -->
        <group>[Channel1]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB0</status>
        <midino>0x27</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- With vinyl button active -->
        <group>[Channel1]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB0</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel1]]</group>
        <key>super1</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x2F</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>TerminalMix.crossfaderCurve</key>
        <description>crossfader</description>
        <status>0xB0</status>
        <midino>0x46</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.pitchRange</key>
        <status>0x90</status>
        <midino>0x01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>LoadSelectedTrack</key>
        <status>0x90</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control> <!-- Fader start -->
        <group>[Channel1]</group>
        <key>TerminalMix.faderStart</key>
        <description>play controls</description>
        <status>0x90</status>
        <midino>0x78</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <!-- Loop controls -->
      <control> <!-- Loop Length encoder press -->
        <group>[Channel1]</group>
				<key>TerminalMix.loopLengthPress</key>
        <description>looping</description>
        <status>0x90</status>
        <midino>0x0B</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
				<key>TerminalMix.loopLengthPress</key>
        <description>looping</description>
        <status>0x91</status>
        <midino>0x0B</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
				<key>TerminalMix.loopLengthPress</key>
        <description>looping</description>
        <status>0x92</status>
        <midino>0x0B</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
				<key>TerminalMix.loopLengthPress</key>
        <description>looping</description>
        <status>0x93</status>
        <midino>0x0B</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Shifetd Loop Length encoder press -->
        <group>[Channel1]</group>
				<key>TerminalMix.shiftedLoopLengthPress</key>
        <description>looping</description>
        <status>0x90</status>
        <midino>0x51</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
				<key>TerminalMix.shiftedLoopLengthPress</key>
        <description>looping</description>
        <status>0x91</status>
        <midino>0x51</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
				<key>TerminalMix.shiftedLoopLengthPress</key>
        <description>looping</description>
        <status>0x92</status>
        <midino>0x51</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
				<key>TerminalMix.shiftedLoopLengthPress</key>
        <description>looping</description>
        <status>0x93</status>
        <midino>0x51</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- Loop Length knob turn -->
        <group>[Channel1]</group>
        <key>TerminalMix.loopLengthTurn</key>
        <description>looping</description>
        <status>0xB0</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.loopLengthTurn</key>
        <description>looping</description>
        <status>0xB1</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.loopLengthTurn</key>
        <description>looping</description>
        <status>0xB2</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.loopLengthTurn</key>
        <description>looping</description>
        <status>0xB3</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Loop move press -->
        <group>[Channel1]</group>
        <key>TerminalMix.loopMovePress</key>
        <description>loopmove / beatjump</description>
        <status>0x90</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.loopMovePress</key>
        <description>loopmove / beatjump</description>
        <status>0x91</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.loopMovePress</key>
        <description>loopmove / beatjump</description>
        <status>0x92</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.loopMovePress</key>
        <description>loopmove / beatjump</description>
        <status>0x93</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Loop Move knob turn -->
        <group>[Channel1]</group>
        <key>TerminalMix.loopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB0</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.loopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB1</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.loopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB2</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.loopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB3</status>
        <midino>0x0E</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Shifted Loop Move knob turn -->
        <group>[Channel1]</group>
        <key>TerminalMix.shiftedLoopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB0</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.shiftedLoopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB1</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.shiftedLoopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB2</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.shiftedLoopMoveTurn</key>
        <description>loopmove / beatjump</description>
        <status>0xB3</status>
        <midino>0x54</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_in</key>
        <description>looping</description>
        <status>0x90</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_in</key>
        <description>looping</description>
        <status>0x91</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_in</key>
        <description>looping</description>
        <status>0x92</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_in</key>
        <description>looping</description>
        <status>0x93</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>loop_out</key>
        <description>looping</description>
        <status>0x90</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>loop_out</key>
        <description>looping</description>
        <status>0x91</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>loop_out</key>
        <description>looping</description>
        <status>0x92</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>loop_out</key>
        <description>looping</description>
        <status>0x93</status>
        <midino>0x0D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <!--    Samplers    -->
      <control>
        <group>[Sampler1]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x14</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x15</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x16</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x17</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x1C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x1D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x1E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x1F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x5A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x5B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x5C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x5D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x63</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x64</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_stop</key>
        <status>0x90</status>
        <midino>0x65</midino>
        <options>
          <normal/>
        </options>
      </control>
      <!--    Hot cues    -->
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_activate</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_activate</key>
        <status>0x90</status>
        <midino>0x11</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_activate</key>
        <status>0x90</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_activate</key>
        <status>0x90</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_5_activate</key>
        <status>0x90</status>
        <midino>0x18</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_6_activate</key>
        <status>0x90</status>
        <midino>0x19</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_7_activate</key>
        <status>0x90</status>
        <midino>0x1A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_8_activate</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_1_clear</key>
        <status>0x90</status>
        <midino>0x56</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_2_clear</key>
        <status>0x90</status>
        <midino>0x57</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_3_clear</key>
        <status>0x90</status>
        <midino>0x58</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_4_clear</key>
        <status>0x90</status>
        <midino>0x59</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_5_clear</key>
        <status>0x90</status>
        <midino>0x5E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_6_clear</key>
        <status>0x90</status>
        <midino>0x5F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_7_clear</key>
        <status>0x90</status>
        <midino>0x60</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>hotcue_8_clear</key>
        <status>0x90</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <description>BPM</description>
        <status>0x90</status>
        <midino>0x22</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control> <!-- Quantize -->
        <group>[Channel1]</group>
        <key>quantize</key>
        <status>0x90</status>
        <midino>0x54</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>quantize</key>
        <status>0x91</status>
        <midino>0x54</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>quantize</key>
        <status>0x92</status>
        <midino>0x54</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>quantize</key>
        <status>0x93</status>
        <midino>0x54</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>playposition</key>
        <status>0xB0</status>
        <midino>0x29</midino>
        <options>
          <rot64/>
        </options>
      </control>
      <control><!-- Beats knob press -->
        <group>[Channel1]</group>
        <key>bpm_tap</key>
        <description>BPM</description>
        <status>0x90</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control><!-- Shift + Beats knob press -->
        <group>[Channel1]</group>
        <key>beat_translate_curpos</key>
        <description>BPM</description>
        <status>0x90</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterLow</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x2E</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_gotoandplay</key>
        <description>play controls</description>
        <status>0x90</status>
        <midino>0x23</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>play</key>
        <description>play controls</description>
        <status>0x90</status>
        <midino>0x25</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.brake</key>
        <description>play controls</description>
        <status>0x90</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pfl</key>
        <description>mixer</description>
        <status>0x90</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterMid</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x2D</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.pitchSlider</key>
        <description>BPM</description>
        <status>0xE0</status>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <description>play controls</description>
        <status>0x90</status>
        <midino>0x24</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>filterHigh</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x2C</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>TerminalMix.channelFader</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x31</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>pregain</key>
        <description>mixer</description>
        <status>0xB0</status>
        <midino>0x2B</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <!-- *** Deck 2 *** -->
      <control>   <!-- With vinyl button active -->
        <group>[Channel2]</group>
        <key>TerminalMix.wheelTouch</key>
        <description>wheel</description>
        <status>0x91</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- With vinyl button NOT active -->
        <group>[Channel2]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB1</status>
        <midino>0x27</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control> <!-- With vinyl button active -->
        <group>[Channel2]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB1</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]]</group>
        <key>super1</key>
        <description>mixer</description>
        <status>0xB1</status>
        <midino>0x2F</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.pitchRange</key>
        <status>0x91</status>
        <midino>0x01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>LoadSelectedTrack</key>
        <status>0x91</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control> <!-- Fader start -->
        <group>[Channel2]</group>
        <key>TerminalMix.faderStart</key>
        <description>play controls</description>
        <status>0x91</status>
        <midino>0x78</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
<!--    Samplers    -->
      <control>
        <group>[Sampler1]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x14</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x15</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x16</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x17</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x1C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x1D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x1E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x1F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x5A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x5B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x5C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x5D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x63</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x64</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_stop</key>
        <status>0x91</status>
        <midino>0x65</midino>
        <options>
          <normal/>
        </options>
      </control>
<!--    Hot cues    -->
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_activate</key>
        <status>0x91</status>
        <midino>0x10</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_activate</key>
        <status>0x91</status>
        <midino>0x11</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_activate</key>
        <status>0x91</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_activate</key>
        <status>0x91</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_5_activate</key>
        <status>0x91</status>
        <midino>0x18</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_6_activate</key>
        <status>0x91</status>
        <midino>0x19</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_7_activate</key>
        <status>0x91</status>
        <midino>0x1A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_8_activate</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_1_clear</key>
        <status>0x91</status>
        <midino>0x56</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_2_clear</key>
        <status>0x91</status>
        <midino>0x57</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_3_clear</key>
        <status>0x91</status>
        <midino>0x58</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_4_clear</key>
        <status>0x91</status>
        <midino>0x59</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_5_clear</key>
        <status>0x91</status>
        <midino>0x5E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_6_clear</key>
        <status>0x91</status>
        <midino>0x5F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_7_clear</key>
        <status>0x91</status>
        <midino>0x60</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>hotcue_8_clear</key>
        <status>0x91</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
<!--    -->
      <control>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <description>BPM</description>
        <status>0x91</status>
        <midino>0x22</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>playposition</key>
        <status>0xB1</status>
        <midino>0x29</midino>
        <options>
          <rot64/>
        </options>
      </control>
      <control><!-- Beats knob press -->
        <group>[Channel2]</group>
        <key>bpm_tap</key>
        <description>BPM</description>
        <status>0x91</status>
        <midino>0x06</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control><!-- Shift + Beats knob press -->
        <group>[Channel2]</group>
        <key>beat_translate_curpos</key>
        <description>BPM</description>
        <status>0x91</status>
        <midino>0x4C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterLow</key>
        <description>mixer</description>
        <status>0xB1</status>
        <midino>0x2E</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_gotoandplay</key>
        <description>play controls</description>
        <status>0x91</status>
        <midino>0x23</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>play</key>
        <description>play controls</description>
        <status>0x91</status>
        <midino>0x25</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.brake</key>
        <description>play controls</description>
        <status>0x91</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>pfl</key>
        <description>mixer</description>
        <status>0x91</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterMid</key>
        <description>mixer</description>
        <status>0xB1</status>
        <midino>0x2D</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.pitchSlider</key>
        <description>BPM</description>
        <status>0xE1</status>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <description>play controls</description>
        <status>0x91</status>
        <midino>0x24</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>filterHigh</key>
        <description>mixer</description>
        <status>0xB1</status>
        <midino>0x2C</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>TerminalMix.channelFader</key>
        <description>mixer</description>
        <status>0xB1</status>
        <midino>0x31</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <description>mixer</description>
        <key>pregain</key>
        <status>0xB1</status>
        <midino>0x2B</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
<!-- *** Deck 3 *** -->
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.wheelTouch</key>
        <description>wheel</description>
        <status>0x92</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB2</status>
        <midino>0x27</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB2</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel3]]</group>
        <key>super1</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x2F</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.pitchRange</key>
        <status>0x92</status>
        <midino>0x01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>LoadSelectedTrack</key>
        <status>0x92</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.faderStart</key>
        <description>play controls</description>
        <status>0x92</status>
        <midino>0x78</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
<!--    Samplers    -->
      <control>
        <group>[Sampler1]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x14</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x15</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x16</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x17</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x1C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x1D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x1E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x1F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x5A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x5B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x5C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x5D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x63</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x64</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_stop</key>
        <status>0x92</status>
        <midino>0x65</midino>
        <options>
          <normal/>
        </options>
      </control>
      <!--    Hot cues    -->
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_activate</key>
        <status>0x92</status>
        <midino>0x10</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_activate</key>
        <status>0x92</status>
        <midino>0x11</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_activate</key>
        <status>0x92</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_activate</key>
        <status>0x92</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_5_activate</key>
        <status>0x92</status>
        <midino>0x18</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_6_activate</key>
        <status>0x92</status>
        <midino>0x19</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_7_activate</key>
        <status>0x92</status>
        <midino>0x1A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_8_activate</key>
        <status>0x92</status>
        <midino>0x1B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_1_clear</key>
        <status>0x92</status>
        <midino>0x56</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_2_clear</key>
        <status>0x92</status>
        <midino>0x57</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_3_clear</key>
        <status>0x92</status>
        <midino>0x58</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_4_clear</key>
        <status>0x92</status>
        <midino>0x59</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_5_clear</key>
        <status>0x92</status>
        <midino>0x5E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_6_clear</key>
        <status>0x92</status>
        <midino>0x5F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_7_clear</key>
        <status>0x92</status>
        <midino>0x60</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>hotcue_8_clear</key>
        <status>0x92</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>beatsync</key>
        <description>BPM</description>
        <status>0x92</status>
        <midino>0x22</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>playposition</key>
        <status>0xB2</status>
        <midino>0x29</midino>
        <options>
          <rot64/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>filterLow</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x2E</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_gotoandplay</key>
        <description>play controls</description>
        <status>0x92</status>
        <midino>0x23</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>play</key>
        <description>play controls</description>
        <status>0x92</status>
        <midino>0x25</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.brake</key>
        <description>play controls</description>
        <status>0x92</status>
        <midino>0x68</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pfl</key>
        <description>mixer</description>
        <status>0x92</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>filterMid</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x2D</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.pitchSlider</key>
        <description>BPM</description>
        <status>0xE2</status>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>cue_default</key>
        <description>play controls</description>
        <status>0x92</status>
        <midino>0x24</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>filterHigh</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x2C</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>keylock</key>
        <status>0x92</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>TerminalMix.channelFader</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x31</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel3]</group>
        <key>pregain</key>
        <description>mixer</description>
        <status>0xB2</status>
        <midino>0x2B</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <!-- *** Deck 4 *** -->
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.wheelTouch</key>
        <description>wheel</description>
        <status>0x93</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB3</status>
        <midino>0x27</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.wheelTurn</key>
        <description>wheel</description>
        <status>0xB3</status>
        <midino>0x28</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel4]]</group>
        <key>super1</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x2F</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.pitchRange</key>
        <status>0x93</status>
        <midino>0x01</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>LoadSelectedTrack</key>
        <status>0x93</status>
        <midino>0x32</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.faderStart</key>
        <description>play controls</description>
        <status>0x93</status>
        <midino>0x78</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <!--    Samplers    -->
      <control>
        <group>[Sampler1]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x14</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x15</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x16</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x17</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x1C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x1D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x1E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x1F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x5A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x5B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x5C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x5D</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x62</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x63</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x64</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>start_stop</key>
        <status>0x93</status>
        <midino>0x65</midino>
        <options>
          <normal/>
        </options>
      </control>
      <!--    Hot cues    -->
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_activate</key>
        <status>0x93</status>
        <midino>0x10</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_activate</key>
        <status>0x93</status>
        <midino>0x11</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_activate</key>
        <status>0x93</status>
        <midino>0x12</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_activate</key>
        <status>0x93</status>
        <midino>0x13</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_5_activate</key>
        <status>0x93</status>
        <midino>0x18</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_6_activate</key>
        <status>0x93</status>
        <midino>0x19</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_7_activate</key>
        <status>0x93</status>
        <midino>0x1A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_8_activate</key>
        <status>0x93</status>
        <midino>0x1B</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_1_clear</key>
        <status>0x93</status>
        <midino>0x56</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_2_clear</key>
        <status>0x93</status>
        <midino>0x57</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_3_clear</key>
        <status>0x93</status>
        <midino>0x58</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_4_clear</key>
        <status>0x93</status>
        <midino>0x59</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_5_clear</key>
        <status>0x93</status>
        <midino>0x5E</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_6_clear</key>
        <status>0x93</status>
        <midino>0x5F</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_7_clear</key>
        <status>0x93</status>
        <midino>0x60</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>hotcue_8_clear</key>
        <status>0x93</status>
        <midino>0x61</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>beatsync</key>
        <description>BPM</description>
        <status>0x93</status>
        <midino>0x22</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>playposition</key>
        <status>0xB3</status>
        <midino>0x29</midino>
        <options>
          <rot64/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>filterLow</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x2E</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_gotoandplay</key>
        <description>play controls</description>
        <status>0x93</status>
        <midino>0x23</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>play</key>
        <description>play controls</description>
        <status>0x93</status>
        <midino>0x25</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.brake</key>
        <description>play controls</description>
        <status>0x93</status>
        <midino>0x6B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pfl</key>
        <description>mixer</description>
        <status>0x93</status>
        <midino>0x30</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>filterMid</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x2D</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.pitchSlider</key>
        <description>BPM</description>
        <status>0xE3</status>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>cue_default</key>
        <description>play controls</description>
        <status>0x93</status>
        <midino>0x24</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>filterHigh</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x2C</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>keylock</key>
        <status>0x93</status>
        <midino>0x02</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>TerminalMix.channelFader</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x31</midino>
        <options>
          <Script-Binding/>
        </options>
      </control>
      <control>
        <group>[Channel4]</group>
        <key>pregain</key>
        <description>mixer</description>
        <status>0xB3</status>
        <midino>0x2B</midino>
        <options>
          <soft-takeover/>
        </options>
      </control>
<!-- Effect controls -->
      <control><!-- Focus buttons, unshifted -->
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.effectFocusButton.input</key>
        <description>fx</description>
        <status>0x90</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <key>TerminalMix.effectUnit24.effectFocusButton.input</key>
        <group>[EffectRack1]</group>
        <description>fx</description>
        <status>0x91</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Focus buttons, shifted -->
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.effectFocusButton.input</key>
        <description>fx</description>
        <status>0x90</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.effectFocusButton.input</key>
        <description>fx</description>
        <status>0x91</status>
        <midino>0x50</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Effect enable buttons -->
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.enableButtons[1].input</key>
        <description>fx</description>
        <status>0x90</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.enableButtons[1].input</key>
        <description>fx</description>
        <status>0x91</status>
        <midino>0x07</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.enableButtons[2].input</key>
        <description>fx</description>
        <status>0x90</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.enableButtons[2].input</key>
        <description>fx</description>
        <status>0x91</status>
        <midino>0x08</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.enableButtons[3].input</key>
        <description>fx</description>
        <status>0x90</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.enableButtons[3].input</key>
        <description>fx</description>
        <status>0x91</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Dry/Wet knobs -->
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.dryWetKnob.input</key>
        <description>fx</description>
        <status>0xB0</status>
        <!-- 'Beats' knob -->
        <midino>0x06</midino>
        <!-- Gain knob
        <midino>0x2B</midino> -->
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.dryWetKnob.input</key>
        <description>fx</description>
        <status>0xB1</status>
        <!-- 'Beats' knob -->
        <midino>0x06</midino>
        <!-- Gain knob
        <midino>0x2B</midino> -->
        <options>
          <script-binding/>
        </options>
      </control>
      <control><!-- Effect knobs -->
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.knobs[1].input</key>
        <description>fx</description>
        <status>0xB0</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.knobs[1].input</key>
        <description>fx</description>
        <status>0xB1</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.knobs[2].input</key>
        <description>fx</description>
        <status>0xB0</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.knobs[2].input</key>
        <description>fx</description>
        <status>0xB1</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit13.knobs[3].input</key>
        <description>fx</description>
        <status>0xB0</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[EffectRack1]</group>
        <key>TerminalMix.effectUnit24.knobs[3].input</key>
        <description>fx</description>
        <status>0xB1</status>
        <midino>0x05</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <!-- Shift buttons -->
      <control>
        <group>[Controller]</group>
        <key>TerminalMix.shiftButtonL</key>
        <description>misc</description>
        <status>0x90</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Controller]</group>
        <key>TerminalMix.shiftButtonR</key>
        <description>misc</description>
        <status>0x91</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
    </controls>

    <outputs>
      <!-- *** Deck 1 *** -->
      <output>
        <group>[Channel1]</group>
        <key>beatsync</key>
        <status>0x90</status>
        <midino>0x22</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>cue_gotoandplay</key>
        <status>0x90</status>
        <midino>0x23</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>cue_default</key>
        <status>0x90</status>
        <midino>0x24</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>play</key>
        <status>0x90</status>
        <midino>0x25</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>keylock</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_in</key>
        <status>0x90</status>
        <midino>0x0C</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>loop_out</key>
        <status>0x90</status>
        <midino>0x0D</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>pfl</key>
        <status>0x90</status>
        <midino>0x30</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_1_enabled</key>
        <status>0x90</status>
        <midino>0x10</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_2_enabled</key>
        <status>0x90</status>
        <midino>0x11</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_3_enabled</key>
        <status>0x90</status>
        <midino>0x12</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_4_enabled</key>
        <status>0x90</status>
        <midino>0x13</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_5_enabled</key>
        <status>0x90</status>
        <midino>0x18</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_6_enabled</key>
        <status>0x90</status>
        <midino>0x19</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_7_enabled</key>
        <status>0x90</status>
        <midino>0x1A</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_8_enabled</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- Hot cues (shifted) -->
      <output>
        <group>[Channel1]</group>
        <key>hotcue_1_enabled</key>
        <status>0x90</status>
        <midino>0x56</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_2_enabled</key>
        <status>0x90</status>
        <midino>0x57</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_3_enabled</key>
        <status>0x90</status>
        <midino>0x58</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_4_enabled</key>
        <status>0x90</status>
        <midino>0x59</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_5_enabled</key>
        <status>0x90</status>
        <midino>0x5E</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_6_enabled</key>
        <status>0x90</status>
        <midino>0x5F</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_7_enabled</key>
        <status>0x90</status>
        <midino>0x60</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel1]</group>
        <key>hotcue_8_enabled</key>
        <status>0x90</status>
        <midino>0x61</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- *** Deck 2 *** -->
      <output>
        <group>[Channel2]</group>
        <key>beatsync</key>
        <status>0x91</status>
        <midino>0x22</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>cue_gotoandplay</key>
        <status>0x91</status>
        <midino>0x23</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>cue_default</key>
        <status>0x91</status>
        <midino>0x24</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>play</key>
        <status>0x91</status>
        <midino>0x25</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>keylock</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_in</key>
        <status>0x91</status>
        <midino>0x0C</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>loop_out</key>
        <status>0x91</status>
        <midino>0x0D</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>pfl</key>
        <status>0x91</status>
        <midino>0x30</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_1_enabled</key>
        <status>0x91</status>
        <midino>0x10</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_2_enabled</key>
        <status>0x91</status>
        <midino>0x11</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_3_enabled</key>
        <status>0x91</status>
        <midino>0x12</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_4_enabled</key>
        <status>0x91</status>
        <midino>0x13</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_5_enabled</key>
        <status>0x91</status>
        <midino>0x18</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_6_enabled</key>
        <status>0x91</status>
        <midino>0x19</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_7_enabled</key>
        <status>0x91</status>
        <midino>0x1A</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_8_enabled</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- Hot cues (shifted) -->
      <output>
        <group>[Channel2]</group>
        <key>hotcue_1_enabled</key>
        <status>0x91</status>
        <midino>0x56</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_2_enabled</key>
        <status>0x91</status>
        <midino>0x57</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_3_enabled</key>
        <status>0x91</status>
        <midino>0x58</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_4_enabled</key>
        <status>0x91</status>
        <midino>0x59</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_5_enabled</key>
        <status>0x91</status>
        <midino>0x5E</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_6_enabled</key>
        <status>0x91</status>
        <midino>0x5F</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_7_enabled</key>
        <status>0x91</status>
        <midino>0x60</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel2]</group>
        <key>hotcue_8_enabled</key>
        <status>0x91</status>
        <midino>0x61</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- *** Deck 3 *** -->
      <output>
        <group>[Channel3]</group>
        <key>beatsync</key>
        <status>0x92</status>
        <midino>0x22</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>cue_gotoandplay</key>
        <status>0x92</status>
        <midino>0x23</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>cue_default</key>
        <status>0x92</status>
        <midino>0x24</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>play</key>
        <status>0x92</status>
        <midino>0x25</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>keylock</key>
        <status>0x92</status>
        <midino>0x02</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_in</key>
        <status>0x92</status>
        <midino>0x0C</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>loop_out</key>
        <status>0x92</status>
        <midino>0x0D</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>pfl</key>
        <status>0x92</status>
        <midino>0x30</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_1_enabled</key>
        <status>0x92</status>
        <midino>0x10</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_2_enabled</key>
        <status>0x92</status>
        <midino>0x11</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_3_enabled</key>
        <status>0x92</status>
        <midino>0x12</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_4_enabled</key>
        <status>0x92</status>
        <midino>0x13</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_5_enabled</key>
        <status>0x92</status>
        <midino>0x18</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_6_enabled</key>
        <status>0x92</status>
        <midino>0x19</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_7_enabled</key>
        <status>0x92</status>
        <midino>0x1A</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_8_enabled</key>
        <status>0x92</status>
        <midino>0x1B</midino>
        <minimum>0.1</minimum>
      </output>

      <output>
        <group>[Channel3]</group>
        <key>hotcue_1_enabled</key>
        <status>0x92</status>
        <midino>0x56</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_2_enabled</key>
        <status>0x92</status>
        <midino>0x57</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_3_enabled</key>
        <status>0x92</status>
        <midino>0x58</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_4_enabled</key>
        <status>0x92</status>
        <midino>0x59</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_5_enabled</key>
        <status>0x92</status>
        <midino>0x5E</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_6_enabled</key>
        <status>0x92</status>
        <midino>0x5F</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_7_enabled</key>
        <status>0x92</status>
        <midino>0x60</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel3]</group>
        <key>hotcue_8_enabled</key>
        <status>0x92</status>
        <midino>0x61</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- *** Deck 4 *** -->
      <output>
        <group>[Channel4]</group>
        <key>beatsync</key>
        <status>0x93</status>
        <midino>0x22</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>cue_gotoandplay</key>
        <status>0x93</status>
        <midino>0x23</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>cue_default</key>
        <status>0x93</status>
        <midino>0x24</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>play</key>
        <status>0x93</status>
        <midino>0x25</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>keylock</key>
        <status>0x93</status>
        <midino>0x02</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_in</key>
        <status>0x93</status>
        <midino>0x0C</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>loop_out</key>
        <status>0x93</status>
        <midino>0x0D</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>pfl</key>
        <status>0x93</status>
        <midino>0x30</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_1_enabled</key>
        <status>0x93</status>
        <midino>0x10</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_2_enabled</key>
        <status>0x93</status>
        <midino>0x11</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_3_enabled</key>
        <status>0x93</status>
        <midino>0x12</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_4_enabled</key>
        <status>0x93</status>
        <midino>0x13</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_5_enabled</key>
        <status>0x93</status>
        <midino>0x18</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_6_enabled</key>
        <status>0x93</status>
        <midino>0x19</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_7_enabled</key>
        <status>0x93</status>
        <midino>0x1A</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_8_enabled</key>
        <status>0x93</status>
        <midino>0x1B</midino>
        <minimum>0.1</minimum>
      </output>
      <!-- Hot cues -->
      <output>
        <group>[Channel4]</group>
        <key>hotcue_1_enabled</key>
        <status>0x93</status>
        <midino>0x56</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_2_enabled</key>
        <status>0x93</status>
        <midino>0x57</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_3_enabled</key>
        <status>0x93</status>
        <midino>0x58</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_4_enabled</key>
        <status>0x93</status>
        <midino>0x59</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_5_enabled</key>
        <status>0x93</status>
        <midino>0x5E</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_6_enabled</key>
        <status>0x93</status>
        <midino>0x5F</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_7_enabled</key>
        <status>0x93</status>
        <midino>0x60</midino>
        <minimum>0.1</minimum>
      </output>
      <output>
        <group>[Channel4]</group>
        <key>hotcue_8_enabled</key>
        <status>0x93</status>
        <midino>0x61</midino>
        <minimum>0.1</minimum>
      </output>
    </outputs>
  </controller>
</MixxxMIDIPreset>
