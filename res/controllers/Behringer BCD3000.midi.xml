<MixxxMIDIPreset mixxxVersion="1.8.0+" schemaVersion="1">
    <info>
        <name><PERSON><PERSON>inger BCD3000</name>
        <author>Aposto</author>
        <description>MIDI Mapping for <PERSON><PERSON>inger BCD3000</description>
        <forums>https://mixxx.discourse.group/t/behringer-bcd3000/10194</forums>
    </info>
    <controller id="BCD3000 MIDI 1">
        <scriptfiles>
            <file functionprefix="BehringerBCD3000" filename="Behringer-BCD3000-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>cue_set</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xa</midino>
                <group>[Channel2]</group>
                <key>cue_set</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xb</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xc</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>BehringerBCD3000.loop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>BehringerBCD3000.scratchButton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x14</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel2]</group>
                <key>BehringerBCD3000.loop</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>BehringerBCD3000.scratchButton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xa</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x20</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x21</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xf</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Master]</group>
                <key>balance</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x23</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x11</midino>
                <group>[Master]</group>
                <key>headMix</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel2]</group>
                <key>BehringerBCD3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>BehringerBCD3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x10</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x16</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xd</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x17</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x2</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x11</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x1</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xf</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xe</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x9</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xa</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x18</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x4</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x12</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x15</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x5</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
        </outputs>
    </controller>
</MixxxMIDIPreset>
