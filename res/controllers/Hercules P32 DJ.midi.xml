<?xml version='1.0' encoding='utf-8'?>
<MixxxControllerPreset mixxxVersion="2.1.0+" schemaVersion="1">
    <info>
        <name>Hercules P32 DJ</name>
        <author>Be</author>
        <description>4-deck mapping for Hercules P32 controller</description>
        <forums>https://mixxx.discourse.group/t/hercules-p32-dj-controller-mapping/15804</forums>
        <manual>hercules_p32_dj</manual>
    </info>
    <controller id="Hercules">
        <scriptfiles>
            <file filename="lodash.mixxx.js"/>
            <file filename="midi-components-0.0.js"/>
            <file functionprefix="P32" filename="Hercules-P32-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <group>[Skin]</group>
                <key>show_maximized_library</key>
                <status>0x90</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>headSplit</key>
                <status>0x93</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>P32.headMixEncoder</key>
                <status>0xB3</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.leftEncoder.input</key>
                <status>0xB1</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.leftEncoder.input</key>
                <status>0xB4</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.leftEncoderPress.input</key>
                <status>0x91</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.leftEncoderPress.input</key>
                <status>0x94</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.rightEncoder.input</key>
                <status>0xB1</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.rightEncoder.input</key>
                <status>0xB4</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.rightEncoderPress.input</key>
                <status>0x91</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.rightEncoderPress.input</key>
                <status>0x94</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loopIn.input</key>
                <status>0x91</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loopOut.input</key>
                <status>0x91</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.reloop.input</key>
                <status>0x91</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loopIn.input</key>
                <status>0x94</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loopOut.input</key>
                <status>0x94</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.reloop.input</key>
                <status>0x94</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[1].input</key>
                <status>0x91</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[2].input</key>
                <status>0x91</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[3].input</key>
                <status>0x91</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[4].input</key>
                <status>0x91</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[5].input</key>
                <status>0x91</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[6].input</key>
                <status>0x91</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[7].input</key>
                <status>0x91</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[8].input</key>
                <status>0x91</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[9].input</key>
                <status>0x91</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[10].input</key>
                <status>0x91</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[11].input</key>
                <status>0x91</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[12].input</key>
                <status>0x91</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[13].input</key>
                <status>0x91</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[14].input</key>
                <status>0x91</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[15].input</key>
                <status>0x91</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[16].input</key>
                <status>0x91</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[1].input</key>
                <status>0x94</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[2].input</key>
                <status>0x94</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[3].input</key>
                <status>0x94</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[4].input</key>
                <status>0x94</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[5].input</key>
                <status>0x94</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[6].input</key>
                <status>0x94</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[7].input</key>
                <status>0x94</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[8].input</key>
                <status>0x94</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[9].input</key>
                <status>0x94</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[10].input</key>
                <status>0x94</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[11].input</key>
                <status>0x94</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[12].input</key>
                <status>0x94</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[13].input</key>
                <status>0x94</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[14].input</key>
                <status>0x94</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[15].input</key>
                <status>0x94</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.hotcueButton[16].input</key>
                <status>0x94</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[1].input</key>
                <status>0xB1</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[2].input</key>
                <status>0xB1</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[3].input</key>
                <status>0xB1</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- shifted parameter knobs -->
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[1].input</key>
                <status>0xB4</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[2].input</key>
                <status>0xB4</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.knobs[3].input</key>
                <status>0xB4</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.dryWetKnobOrPregain.input</key>
                <status>0xB1</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.dryWetKnobOrPregain.input</key>
                <status>0xB4</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[1].input</key>
                <status>0x91</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[2].input</key>
                <status>0x91</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[3].input</key>
                <status>0x91</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.effectFocusButton.input</key>
                <status>0x91</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[1].input</key>
                <status>0x94</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[2].input</key>
                <status>0x94</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.enableButtons[3].input</key>
                <status>0x94</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.effectUnit.effectFocusButton.input</key>
                <status>0x94</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.play.input</key>
                <status>0x91</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.cue.input</key>
                <status>0x91</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.cue.input</key>
                <status>0x94</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.play.input</key>
                <status>0x94</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.sync.input</key>
                <status>0x91</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.sync.input</key>
                <status>0x94</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.tempSlow.input</key>
                <status>0x91</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.tempSlow.input</key>
                <status>0x94</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.tempFast.input</key>
                <status>0x91</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.tempFast.input</key>
                <status>0x94</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.alignBeats.input</key>
                <status>0x91</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.alignBeats.input</key>
                <status>0x94</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
           <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.syncKey.input</key>
                <status>0x91</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.pitchUp.input</key>
                <status>0x91</status>
                <midino>0x4F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.pitchDown.input</key>
                <status>0x91</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.resetKey.input</key>
                <status>0x91</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[0].input</key>
                <status>0x91</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[0].input</key>
                <status>0x94</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[1].input</key>
                <status>0x91</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[1].input</key>
                <status>0x94</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[2].input</key>
                <status>0x94</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[2].input</key>
                <status>0x91</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[3].input</key>
                <status>0x91</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.enableEffectUnitButtons[3].input</key>
                <status>0x94</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.shiftButton</key>
                <status>0x91</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[3].input</key>
                <status>0xB1</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[2].input</key>
                <status>0xB1</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[3].input</key>
                <status>0xB4</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Playlist]</group>
                <key>P32.browseEncoder</key>
                <status>0xB0</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.pfl.input</key>
                <status>0x91</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[1].input</key>
                <status>0xB1</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[2].input</key>
                <status>0xB4</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loadTrack.input</key>
                <status>0x91</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.loadTrack.input</key>
                <status>0x94</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.volume.input</key>
                <status>0xB1</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.eqKnob[1].input</key>
                <status>0xB4</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Recording]</group>
                <key>P32.recordButton.input</key>
                <status>0x90</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.leftEncoder.input</key>
                <status>0xB2</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.leftEncoder.input</key>
                <status>0xB5</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.leftEncoderPress.input</key>
                <status>0x92</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.leftEncoderPress.input</key>
                <status>0x95</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.rightEncoder.input</key>
                <status>0xB2</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.rightEncoder.input</key>
                <status>0xB5</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.rightEncoderPress.input</key>
                <status>0x92</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.rightEncoderPress.input</key>
                <status>0x95</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Recording]</group>
                <key>P32.slipButton.input</key>
                <status>0x90</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.volume.input</key>
                <status>0xB4</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.play.input</key>
                <status>0x92</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.cue.input</key>
                <status>0x92</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.cue.input</key>
                <status>0x95</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.play.input</key>
                <status>0x95</status>
                <midino>0x0A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.sync.input</key>
                <status>0x92</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.sync.input</key>
                <status>0x95</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.shiftButton</key>
                <status>0x92</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[3].input</key>
                <status>0xB2</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[2].input</key>
                <status>0xB2</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[3].input</key>
                <status>0xB5</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.pfl.input</key>
                <status>0x92</status>
                <midino>0x10</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[1].input</key>
                <status>0xB2</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[2].input</key>
                <status>0xB5</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Master]</group>
                <key>crossfader</key>
                <status>0xB0</status>
                <midino>0x01</midino>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loadTrack.input</key>
                <status>0x92</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loadTrack.input</key>
                <status>0x95</status>
                <midino>0x0F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.volume.input</key>
                <status>0xB2</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.eqKnob[1].input</key>
                <status>0xB5</status>
                <midino>0x02</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.volume.input</key>
                <status>0xB5</status>
                <midino>0x01</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[1].input</key>
                <status>0x92</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[2].input</key>
                <status>0x92</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[3].input</key>
                <status>0x92</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[4].input</key>
                <status>0x92</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[5].input</key>
                <status>0x92</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[6].input</key>
                <status>0x92</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[7].input</key>
                <status>0x92</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[8].input</key>
                <status>0x92</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[9].input</key>
                <status>0x92</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[10].input</key>
                <status>0x92</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[11].input</key>
                <status>0x92</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[12].input</key>
                <status>0x92</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[13].input</key>
                <status>0x92</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[14].input</key>
                <status>0x92</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[15].input</key>
                <status>0x92</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[16].input</key>
                <status>0x92</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[1].input</key>
                <status>0x95</status>
                <midino>0x60</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[2].input</key>
                <status>0x95</status>
                <midino>0x61</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[3].input</key>
                <status>0x95</status>
                <midino>0x62</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[4].input</key>
                <status>0x95</status>
                <midino>0x63</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[5].input</key>
                <status>0x95</status>
                <midino>0x5C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[6].input</key>
                <status>0x95</status>
                <midino>0x5D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[7].input</key>
                <status>0x95</status>
                <midino>0x5E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[8].input</key>
                <status>0x95</status>
                <midino>0x5F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[9].input</key>
                <status>0x95</status>
                <midino>0x58</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[10].input</key>
                <status>0x95</status>
                <midino>0x59</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[11].input</key>
                <status>0x95</status>
                <midino>0x5A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[12].input</key>
                <status>0x95</status>
                <midino>0x5B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[13].input</key>
                <status>0x95</status>
                <midino>0x54</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[14].input</key>
                <status>0x95</status>
                <midino>0x55</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[15].input</key>
                <status>0x95</status>
                <midino>0x56</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.hotcueButton[16].input</key>
                <status>0x95</status>
                <midino>0x57</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[1].input</key>
                <status>0xB2</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[2].input</key>
                <status>0xB2</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[3].input</key>
                <status>0xB2</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <!-- shifted parameter knobs -->
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[1].input</key>
                <status>0xB5</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[2].input</key>
                <status>0xB5</status>
                <midino>0x07</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.knobs[3].input</key>
                <status>0xB5</status>
                <midino>0x08</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.dryWetKnobOrPregain.input</key>
                <status>0xB2</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.dryWetKnobOrPregain.input</key>
                <status>0xB5</status>
                <midino>0x09</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[1].input</key>
                <status>0x92</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[2].input</key>
                <status>0x92</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[3].input</key>
                <status>0x92</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.effectFocusButton.input</key>
                <status>0x92</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[1].input</key>
                <status>0x95</status>
                <midino>0x03</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[2].input</key>
                <status>0x95</status>
                <midino>0x04</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.enableButtons[3].input</key>
                <status>0x95</status>
                <midino>0x05</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.effectUnit.effectFocusButton.input</key>
                <status>0x95</status>
                <midino>0x06</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[1].input</key>
                <status>0x91</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[2].input</key>
                <status>0x91</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[3].input</key>
                <status>0x91</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[4].input</key>
                <status>0x91</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[9].input</key>
                <status>0x91</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[10].input</key>
                <status>0x91</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[11].input</key>
                <status>0x91</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[12].input</key>
                <status>0x91</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[17].input</key>
                <status>0x91</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[18].input</key>
                <status>0x91</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[19].input</key>
                <status>0x91</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[20].input</key>
                <status>0x91</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[25].input</key>
                <status>0x91</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[26].input</key>
                <status>0x91</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[27].input</key>
                <status>0x91</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[28].input</key>
                <status>0x91</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[1].input</key>
                <status>0x94</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[2].input</key>
                <status>0x94</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[3].input</key>
                <status>0x94</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[4].input</key>
                <status>0x94</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[9].input</key>
                <status>0x94</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[10].input</key>
                <status>0x94</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[11].input</key>
                <status>0x94</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[12].input</key>
                <status>0x94</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[17].input</key>
                <status>0x94</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[18].input</key>
                <status>0x94</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[19].input</key>
                <status>0x94</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[20].input</key>
                <status>0x94</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[25].input</key>
                <status>0x94</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[26].input</key>
                <status>0x94</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[27].input</key>
                <status>0x94</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.leftDeck.samplerButton[28].input</key>
                <status>0x94</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[5].input</key>
                <status>0x92</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[6].input</key>
                <status>0x92</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[7].input</key>
                <status>0x92</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[8].input</key>
                <status>0x92</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[13].input</key>
                <status>0x92</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[14].input</key>
                <status>0x92</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[15].input</key>
                <status>0x92</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[16].input</key>
                <status>0x92</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[21].input</key>
                <status>0x92</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[22].input</key>
                <status>0x92</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[23].input</key>
                <status>0x92</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[24].input</key>
                <status>0x92</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[29].input</key>
                <status>0x92</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[30].input</key>
                <status>0x92</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[31].input</key>
                <status>0x92</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[32].input</key>
                <status>0x92</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[5].input</key>
                <status>0x95</status>
                <midino>0x30</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[6].input</key>
                <status>0x95</status>
                <midino>0x31</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[7].input</key>
                <status>0x95</status>
                <midino>0x32</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[8].input</key>
                <status>0x95</status>
                <midino>0x33</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[13].input</key>
                <status>0x95</status>
                <midino>0x2C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[14].input</key>
                <status>0x95</status>
                <midino>0x2D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[15].input</key>
                <status>0x95</status>
                <midino>0x2E</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[16].input</key>
                <status>0x95</status>
                <midino>0x2F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[21].input</key>
                <status>0x95</status>
                <midino>0x28</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[22].input</key>
                <status>0x95</status>
                <midino>0x29</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[23].input</key>
                <status>0x95</status>
                <midino>0x2A</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[24].input</key>
                <status>0x95</status>
                <midino>0x2B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[29].input</key>
                <status>0x95</status>
                <midino>0x24</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[30].input</key>
                <status>0x95</status>
                <midino>0x25</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[31].input</key>
                <status>0x95</status>
                <midino>0x26</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.samplerButton[32].input</key>
                <status>0x95</status>
                <midino>0x27</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loopIn.input</key>
                <status>0x92</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loopOut.input</key>
                <status>0x92</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.reloop.input</key>
                <status>0x92</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loopIn.input</key>
                <status>0x95</status>
                <midino>0x50</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.loopOut.input</key>
                <status>0x95</status>
                <midino>0x51</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.reloop.input</key>
                <status>0x95</status>
                <midino>0x52</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.tempSlow.input</key>
                <status>0x92</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.tempSlow.input</key>
                <status>0x95</status>
                <midino>0x44</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.tempFast.input</key>
                <status>0x92</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.tempFast.input</key>
                <status>0x95</status>
                <midino>0x45</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.alignBeats.input</key>
                <status>0x92</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.alignBeats.input</key>
                <status>0x95</status>
                <midino>0x46</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
           <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.syncKey.input</key>
                <status>0x92</status>
                <midino>0x53</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.pitchUp.input</key>
                <status>0x92</status>
                <midino>0x4F</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.pitchDown.input</key>
                <status>0x92</status>
                <midino>0x4B</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.resetKey.input</key>
                <status>0x92</status>
                <midino>0x47</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[0].input</key>
                <status>0x92</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[0].input</key>
                <status>0x95</status>
                <midino>0x40</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[1].input</key>
                <status>0x92</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[1].input</key>
                <status>0x95</status>
                <midino>0x41</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[2].input</key>
                <status>0x92</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[2].input</key>
                <status>0x95</status>
                <midino>0x3C</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[3].input</key>
                <status>0x92</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <group>[Channel1]</group>
                <key>P32.rightDeck.enableEffectUnitButtons[3].input</key>
                <status>0x95</status>
                <midino>0x3D</midino>
                <options>
                    <script-binding/>
                </options>
            </control>
        </controls>
        <outputs/>
    </controller>
</MixxxControllerPreset>
