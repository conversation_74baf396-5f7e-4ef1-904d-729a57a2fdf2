<MixxxMIDIPreset mixxxVersion="1.10.0+" schemaVersion="1">
    <info>
      <name><PERSON><PERSON>inger BCD3000 Advanced</name>
      <author>Joachim</author>
      <description>Advanced preset for BCD3000. See forums for more information.</description>
      <forums>http://www.mixxx.org/forums/viewtopic.php?f=7&amp;t=4013</forums>
      <manual>behringer_b_control_deejay_bcd3000</manual>
    </info>
    <controller id="BCD3000 MIDI 1">
        <scriptfiles>
            <file functionprefix="BehringerBCD3000" filename="Behringer-BCD3000-Advanced-scripts.js"/>
        </scriptfiles>
        <controls>
            <control>
                <status>0x90</status>
                <midino>0x2</midino>
                <group>[Channel1]</group>
                <key>rate_temp_down</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>rate_temp_up</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>rate_temp_down</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>rate_temp_up</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x11</midino>
                <group>[Channel1]</group>
                <key>BehringerBCD3000.scratchButton</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x19</midino>
                <group>[Channel2]</group>
                <key>BehringerBCD3000.scratchButton</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0xb0</status>
                <midino>0x12</midino>
                <group>[Channel2]</group>
                <key>BehringerBCD3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>BehringerBCD3000.jogWheel</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0xc</midino>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xd</midino>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xe</midino>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <button/>
                </options>
            </control>

            <control>
                <status>0x90</status>
                <midino>0x14</midino>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x15</midino>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <button/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x16</midino>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <button/>
                </options>
            </control>

            <control>
                <status>0x80</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x6</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1</midino>
                <group>[Channel1]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xa</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xb</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x6</midino>
                <group>[Channel2]</group>
                <key>back</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>fwd</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xa</midino>
                <group>[Channel2]</group>
                <key>loop_in</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xb</midino>
                <group>[Channel2]</group>
                <key>reloop_exit</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x17</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0xf</midino>
                <group>[Channel1]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x10</midino>
                <group>[Channel1]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x1b</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x0</midino>
                <group>[Channel1]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x12</midino>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x1</midino>
                <group>[Master]</group>
                <key>crossfader</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x10</midino>
                <group>[Master]</group>
                <key>balance</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x13</midino>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x2</midino>
                <group>[Channel2]</group>
                <key>volume</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x3</midino>
                <group>[Channel1]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x4</midino>
                <group>[Channel1]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x20</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x5</midino>
                <group>[Channel1]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x17</midino>
                <group>[Channel2]</group>
                <key>loop_out</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x21</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x6</midino>
                <group>[Channel1]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x7</midino>
                <group>[Channel2]</group>
                <key>filterLow</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x23</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1a</midino>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x8</midino>
                <group>[Channel2]</group>
                <key>filterMid</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x80</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0x9</midino>
                <group>[Channel2]</group>
                <key>filterHigh</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1b</midino>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xa</midino>
                <group>[Channel2]</group>
                <key>pregain</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1c</midino>
                <group>[Master]</group>
                <key>BehringerBCD3000.keykey</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1d</midino>
                <group>[Channel1]</group>
                <key>bpm_tap</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1e</midino>
                <group>[Channel2]</group>
                <key>bpm_tap</key>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xb</midino>
                <group>[Channel1]</group>
                <key>rate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xc</midino>
                <group>[Channel2]</group>
                <key>rate</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x1f</midino>
        <group>[Channel1]</group>
                <key>BehringerBCD3000.On</key>
                <options>
                    <script-binding/>
                </options>
            </control>
            <control>
                <status>0x90</status>
        <group>[Channel2]</group>
                <midino>0x22</midino>
                <key>BehringerBCD3000.Action</key>
                <options>
                    <script-binding/>
                </options>
            </control>

            <control>
                <status>0xb0</status>
                <midino>0xd</midino>
                <group>[Flanger]</group>
                <key>lfoPeriod</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x20</midino>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xe</midino>
                <group>[Flanger]</group>
                <key>lfoDepth</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x21</midino>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0xb0</status>
                <midino>0xf</midino>
                <group>[Flanger]</group>
                <key>lfoDelay</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x18</midino>
                <group>[Channel2]</group>
                <key>beatsync</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x23</midino>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
            <control>
                <status>0x90</status>
                <midino>0x24</midino>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
            </control>
        </controls>
        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x10</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x16</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xd</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x17</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x2</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x11</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x1</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xf</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xe</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x9</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xa</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x18</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x4</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x12</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x15</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <description></description>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x5</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>

        <outputs>
            <output>
                <group>[Channel2]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x10</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x16</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>loop_enabled</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xd</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x17</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x2</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x11</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>pfl</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x1</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterMidKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xf</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>filterHighKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xe</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>cue_default</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x9</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0xa</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>filterLowKill</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x18</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel2]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x4</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>play</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x12</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>loop_enabled</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x15</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
            <output>
                <group>[Channel1]</group>
                <key>flanger</key>
                <options>
                    <normal/>
                </options>
                <minimum>0.5</minimum>
                <maximum>1</maximum>
                <status>0xb0</status>
                <midino>0x5</midino>
                <on>0x7f</on>
                <off>0x0</off>
            </output>
        </outputs>


        </outputs>
    </controller>
</MixxxMIDIPreset>
