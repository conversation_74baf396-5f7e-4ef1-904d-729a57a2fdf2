<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="2.3.0+">
  <info>
    <name>Numark DJ2GO2 Touch</name>
    <author>tandy1000</author>
    <description>Numark DJ2GO2 Touch Mapping</description>
    <wiki/>
    <forums/>
  </info>
  <controller id="Numark DJ2GO2 Touch">
    <scriptfiles>
      <file filename="lodash.mixxx.js"/>
      <file filename="midi-components-0.0.js"/>
      <file functionprefix="DJ2GO2Touch" filename="Numark_DJ2GO2_Touch_scripts.js"/>
    </scriptfiles>
    <controls>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loadButton.input</key>
        <status>0x8F</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loadButton.input</key>
        <status>0x9F</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.wheelTouch</key>
        <status>0x80</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.wheelTouch</key>
        <status>0x90</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.wheelTurn</key>
        <status>0xB0</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control> -->
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[1].input</key>
        <status>0x84</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[1].input</key>
        <status>0x94</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[2].input</key>
        <status>0x84</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[2].input</key>
        <status>0x94</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[3].input</key>
        <status>0x84</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[3].input</key>
        <status>0x94</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[4].input</key>
        <status>0x84</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.beatloopButtons[4].input</key>
        <status>0x94</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.cueButton.input</key>
        <status>0x80</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.cueButton.input</key>
        <status>0x90</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[1].input</key>
        <status>0x84</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[1].input</key>
        <status>0x94</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[2].input</key>
        <status>0x84</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[2].input</key>
        <status>0x94</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[3].input</key>
        <status>0x84</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[3].input</key>
        <status>0x94</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[4].input</key>
        <status>0x84</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[4].input</key>
        <status>0x94</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[1].input</key>
        <status>0x84</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[1].input</key>
        <status>0x94</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[2].input</key>
        <status>0x84</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[2].input</key>
        <status>0x94</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[3].input</key>
        <status>0x84</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[3].input</key>
        <status>0x94</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[4].input</key>
        <status>0x84</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.hotcueButtons[4].input</key>
        <status>0x94</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loopIn.input</key>
        <status>0x84</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loopIn.input</key>
        <status>0x94</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loopOut.input</key>
        <status>0x84</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.loopOut.input</key>
        <status>0x94</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.reLoopStop.input</key>
        <status>0x84</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.reLoopStop.input</key>
        <status>0x94</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.pflButton.input</key>
        <status>0x80</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.pflButton.input</key>
        <status>0x90</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.playButton.input</key>
        <status>0x80</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.playButton.input</key>
        <status>0x90</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.tempoFader.input</key>
        <status>0xB0</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.LoopToggleButton.input</key>
        <status>0x84</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.LoopToggleButton.input</key>
        <status>0x94</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel1]]</group>
        <key>DJ2GO2Touch.leftDeck.preGain.input</key>
        <status>0xB0</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.syncButton.input</key>
        <status>0x80</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.leftDeck.syncButton.input</key>
        <status>0x90</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loadButton.input</key>
        <status>0x8F</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loadButton.input</key>
        <status>0x9F</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.wheelTouch</key>
        <status>0x81</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.wheelTouch</key>
        <status>0x91</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.wheelTurn</key>
        <status>0xB1</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[1].input</key>
        <status>0x85</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[1].input</key>
        <status>0x95</status>
        <midino>0x11</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[2].input</key>
        <status>0x85</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[2].input</key>
        <status>0x95</status>
        <midino>0x12</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[3].input</key>
        <status>0x85</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[3].input</key>
        <status>0x95</status>
        <midino>0x13</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[4].input</key>
        <status>0x85</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel1]</group>
        <key>DJ2GO2Touch.rightDeck.beatloopButtons[4].input</key>
        <status>0x95</status>
        <midino>0x14</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.cueButton.input</key>
        <status>0x81</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.cueButton.input</key>
        <status>0x91</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[1].input</key>
        <status>0x85</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[1].input</key>
        <status>0x95</status>
        <midino>0x01</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[2].input</key>
        <status>0x85</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[2].input</key>
        <status>0x95</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[3].input</key>
        <status>0x85</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[3].input</key>
        <status>0x95</status>
        <midino>0x03</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[4].input</key>
        <status>0x85</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[4].input</key>
        <status>0x95</status>
        <midino>0x04</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[1].input</key>
        <status>0x85</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[1].input</key>
        <status>0x95</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[2].input</key>
        <status>0x85</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[2].input</key>
        <status>0x95</status>
        <midino>0x0A</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[3].input</key>
        <status>0x85</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[3].input</key>
        <status>0x95</status>
        <midino>0x0B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[4].input</key>
        <status>0x85</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.hotcueButtons[4].input</key>
        <status>0x95</status>
        <midino>0x0C</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loopIn.input</key>
        <status>0x85</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loopIn.input</key>
        <status>0x95</status>
        <midino>0x21</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loopOut.input</key>
        <status>0x85</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.loopOut.input</key>
        <status>0x95</status>
        <midino>0x22</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.reLoopStop.input</key>
        <status>0x95</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.reLoopStop.input</key>
        <status>0x95</status>
        <midino>0x24</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.pflButton.input</key>
        <status>0x81</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.pflButton.input</key>
        <status>0x91</status>
        <midino>0x1B</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.playButton.input</key>
        <status>0x81</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.playButton.input</key>
        <status>0x91</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.tempoFader.input</key>
        <status>0xB1</status>
        <midino>0x09</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.LoopToggleButton.input</key>
        <status>0x85</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.LoopToggleButton.input</key>
        <status>0x95</status>
        <midino>0x23</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[QuickEffectRack1_[Channel2]]</group>
        <key>DJ2GO2Touch.rightDeck.preGain.input</key>
        <status>0xB1</status>
        <midino>0x16</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.syncButton.input</key>
        <status>0x81</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Channel2]</group>
        <key>DJ2GO2Touch.rightDeck.syncButton.input</key>
        <status>0x91</status>
        <midino>0x02</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>DJ2GO2Touch.browseEncoder.input</key>
        <description>BROWSER KNOB</description>
        <status>0xBF</status>
        <midino>0x00</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>DJ2GO2Touch.browseEncoder.input</key>
        <description>BROWSER KNOB PRESS</description>
        <status>0x8F</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Library]</group>
        <key>DJ2GO2Touch.browseEncoder.input</key>
        <description>BROWSER KNOB PRESS</description>
        <status>0x9F</status>
        <midino>0x06</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>crossfader</key>
        <status>0xBF</status>
        <midino>0x08</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>gain</key>
        <status>0xBF</status>
        <midino>0x0A</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Master]</group>
        <key>headGain</key>
        <status>0xBF</status>
        <midino>0x0C</midino>
        <options>
          <normal/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[1].input</key>
        <status>0x84</status>
        <midino>0x31</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler1]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[1].input</key>
        <status>0x94</status>
        <midino>0x31</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[2].input</key>
        <status>0x84</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler2]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[2].input</key>
        <status>0x94</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[3].input</key>
        <status>0x84</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler3]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[3].input</key>
        <status>0x94</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[4].input</key>
        <status>0x84</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler4]</group>
        <key>DJ2GO2Touch.leftDeck.samplerButtons[4].input</key>
        <status>0x94</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[1].input</key>
        <status>0x85</status>
        <midino>0x31</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler5]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[1].input</key>
        <status>0x95</status>
        <midino>0x31</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[2].input</key>
        <status>0x85</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler6]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[2].input</key>
        <status>0x95</status>
        <midino>0x32</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[3].input</key>
        <status>0x85</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler7]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[3].input</key>
        <status>0x95</status>
        <midino>0x33</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[4].input</key>
        <status>0x85</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
      <control>
        <group>[Sampler8]</group>
        <key>DJ2GO2Touch.rightDeck.samplerButtons[4].input</key>
        <status>0x95</status>
        <midino>0x34</midino>
        <options>
          <script-binding/>
        </options>
      </control>
    </controls>
    <outputs/>
  </controller>
</MixxxMIDIPreset>
