<?xml version="1.0" encoding="utf-8"?>
<MixxxMIDIPreset schemaVersion="1" mixxxVersion="2.0.0">
   <info>
      <name>American Audio VMS4/4.1</name>
      <author><PERSON> &amp; <PERSON></author>
      <description>2-deck control/4-deck mixer preset for Mixxx 2.1.
Assumes "Post EQ" mode. (See Wiki for full setup instructions.)</description>
      <manual>american_audio_vms4</manual>
   </info>
   <controller id="American Audio VMS4">
      <scriptfiles>
         <file filename="American-Audio-VMS4-scripts.js" functionprefix="VMS4"/>
      </scriptfiles>
      <controls>
         <!-- Deck A -->
         <control>
            <group>[Channel1]</group>
            <key>VMS4.pitch</key>
            <status>0xE0</status>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.pitchCenter</key>
            <status>0x90</status>
            <midino>0x51</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.pitchCenter</key>
            <status>0x80</status>
            <midino>0x51</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <!-- Temp pitch bend -->
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>rate_temp_down</key>
            <status>0x90</status>
            <midino>0x0F</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>rate_temp_down</key>
            <status>0x80</status>
            <midino>0x0F</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>rate_temp_up</key>
            <status>0x90</status>
            <midino>0x10</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>rate_temp_up</key>
            <status>0x80</status>
            <midino>0x10</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
             <group>[Channel1]</group>
             <key>rate_temp_down</key>
             <status>0x90</status>
             <midino>0x0F</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel1]</group>
             <key>rate_temp_down</key>
             <status>0x80</status>
             <midino>0x0F</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <control><!-- on -->
             <group>[Channel1]</group>
             <key>rate_temp_up</key>
             <status>0x90</status>
             <midino>0x10</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel1]</group>
             <key>rate_temp_up</key>
             <status>0x80</status>
             <midino>0x10</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <!-- Key adjust -->
         <control><!-- on -->
             <group>[Channel1]</group>
             <key>pitch_down_small</key>
             <status>0x91</status>
             <midino>0x0F</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel1]</group>
             <key>pitch_down_small</key>
             <status>0x81</status>
             <midino>0x0F</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <control><!-- on -->
             <group>[Channel1]</group>
             <key>pitch_up_small</key>
             <status>0x91</status>
             <midino>0x10</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel1]</group>
             <key>pitch_up_small</key>
             <status>0x81</status>
             <midino>0x10</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>sync_enabled</key>
            <status>0x90</status>
            <midino>0x11</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>sync_enabled</key>
            <status>0x80</status>
            <midino>0x11</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[EffectRack1_EffectUnit1]</group>
            <key>group_[Channel1]_enable</key>
            <status>0x90</status>
            <midino>0x1B</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
             <group>[EffectRack1_EffectUnit1]</group>
             <key>group_[Channel1]_enable</key>
            <status>0x80</status>
            <midino>0x1B</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- Effect select knob L -->
            <group>[Channel1]</group>
            <key>VMS4.effectSelect</key>
            <status>0xB0</status>
            <midino>0x24</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect select knob press -->
            <group>[Channel1]</group>
            <key>VMS4.effectSelectPress</key>
            <status>0x90</status>
            <midino>0x53</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect select knob release -->
            <group>[Channel1]</group>
            <key>VMS4.effectSelectPress</key>
            <status>0x80</status>
            <midino>0x53</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect control knob -->
            <group>[Channel1]</group>
            <key>VMS4.effectControl</key>
            <status>0xB0</status>
            <midino>0x25</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect parameter button press -->
             <group>[Channel1]</group>
             <key>VMS4.effectParameterButton</key>
             <status>0x90</status>
             <midino>0x1C</midino>
             <options>
                 <Script-Binding/>
             </options>
         </control>
         <control><!-- Effect parameter button release -->
             <group>[Channel1]</group>
             <key>VMS4.effectParameterButton</key>
             <status>0x80</status>
             <midino>0x1C</midino>
             <options>
                 <Script-Binding/>
             </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.rate_range</key>
            <status>0x90</status>
            <midino>0x16</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.rate_range</key>
            <status>0x80</status>
            <midino>0x16</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>cue_default</key>
            <status>0x90</status>
            <midino>0x0C</midino>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>cue_default</key>
            <status>0x80</status>
            <midino>0x0C</midino>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>cue_gotoandplay</key>
            <status>0x91</status>
            <midino>0x0C</midino>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>cue_gotoandplay</key>
            <status>0x81</status>
            <midino>0x0C</midino>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>play</key>
            <status>0x90</status>
            <midino>0x0D</midino>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>play</key>
            <status>0x80</status>
            <midino>0x0D</midino>
         </control>
         <control><!-- Shifted Play -->
            <group>[Channel1]</group>
            <key>sync_leader</key>
            <status>0x91</status>
            <midino>0x0D</midino>
         </control>
         <control><!-- Shifted Play -->
            <group>[Channel1]</group>
            <key>sync_leader</key>
            <status>0x81</status>
            <midino>0x0D</midino>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>stop</key>
            <status>0x90</status>
            <midino>0x0E</midino>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>stop</key>
            <status>0x80</status>
            <midino>0x0E</midino>
         </control>
         <!-- Hot Cues, deck 1 -->
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x12</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x12</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x13</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x13</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x14</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x14</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x15</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x15</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x17</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x17</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x18</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x18</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x19</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x19</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x1A</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x1A</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>loop_in</key>
            <status>0x90</status>
            <midino>0x1F</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>loop_in</key>
            <status>0x80</status>
            <midino>0x1F</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>loop_out</key>
            <status>0x90</status>
            <midino>0x20</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>loop_out</key>
            <status>0x80</status>
            <midino>0x20</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>reloop_toggle</key>
            <status>0x90</status>
            <midino>0x21</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>reloop_toggle</key>
            <status>0x80</status>
            <midino>0x21</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>beatloop_4</key>
            <status>0x90</status>
            <midino>0x22</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>quantize</key>
            <status>0x90</status>
            <midino>0x26</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>quantize</key>
            <status>0x80</status>
            <midino>0x26</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>back</key>
            <status>0x90</status>
            <midino>0x28</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>back</key>
            <status>0x80</status>
            <midino>0x28</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>fwd</key>
            <status>0x90</status>
            <midino>0x29</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>fwd</key>
            <status>0x80</status>
            <midino>0x29</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>loop_halve</key>
            <status>0x91</status>
            <midino>0x28</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>loop_halve</key>
            <status>0x81</status>
            <midino>0x28</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>loop_double</key>
            <status>0x91</status>
            <midino>0x29</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>loop_double</key>
            <status>0x81</status>
            <midino>0x29</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.keylock</key>
            <status>0x91</status>
            <midino>0x27</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.keylock</key>
            <status>0x81</status>
            <midino>0x27</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.vinyl</key>
            <status>0x90</status>
            <midino>0x27</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.vinyl</key>
            <status>0x80</status>
            <midino>0x27</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>LoadSelectedTrack</key>
            <status>0x90</status>
            <midino>0x65</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>LoadSelectedTrack</key>
            <status>0x80</status>
            <midino>0x65</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>VMS4.jog_touch</key>
            <status>0x90</status>
            <midino>0x4F</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>VMS4.jog_touch</key>
            <status>0x80</status>
            <midino>0x4F</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Channel1]</group>
            <key>VMS4.jog_move_lsb</key>
            <status>0xB0</status>
            <midino>0x30</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Channel1]</group>
            <key>VMS4.jog_move_msb</key>
            <status>0xB0</status>
            <midino>0x31</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Library]</group>
            <key>VMS4.strip_scroll</key>
            <status>0xB0</status>
            <midino>0x28</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>  <!-- Shifted -->
            <group>[Channel1]</group>
            <key>VMS4.strip_fx_dw</key>
            <status>0xB1</status>
            <midino>0x28</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel1]</group>
            <key>VMS4.strip_touch</key>
            <status>0x90</status>
            <midino>0x57</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel1]</group>
            <key>VMS4.strip_touch</key>
            <status>0x80</status>
            <midino>0x57</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control> <!-- Shifted -->
            <group>[Channel1]</group>
            <key>VMS4.strip_touch</key>
            <status>0x91</status>
            <midino>0x57</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel1]</group>
            <key>VMS4.strip_touch</key>
            <status>0x81</status>
            <midino>0x57</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>

         <!-- Deck B -->
         <control>
            <group>[Channel2]</group>
            <key>VMS4.pitch</key>
            <status>0xE1</status>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.pitchCenter</key>
            <status>0x90</status>
            <midino>0x52</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.pitchCenter</key>
            <status>0x80</status>
            <midino>0x52</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>rate_temp_down</key>
            <status>0x90</status>
            <midino>0x31</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>rate_temp_down</key>
            <status>0x80</status>
            <midino>0x31</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>rate_temp_up</key>
            <status>0x90</status>
            <midino>0x32</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>rate_temp_up</key>
            <status>0x80</status>
            <midino>0x32</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <!-- Key adjust -->
         <control><!-- on -->
             <group>[Channel2]</group>
             <key>pitch_down_small</key>
             <status>0x91</status>
             <midino>0x31</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel2]</group>
             <key>pitch_down_small</key>
             <status>0x81</status>
             <midino>0x31</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <control><!-- on -->
             <group>[Channel2]</group>
             <key>pitch_up_small</key>
             <status>0x91</status>
             <midino>0x32</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel2]</group>
             <key>pitch_up_small</key>
             <status>0x81</status>
             <midino>0x32</midino>
             <options>
                 <Spread64 />
             </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>sync_enabled</key>
            <status>0x90</status>
            <midino>0x33</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>sync_enabled</key>
            <status>0x80</status>
            <midino>0x33</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[EffectRack1_EffectUnit2]</group>
            <key>group_[Channel2]_enable</key>
            <status>0x90</status>
            <midino>0x3D</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[EffectRack1_EffectUnit2]</group>
            <key>group_[Channel2]_enable</key>
            <status>0x80</status>
            <midino>0x3D</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- Effect select knob R -->
            <group>[Channel2]</group>
            <key>VMS4.effectSelect</key>
            <status>0xB0</status>
            <midino>0x29</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect select knob press -->
            <group>[Channel2]</group>
            <key>VMS4.effectSelectPress</key>
            <status>0x90</status>
            <midino>0x55</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect select knob release -->
            <group>[Channel2]</group>
            <key>VMS4.effectSelectPress</key>
            <status>0x80</status>
            <midino>0x55</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect control knob -->
            <group>[Channel2]</group>
            <key>VMS4.effectControl</key>
            <status>0xB0</status>
            <midino>0x2A</midino>
            <options>
                <Script-Binding/>
            </options>
         </control>
         <control><!-- Effect parameter button press -->
             <group>[Channel2]</group>
             <key>VMS4.effectParameterButton</key>
             <status>0x90</status>
             <midino>0x3E</midino>
             <options>
                 <Script-Binding/>
             </options>
         </control>
         <control><!-- Effect parameter button release -->
             <group>[Channel2]</group>
             <key>VMS4.effectParameterButton</key>
             <status>0x80</status>
             <midino>0x3E</midino>
             <options>
                 <Script-Binding/>
             </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.rate_range</key>
            <status>0x90</status>
            <midino>0x38</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.rate_range</key>
            <status>0x80</status>
            <midino>0x38</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>cue_default</key>
            <status>0x90</status>
            <midino>0x2E</midino>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>cue_default</key>
            <status>0x80</status>
            <midino>0x2E</midino>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>cue_gotoandplay</key>
            <status>0x91</status>
            <midino>0x2E</midino>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>cue_gotoandplay</key>
            <status>0x81</status>
            <midino>0x2E</midino>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>play</key>
            <status>0x90</status>
            <midino>0x2F</midino>
         </control>
         <control><!-- Shifted Play -->
            <group>[Channel2]</group>
            <key>sync_leader</key>
            <status>0x91</status>
            <midino>0x2F</midino>
         </control>
         <control><!-- Shifted Play -->
            <group>[Channel2]</group>
            <key>sync_leader</key>
            <status>0x81</status>
            <midino>0x2F</midino>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>stop</key>
            <status>0x90</status>
            <midino>0x30</midino>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>stop</key>
            <status>0x80</status>
            <midino>0x30</midino>
         </control>
        <!-- Hot Cues, deck 2 -->
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x34</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x34</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x35</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x35</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x36</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x36</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x37</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x37</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x39</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x39</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x3A</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x3A</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x3B</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x3B</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x90</status>
            <midino>0x3C</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.hotCue</key>
            <status>0x80</status>
            <midino>0x3C</midino>
            <options>
               <Script-Binding />
            </options>
         </control>

         <control><!-- on -->
            <group>[Channel2]</group>
            <key>loop_in</key>
            <status>0x90</status>
            <midino>0x41</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>loop_in</key>
            <status>0x80</status>
            <midino>0x41</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>loop_out</key>
            <status>0x90</status>
            <midino>0x42</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>loop_out</key>
            <status>0x80</status>
            <midino>0x42</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>reloop_toggle</key>
            <status>0x90</status>
            <midino>0x43</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>reloop_toggle</key>
            <status>0x80</status>
            <midino>0x43</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>beatloop_4</key>
            <status>0x90</status>
            <midino>0x44</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>quantize</key>
            <status>0x90</status>
            <midino>0x48</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>quantize</key>
            <status>0x80</status>
            <midino>0x48</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>back</key>
            <status>0x90</status>
            <midino>0x4A</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>back</key>
            <status>0x80</status>
            <midino>0x4A</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>fwd</key>
            <status>0x90</status>
            <midino>0x4B</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>fwd</key>
            <status>0x80</status>
            <midino>0x4B</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>loop_halve</key>
            <status>0x91</status>
            <midino>0x4A</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>loop_halve</key>
            <status>0x81</status>
            <midino>0x4A</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>loop_double</key>
            <status>0x91</status>
            <midino>0x4B</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>loop_double</key>
            <status>0x81</status>
            <midino>0x4B</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.keylock</key>
            <status>0x91</status>
            <midino>0x49</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.keylock</key>
            <status>0x81</status>
            <midino>0x49</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.vinyl</key>
            <status>0x90</status>
            <midino>0x49</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.vinyl</key>
            <status>0x80</status>
            <midino>0x49</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>LoadSelectedTrack</key>
            <status>0x90</status>
            <midino>0x66</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>LoadSelectedTrack</key>
            <status>0x80</status>
            <midino>0x66</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>VMS4.jog_touch</key>
            <status>0x90</status>
            <midino>0x50</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>VMS4.jog_touch</key>
            <status>0x80</status>
            <midino>0x50</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Channel2]</group>
            <key>VMS4.jog_move_lsb</key>
            <status>0xB0</status>
            <midino>0x32</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Channel2]</group>
            <key>VMS4.jog_move_msb</key>
            <status>0xB0</status>
            <midino>0x33</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>
            <group>[Library]</group>
            <key>VMS4.strip_scroll</key>
            <status>0xB0</status>
            <midino>0x2D</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control>  <!-- Shifted -->
            <group>[Channel2]</group>
            <key>VMS4.strip_fx_dw</key>
            <status>0xB1</status>
            <midino>0x2D</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel2]</group>
            <key>VMS4.strip_touch</key>
            <status>0x90</status>
            <midino>0x58</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel2]</group>
            <key>VMS4.strip_touch</key>
            <status>0x80</status>
            <midino>0x58</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
         <control> <!-- Shifted -->
            <group>[Channel2]</group>
            <key>VMS4.strip_touch</key>
            <status>0x91</status>
            <midino>0x58</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>
        <control>
            <group>[Channel2]</group>
            <key>VMS4.strip_touch</key>
            <status>0x81</status>
            <midino>0x58</midino>
            <options>
               <Script-Binding/>
            </options>
         </control>

         <!-- Mixer main-->
         <control>
            <group>[Master]</group>
            <key>headMix</key>
            <status>0xB0</status>
            <midino>0x22</midino>
            <options>
                <soft-takeover/>
            </options>
         </control>
         <control>
            <group>[Master]</group>
            <key>headVolume</key>
            <status>0xB0</status>
            <midino>0x23</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[Master]</group>
            <key>volume</key>
            <status>0xB0</status>
            <midino>0x1C</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[Master]</group>
            <key>balance</key>
            <status>0xB0</status>
            <midino>0x1D</midino>
            <options>
                <soft-takeover />
            </options>
         </control>

         <!-- Mixer channel 1 -->
         <control>
            <group>[Channel1]</group>
            <key>pregain</key>
            <status>0xB0</status>
            <midino>0x05</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel1]_Effect1]</group>
            <key>parameter3</key>
            <status>0xB0</status>
            <midino>0x06</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel1]_Effect1]</group>
            <key>parameter2</key>
            <status>0xB0</status>
            <midino>0x07</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel1]_Effect1]</group>
            <key>parameter1</key>
            <status>0xB0</status>
            <midino>0x08</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel1]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2B</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel1]</group>
            <key>pfl</key>
            <status>0x80</status>
            <midino>0x2B</midino>
            <options>
               <Spread64 />
            </options>
         </control>

         <!-- Mixer channel 2 -->
         <control>
            <group>[Channel2]</group>
            <key>pregain</key>
            <status>0xB0</status>
            <midino>0x0A</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel2]_Effect1]</group>
            <key>parameter3</key>
            <status>0xB0</status>
            <midino>0x0B</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel2]_Effect1]</group>
            <key>parameter2</key>
            <status>0xB0</status>
            <midino>0x0C</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control>
            <group>[EqualizerRack1_[Channel2]_Effect1]</group>
            <key>parameter1</key>
            <status>0xB0</status>
            <midino>0x0D</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control><!-- on -->
            <group>[Channel2]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2C</midino>
            <options>
               <Button />
            </options>
         </control>
         <control><!-- off -->
            <group>[Channel2]</group>
            <key>pfl</key>
            <status>0x80</status>
            <midino>0x2C</midino>
            <options>
               <Spread64 />
            </options>
         </control>

         <!-- Mixer channel 3 -->
         <control>
             <group>[Channel3]</group>
             <key>pregain</key>
             <status>0xB0</status>
             <midino>0x00</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel3]_Effect1]</group>
             <key>parameter3</key>
             <status>0xB0</status>
             <midino>0x01</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel3]_Effect1]</group>
             <key>parameter2</key>
             <status>0xB0</status>
             <midino>0x02</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel3]_Effect1]</group>
             <key>parameter1</key>
             <status>0xB0</status>
             <midino>0x03</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control><!-- on -->
             <group>[Channel3]</group>
             <key>pfl</key>
             <status>0x90</status>
             <midino>0x2A</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel3]</group>
             <key>pfl</key>
             <status>0x80</status>
             <midino>0x2A</midino>
             <options>
                 <Spread64 />
             </options>
         </control>

         <!-- Mixer channel 4 -->
         <control>
             <group>[Channel4]</group>
             <key>pregain</key>
             <status>0xB0</status>
             <midino>0x0F</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel4]_Effect1]</group>
             <key>parameter3</key>
             <status>0xB0</status>
             <midino>0x10</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel4]_Effect1]</group>
             <key>parameter2</key>
             <status>0xB0</status>
             <midino>0x11</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control>
             <group>[EqualizerRack1_[Channel4]_Effect1]</group>
             <key>parameter1</key>
             <status>0xB0</status>
             <midino>0x12</midino>
             <options>
                 <soft-takeover />
             </options>
         </control>
         <control><!-- on -->
             <group>[Channel4]</group>
             <key>pfl</key>
             <status>0x90</status>
             <midino>0x2D</midino>
             <options>
                 <Button />
             </options>
         </control>
         <control><!-- off -->
             <group>[Channel4]</group>
             <key>pfl</key>
             <status>0x80</status>
             <midino>0x2D</midino>
             <options>
                 <Spread64 />
             </options>
         </control>

         <!-- Samplers -->
         <!-- 1 -->
         <control>
            <group>[Channel1]</group>
            <key>VMS4.sampleSelect</key>
            <status>0xB0</status>
            <midino>0x26</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control>
            <group>[Sampler1]</group>
            <key>LoadSelectedTrack</key>
            <status>0x90</status>
            <midino>0x54</midino>
            <options>
               <Button />
            </options>
         </control>
         <control>
            <group>[Sampler1]</group>
            <key>LoadSelectedTrack</key>
            <status>0x80</status>
            <midino>0x54</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control> <!-- Shifted knob press -->
            <group>[Sampler1]</group>
            <key>eject</key>
            <status>0x91</status>
            <midino>0x54</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- Shifted knob press -->
            <group>[Sampler1]</group>
            <key>eject</key>
            <status>0x81</status>
            <midino>0x54</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control>
            <group>[Sampler1]</group>
            <key>pregain</key>
            <status>0xB0</status>
            <midino>0x27</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control> <!-- on -->
            <group>[Sampler1]</group>
            <key>start_play</key>
            <status>0x90</status>
            <midino>0x1D</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- off -->
            <group>[Sampler1]</group>
            <key>start_play</key>
            <status>0x80</status>
            <midino>0x1D</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control> <!-- on -->
            <group>[Sampler1]</group>
            <key>start_stop</key>
            <status>0x91</status>
            <midino>0x1D</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- off -->
            <group>[Sampler1]</group>
            <key>start_stop</key>
            <status>0x81</status>
            <midino>0x1D</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <!-- 2 -->
         <control>
            <group>[Channel2]</group>
            <key>VMS4.sampleSelect</key>
            <status>0xB0</status>
            <midino>0x2B</midino>
            <options>
               <Script-Binding />
            </options>
         </control>
         <control>
            <group>[Sampler2]</group>
            <key>LoadSelectedTrack</key>
            <status>0x90</status>
            <midino>0x56</midino>
            <options>
               <Button />
            </options>
         </control>
         <control>
            <group>[Sampler2]</group>
            <key>LoadSelectedTrack</key>
            <status>0x80</status>
            <midino>0x56</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control> <!-- Shifted knob press -->
            <group>[Sampler2]</group>
            <key>eject</key>
            <status>0x91</status>
            <midino>0x56</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- Shifted knob press -->
            <group>[Sampler2]</group>
            <key>eject</key>
            <status>0x81</status>
            <midino>0x56</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control>
            <group>[Sampler2]</group>
            <key>pregain</key>
            <status>0xB0</status>
            <midino>0x2C</midino>
            <options>
                <soft-takeover />
            </options>
         </control>
         <control> <!-- on -->
            <group>[Sampler2]</group>
            <key>start_play</key>
            <status>0x90</status>
            <midino>0x3F</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- off -->
            <group>[Sampler2]</group>
            <key>start_play</key>
            <status>0x80</status>
            <midino>0x3F</midino>
            <options>
               <Spread64 />
            </options>
         </control>
         <control> <!-- on -->
            <group>[Sampler2]</group>
            <key>start_stop</key>
            <status>0x91</status>
            <midino>0x3F</midino>
            <options>
               <Button />
            </options>
         </control>
         <control> <!-- off -->
            <group>[Sampler2]</group>
            <key>start_stop</key>
            <status>0x81</status>
            <midino>0x3F</midino>
            <options>
               <Spread64 />
            </options>
         </control>
      </controls>
      <outputs>
        <output>
            <group>[Channel1]</group>
            <key>play_indicator</key>
            <status>0x90</status>
            <midino>0x0D</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>play_indicator</key>
            <status>0x90</status>
            <midino>0x2F</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <!-- Pause button lights when play isn't -->
            <group>[Channel1]</group>
            <key>play</key>
            <status>0x90</status>
            <midino>0x0E</midino>
            <maximum>0.5</maximum>
        </output>
        <output>
            <!-- Pause button lights when play isn't -->
            <group>[Channel2]</group>
            <key>play</key>
            <status>0x90</status>
            <midino>0x30</midino>
            <maximum>0.5</maximum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>cue_indicator</key>
            <status>0x90</status>
            <midino>0x0C</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>cue_indicator</key>
            <status>0x90</status>
            <midino>0x2E</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2C</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel3]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2A</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel4]</group>
            <key>pfl</key>
            <status>0x90</status>
            <midino>0x2D</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>rate_temp_down</key>
            <status>0x90</status>
            <midino>0x0F</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>rate_temp_down</key>
            <status>0x90</status>
            <midino>0x31</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>rate_temp_up</key>
            <status>0x90</status>
            <midino>0x10</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>rate_temp_up</key>
            <status>0x90</status>
            <midino>0x32</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>sync_enabled</key>
            <status>0x90</status>
            <midino>0x16</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>sync_enabled</key>
            <status>0x90</status>
            <midino>0x38</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>back</key>
            <status>0x90</status>
            <midino>0x28</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>fwd</key>
            <status>0x90</status>
            <midino>0x29</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>back</key>
            <status>0x90</status>
            <midino>0x4A</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>fwd</key>
            <status>0x90</status>
            <midino>0x4B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[EffectRack1_EffectUnit1]</group>
            <key>group_[Channel1]_enable</key>
            <status>0x90</status>
            <midino>0x1B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[EffectRack1_EffectUnit2]</group>
            <key>group_[Channel2]_enable</key>
            <status>0x90</status>
            <midino>0x3D</midino>
            <minimum>0.5</minimum>
        </output>
         <output>
            <group>[Channel1]</group>
            <key>keylock</key>
            <status>0x90</status>
            <midino>0x27</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>keylock</key>
            <status>0x90</status>
            <midino>0x49</midino>
            <minimum>0.5</minimum>
        </output>
        <!-- Hot cues -->
        <output>
            <group>[Channel1]</group>
            <key>hotcue_1_enabled</key>
            <status>0x90</status>
            <midino>0x12</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_2_enabled</key>
            <status>0x90</status>
            <midino>0x13</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_3_enabled</key>
            <status>0x90</status>
            <midino>0x14</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_4_enabled</key>
            <status>0x90</status>
            <midino>0x15</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_5_enabled</key>
            <status>0x90</status>
            <midino>0x17</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_6_enabled</key>
            <status>0x90</status>
            <midino>0x18</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_7_enabled</key>
            <status>0x90</status>
            <midino>0x19</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>hotcue_8_enabled</key>
            <status>0x90</status>
            <midino>0x1A</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_1_enabled</key>
            <status>0x90</status>
            <midino>0x34</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_2_enabled</key>
            <status>0x90</status>
            <midino>0x35</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_3_enabled</key>
            <status>0x90</status>
            <midino>0x36</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_4_enabled</key>
            <status>0x90</status>
            <midino>0x37</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_5_enabled</key>
            <status>0x90</status>
            <midino>0x39</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_6_enabled</key>
            <status>0x90</status>
            <midino>0x3A</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_7_enabled</key>
            <status>0x90</status>
            <midino>0x3B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>hotcue_8_enabled</key>
            <status>0x90</status>
            <midino>0x3C</midino>
            <minimum>0.5</minimum>
        </output>
        <!-- Looping -->
        <output>
            <group>[Channel1]</group>
            <key>loop_in</key>
            <status>0x90</status>
            <midino>0x1F</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>loop_out</key>
            <status>0x90</status>
            <midino>0x20</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>loop_enabled</key>
            <status>0x90</status>
            <midino>0x21</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>loop_halve</key>
            <status>0x90</status>
            <midino>0x28</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>loop_double</key>
            <status>0x90</status>
            <midino>0x29</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>beatloop_4</key>
            <status>0x90</status>
            <midino>0x22</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel1]</group>
            <key>quantize</key>
            <status>0x90</status>
            <midino>0x26</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>loop_in</key>
            <status>0x90</status>
            <midino>0x41</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>loop_out</key>
            <status>0x90</status>
            <midino>0x42</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>loop_enabled</key>
            <status>0x90</status>
            <midino>0x43</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>loop_halve</key>
            <status>0x90</status>
            <midino>0x4A</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>loop_double</key>
            <status>0x90</status>
            <midino>0x4B</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>beatloop_4</key>
            <status>0x90</status>
            <midino>0x44</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Channel2]</group>
            <key>quantize</key>
            <status>0x90</status>
            <midino>0x48</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Sampler1]</group>
            <key>play_indicator</key>
            <status>0x90</status>
            <midino>0x1D</midino>
            <minimum>0.5</minimum>
        </output>
        <output>
            <group>[Sampler2]</group>
            <key>play_indicator</key>
            <status>0x90</status>
            <midino>0x3F</midino>
            <minimum>0.5</minimum>
        </output>
      </outputs>
   </controller>
</MixxxMIDIPreset>
