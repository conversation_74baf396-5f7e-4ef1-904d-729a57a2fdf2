<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:osb="http://www.openswatchbook.org/uri/2009/osb"
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   width="32"
   height="32"
   id="svg2"
   inkscape:version="0.92.3 (unknown)"
   sodipodi:docname="ic_library_banshee.svg"
   style="display:inline"
   inkscape:export-filename=""
   inkscape:export-xdpi="135"
   inkscape:export-ydpi="135">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1366"
     inkscape:window-height="740"
     id="namedview26"
     showgrid="true"
     inkscape:zoom="7.9999996"
     inkscape:cx="2.189517"
     inkscape:cy="20.276123"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2"
     inkscape:object-paths="true"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:snap-midpoints="true">
    <inkscape:grid
       type="xygrid"
       id="grid3788"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       originx="4"
       spacingx="1"
       spacingy="1"
       color="#00ffe0"
       opacity="0.1254902"
       empcolor="#0072ff"
       empopacity="0.25098039"
       originy="4"
       dotted="false" />
  </sodipodi:namedview>
  <title
     id="title5647">Mixxx 1.12+ iconset</title>
  <defs
     id="defs28">
    <linearGradient
       id="linearGradient5515"
       osb:paint="solid">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop5517" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-6">
      <stop
         style="stop-color:#3c3c3c;stop-opacity:1;"
         offset="0"
         id="stop5697-6" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-7" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-4">
      <stop
         style="stop-color:#646464;stop-opacity:1;"
         offset="0"
         id="stop5697-7" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-73">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-6" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-5">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-9" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-5" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient4119"
       x1="12"
       y1="8"
       x2="12"
       y2="32"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       gradientTransform="matrix(1,0,0,0.83333334,5.000137,4.6879259)"
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient4121"
       x1="6.9998631"
       y1="3.9744895"
       x2="6.9998631"
       y2="32.77449"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       gradientTransform="matrix(0.02301235,0,0,0.03908629,13.31361,-2.8465107)"
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient4123"
       x1="-57.082829"
       y1="277.50168"
       x2="-57.082829"
       y2="891.52771"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <metadata
     id="metadata4">
    <rdf:RDF>
      <rdf:Description
         rdf:about="">
        <dc:title />
        <dc:creator />
        <dc:rights />
        <dc:description />
        <dc:format>image/svg+xml</dc:format>
        <dc:language>en</dc:language>
      </rdf:Description>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Mixxx 1.12+ iconset</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>Mixxx</rdf:li>
            <rdf:li>GUI</rdf:li>
            <rdf:li>Interface</rdf:li>
            <rdf:li>Icons</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:creator>
          <cc:Agent>
            <dc:title><EMAIL></dc:title>
          </cc:Agent>
        </dc:creator>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/publicdomain/" />
        <dc:date>2014-04-18</dc:date>
        <dc:source>www.mixxx.org</dc:source>
        <dc:description>Iconset for use in Mixxx 1.12s+. Optimized for 48px (16px) icon size. Contains icons for preference menu and library widget

Grid based on suggestions from http://techbase.kde.org/Projects/Oxygen/Style</dc:description>
        <dc:coverage />
        <dc:language>en</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/publicdomain/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <path
     sodipodi:type="arc"
     style="color:#000000;display:inline;overflow:visible;visibility:visible;fill:none;stroke:url(#linearGradient4123);stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker:none"
     id="path2250"
     sodipodi:cx="18"
     sodipodi:cy="17.999998"
     sodipodi:rx="10.999999"
     sodipodi:ry="10.999996"
     d="M 18.958713,7.0418602 A 10.999999,10.999996 0 0 1 28.490885,14.692235 10.999999,10.999996 0 0 1 25.070663,26.426484 10.999999,10.999996 0 0 1 12.920766,27.757114 10.999999,10.999996 0 0 1 7.0418593,17.041285"
     sodipodi:start="4.7996554"
     sodipodi:end="3.2288591"
     sodipodi:open="true" />
  <ellipse
     ry="5"
     rx="6"
     style="color:#000000;display:inline;overflow:visible;visibility:visible;fill:none;stroke:url(#linearGradient4121);stroke-width:3.99999976;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker:none;enable-background:accumulate"
     id="path4082"
     cx="18.000137"
     cy="18.021259" />
  <path
     style="display:inline;fill:none;stroke:url(#linearGradient4119);stroke-width:4;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     d="M 12,18 V 2.0000004 l -8,4"
     id="path4101"
     inkscape:connector-curvature="0"
     sodipodi:nodetypes="ccc" />
</svg>
