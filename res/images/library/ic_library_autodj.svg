<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:osb="http://www.openswatchbook.org/uri/2009/osb"
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   width="32"
   height="32"
   id="svg2"
   inkscape:version="0.92.3 (unknown)"
   sodipodi:docname="ic_library_autodj.svg"
   style="display:inline"
   inkscape:export-filename=""
   inkscape:export-xdpi="135"
   inkscape:export-ydpi="135">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1366"
     inkscape:window-height="740"
     id="namedview26"
     showgrid="true"
     inkscape:zoom="7.9999996"
     inkscape:cx="2.189517"
     inkscape:cy="20.276123"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2"
     inkscape:object-paths="true"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:snap-midpoints="true">
    <inkscape:grid
       type="xygrid"
       id="grid3788"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       originx="4"
       spacingx="1"
       spacingy="1"
       color="#00ffe0"
       opacity="0.1254902"
       empcolor="#0072ff"
       empopacity="0.25098039"
       originy="4"
       dotted="false" />
  </sodipodi:namedview>
  <title
     id="title5647">Mixxx 1.12+ iconset</title>
  <defs
     id="defs28">
    <linearGradient
       id="linearGradient5515"
       osb:paint="solid">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop5517" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-6">
      <stop
         style="stop-color:#3c3c3c;stop-opacity:1;"
         offset="0"
         id="stop5697-6" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-7" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-4">
      <stop
         style="stop-color:#646464;stop-opacity:1;"
         offset="0"
         id="stop5697-7" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-73">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-6" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-5">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-9" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-5" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient2134"
       x1="12"
       y1="2"
       x2="12"
       y2="30"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <metadata
     id="metadata4">
    <rdf:RDF>
      <rdf:Description
         rdf:about="">
        <dc:title />
        <dc:creator />
        <dc:rights />
        <dc:description />
        <dc:format>image/svg+xml</dc:format>
        <dc:language>en</dc:language>
      </rdf:Description>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Mixxx 1.12+ iconset</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>Mixxx</rdf:li>
            <rdf:li>GUI</rdf:li>
            <rdf:li>Interface</rdf:li>
            <rdf:li>Icons</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:creator>
          <cc:Agent>
            <dc:title><EMAIL></dc:title>
          </cc:Agent>
        </dc:creator>
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/publicdomain/" />
        <dc:date>2014-04-18</dc:date>
        <dc:source>www.mixxx.org</dc:source>
        <dc:description>Iconset for use in Mixxx 1.12s+. Optimized for 48px (16px) icon size. Contains icons for preference menu and library widget

Grid based on suggestions from http://techbase.kde.org/Projects/Oxygen/Style</dc:description>
        <dc:coverage />
        <dc:language>en</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/publicdomain/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     id="g17478"
     style="display:inline;fill:url(#linearGradient2134);fill-opacity:1">
    <path
       style="color:#000000;display:inline;overflow:visible;visibility:visible;opacity:1;fill:url(#linearGradient2134);fill-opacity:1.0;fill-rule:nonzero;stroke:none;stroke-width:2;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;marker:none;enable-background:accumulate"
       d="M 10,2 C 8.892,2 8,2.892 8,4 v 6 H 24 V 4 C 24,2.892 23.108,2 22,2 Z m 2.5,3 C 13.328427,5 14,5.6715729 14,6.5 14,7.3284271 13.328427,8 12.5,8 11.671573,8 11,7.3284271 11,6.5 11,5.6715729 11.671573,5 12.5,5 Z m 7,0 C 20.328427,5 21,5.6715729 21,6.5 21,7.3284271 20.328427,8 19.5,8 18.671573,8 18,7.3284271 18,6.5 18,5.6715729 18.671573,5 19.5,5 Z"
       id="rect17459"
       inkscape:connector-curvature="0" />
    <rect
       ry="1"
       rx="1"
       y="24"
       x="10"
       height="6"
       width="4"
       id="rect4796-5"
       style="display:inline;fill:url(#linearGradient2134);fill-opacity:1.0;stroke:none" />
    <rect
       ry="1"
       rx="1"
       style="display:inline;fill:url(#linearGradient2134);fill-opacity:1.0;stroke:none"
       id="rect4798-3"
       width="4"
       height="6"
       x="18"
       y="24" />
    <rect
       ry="1"
       rx="1"
       y="12"
       x="2"
       height="10"
       width="4"
       id="rect4305-0"
       style="color:#000000;display:inline;overflow:visible;visibility:visible;fill:url(#linearGradient2134);fill-opacity:1.0;fill-rule:nonzero;stroke:none;stroke-width:2;marker:none;enable-background:accumulate" />
    <path
       id="rect4319-1"
       d="M 8,12 V 13.15625 14 24.84375 C 8,25.490083 8.446,26 9,26 h 14 c 0.554,0 1,-0.509917 1,-1.15625 V 14 13.15625 12 H 23 9 Z"
       style="color:#000000;display:inline;overflow:visible;visibility:visible;fill:url(#linearGradient2134);fill-opacity:1.0;fill-rule:nonzero;stroke:none;stroke-width:2;marker:none;enable-background:accumulate"
       inkscape:connector-curvature="0" />
    <rect
       style="color:#000000;display:inline;overflow:visible;visibility:visible;fill:url(#linearGradient2134);fill-opacity:1.0;fill-rule:nonzero;stroke:none;stroke-width:2;marker:none;enable-background:accumulate"
       id="rect4330-9"
       width="4"
       height="10"
       x="26"
       y="12"
       rx="1"
       ry="1" />
  </g>
</svg>
