<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:osb="http://www.openswatchbook.org/uri/2009/osb"
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   width="10"
   height="12"
   id="svg2"
   inkscape:version="0.92.3 (unknown)"
   sodipodi:docname="ic_library_unlocked.svg"
   style="display:inline"
   inkscape:export-filename=""
   inkscape:export-xdpi="135"
   inkscape:export-ydpi="135">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1366"
     inkscape:window-height="740"
     id="namedview26"
     showgrid="true"
     inkscape:zoom="32"
     inkscape:cx="0.36633537"
     inkscape:cy="11.888466"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="ic_library_unchecked"
     inkscape:object-paths="true"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:snap-midpoints="true"
     inkscape:snap-page="true"
     inkscape:snap-bbox="true"
     inkscape:snap-bbox-midpoints="true"
     inkscape:bbox-nodes="true">
    <inkscape:grid
       type="xygrid"
       id="grid3788"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       originx="4"
       spacingx="1"
       spacingy="1"
       color="#00ffe0"
       opacity="0.1254902"
       empcolor="#0072ff"
       empopacity="0.25098039"
       originy="4"
       dotted="false" />
  </sodipodi:namedview>
  <title
     id="title5647">Mixxx 1.12+ iconset</title>
  <defs
     id="defs28">
    <linearGradient
       id="linearGradient5515"
       osb:paint="solid">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop5517" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-6">
      <stop
         style="stop-color:#3c3c3c;stop-opacity:1;"
         offset="0"
         id="stop5697-6" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-7" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-4">
      <stop
         style="stop-color:#646464;stop-opacity:1;"
         offset="0"
         id="stop5697-7" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-73">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-6" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-5">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-9" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-5" />
    </linearGradient>
  </defs>
  <metadata
     id="metadata4">
    <rdf:RDF>
      <rdf:Description
         rdf:about="">
        <dc:title />
        <dc:creator />
        <dc:rights />
        <dc:description />
        <dc:format>image/svg+xml</dc:format>
        <dc:language>en</dc:language>
      </rdf:Description>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Mixxx 1.12+ iconset</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>Mixxx</rdf:li>
            <rdf:li>GUI</rdf:li>
            <rdf:li>Interface</rdf:li>
            <rdf:li>Icons</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:creator>
          <cc:Agent>
            <dc:title><EMAIL></dc:title>
          </cc:Agent>
        </dc:creator>
        <cc:license
           rdf:resource="http://creativecommons.org/publicdomain/zero/1.0/" />
        <dc:date>2014-04-18</dc:date>
        <dc:source>www.mixxx.org</dc:source>
        <dc:description>Iconset for use in Mixxx 1.12s+. Optimized for 48px (16px) icon size. Contains icons for preference menu and library widget

Grid based on suggestions from http://techbase.kde.org/Projects/Oxygen/Style</dc:description>
        <dc:coverage />
        <dc:language>en</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/publicdomain/zero/1.0/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     style="display:inline"
     id="ic_library_crates"
     inkscape:label="#g6169"
     transform="translate(0,-20)">
    <g
       transform="matrix(0.415098,0,0,0.415098,-1.660392,19.54706)"
       inkscape:label="#g6145"
       id="ic_library_unchecked"
       style="display:inline">
      <path
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-feature-settings:normal;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;writing-mode:lr-tb;direction:ltr;text-orientation:mixed;dominant-baseline:auto;baseline-shift:baseline;text-anchor:start;white-space:normal;shape-padding:0;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;vector-effect:none;fill:#979797;fill-opacity:0.50196078;fill-rule:nonzero;stroke:none;stroke-width:3.61360455;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         d="M 15.988281,1.0917969 C 12.524375,1.1142616 10.159034,2.574097 8.9765625,4.3476562 7.7940914,6.1212155 7.685104,7.9580721 7.6933594,8.9140625 c 0.012852,1.4883375 0.00391,1.8133805 0.00391,1.8133805 l 3.6054686,0 c 0,0 0.01696,-0.33297 0.0039,-1.8446305 -0.0047,-0.5444583 0.116662,-1.6897123 0.677734,-2.53125 0.561072,-0.8415377 1.452465,-1.6297854 4.027344,-1.6464844 2.544129,-0.0165 3.365144,0.7272539 3.941406,1.6777344 0.576262,0.9504805 0.73828,2.3849416 0.740234,3.5175781 0.0026,1.4914694 0,6.1269534 0,6.1269534 l 3.613282,0.002 c 0,0 0.0026,-4.626234 0,-6.1347658 C 24.30428,8.5268698 24.215628,6.4439381 23.042969,4.5097656 21.870309,2.5755932 19.444152,1.0693844 15.988281,1.0917969 Z"
         id="path4298"
         inkscape:connector-curvature="0"
         sodipodi:nodetypes="ssscccssscccsss" />
      <path
         style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:Sans;-inkscape-font-specification:Sans;text-indent:0;text-align:start;text-decoration:none;text-decoration-line:none;letter-spacing:normal;word-spacing:normal;text-transform:none;writing-mode:lr-tb;direction:ltr;baseline-shift:baseline;text-anchor:start;display:inline;overflow:visible;visibility:visible;fill:#979797;fill-opacity:0.50196288;fill-rule:nonzero;stroke:none;stroke-width:2;marker:none;enable-background:accumulate"
         d="M 6.0075581,16 C 5.003779,16 4,17 4,18 v 10 c 0,1 1.003779,2 2.0075581,2 H 26.083139 c 1.003779,0 2.007558,-1 2.007558,-2 V 18 c 0,-1 -1.003779,-2 -2.007558,-2 z m 3.0427051,4.568256 H 23.071802 c 0.5241,0 1.000957,0.476401 1.000957,1 0,0.523599 -0.476857,1 -1.000957,1 H 9.0502632 c -0.5436301,0 -1.0351471,-0.52024 -1.0351471,-1 0,-0.47976 0.4854817,-1 1.0351471,-1 z m 0,4.81814 H 23.071802 c 0.5241,0 1.000957,0.476401 1.000957,1 0,0.523599 -0.476857,1 -1.000957,1 H 9.0502632 c -0.5533949,0 -1.0351471,-0.52024 -1.0351471,-1 0,-0.479761 0.4490535,-1 1.0351471,-1 z"
         id="path4300"
         inkscape:connector-curvature="0"
         sodipodi:nodetypes="ssssssssscssscaccssscac" />
    </g>
  </g>
</svg>
