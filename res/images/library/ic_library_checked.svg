<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:osb="http://www.openswatchbook.org/uri/2009/osb"
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   width="12"
   height="12"
   id="svg2"
   inkscape:version="0.92.3 (unknown)"
   sodipodi:docname="ic_library_checked.svg"
   style="display:inline"
   inkscape:export-filename=""
   inkscape:export-xdpi="135"
   inkscape:export-ydpi="135">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1366"
     inkscape:window-height="740"
     id="namedview26"
     showgrid="true"
     inkscape:zoom="22.627417"
     inkscape:cx="3.2949659"
     inkscape:cy="7.3566314"
     inkscape:window-x="0"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="ic_library_unchecked"
     inkscape:object-paths="true"
     showguides="true"
     inkscape:guide-bbox="true"
     inkscape:snap-midpoints="true"
     inkscape:snap-page="true"
     inkscape:snap-bbox="true"
     inkscape:snap-bbox-midpoints="true"
     inkscape:bbox-nodes="true">
    <inkscape:grid
       type="xygrid"
       id="grid3788"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       originx="4"
       spacingx="1"
       spacingy="1"
       color="#00ffe0"
       opacity="0.1254902"
       empcolor="#0072ff"
       empopacity="0.25098039"
       originy="4"
       dotted="false" />
  </sodipodi:namedview>
  <title
     id="title5647">Mixxx 1.12+ iconset</title>
  <defs
     id="defs28">
    <linearGradient
       id="linearGradient5515"
       osb:paint="solid">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop5517" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-6">
      <stop
         style="stop-color:#3c3c3c;stop-opacity:1;"
         offset="0"
         id="stop5697-6" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-7" />
    </linearGradient>
    <linearGradient
       id="linearGradient5695-4">
      <stop
         style="stop-color:#646464;stop-opacity:1;"
         offset="0"
         id="stop5697-7" />
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="1"
         id="stop5699-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-73">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-6" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-6" />
    </linearGradient>
    <linearGradient
       id="linearGradient4942-5">
      <stop
         style="stop-color:#ff6600;stop-opacity:1;"
         offset="0"
         id="stop4944-9" />
      <stop
         style="stop-color:#de5800;stop-opacity:1;"
         offset="1"
         id="stop4946-5" />
    </linearGradient>
    <linearGradient
       gradientTransform="translate(12,20)"
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient5559"
       gradientUnits="userSpaceOnUse"
       x1="28"
       y1="24"
       x2="28"
       y2="34" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4942"
       id="linearGradient5363"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(12,20)"
       x1="28"
       y1="24"
       x2="28"
       y2="34" />
  </defs>
  <metadata
     id="metadata4">
    <rdf:RDF>
      <rdf:Description
         rdf:about="">
        <dc:title />
        <dc:creator />
        <dc:rights />
        <dc:description />
        <dc:format>image/svg+xml</dc:format>
        <dc:language>en</dc:language>
      </rdf:Description>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Mixxx 1.12+ iconset</dc:title>
        <dc:subject>
          <rdf:Bag>
            <rdf:li>Mixxx</rdf:li>
            <rdf:li>GUI</rdf:li>
            <rdf:li>Interface</rdf:li>
            <rdf:li>Icons</rdf:li>
          </rdf:Bag>
        </dc:subject>
        <dc:creator>
          <cc:Agent>
            <dc:title><EMAIL></dc:title>
          </cc:Agent>
        </dc:creator>
        <cc:license
           rdf:resource="http://creativecommons.org/publicdomain/zero/1.0/" />
        <dc:date>2014-04-18</dc:date>
        <dc:source>www.mixxx.org</dc:source>
        <dc:description>Iconset for use in Mixxx 1.12s+. Optimized for 48px (16px) icon size. Contains icons for preference menu and library widget

Grid based on suggestions from http://techbase.kde.org/Projects/Oxygen/Style</dc:description>
        <dc:coverage />
        <dc:language>en</dc:language>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/publicdomain/zero/1.0/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <g
     style="display:inline"
     id="ic_library_crates"
     inkscape:label="#g6169"
     transform="translate(0,-20)">
    <g
       transform="matrix(0.415098,0,0,0.415098,-1.660392,19.54706)"
       inkscape:label="#g6145"
       id="ic_library_unchecked"
       style="display:inline">
      <g
         style="display:inline"
         id="style_checkbox_checked"
         inkscape:label="#g5541"
         transform="matrix(2.4090697,0,0,2.4090697,-44.181394,-47.09023)"
         inkscape:export-filename=""
         inkscape:export-xdpi="90"
         inkscape:export-ydpi="90">
        <desc
           id="desc4255">Checkbox in &quot;Played&quot; library column</desc>
        <rect
           style="fill:none;stroke:url(#linearGradient5363);stroke-width:0.99999994;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1"
           id="rect5533"
           width="11"
           height="11"
           x="20.5"
           y="20.5"
           inkscape:label="#rect5529" />
        <path
           sodipodi:nodetypes="ccc"
           inkscape:connector-curvature="0"
           d="m 23,27 2,2 4,-6"
           style="fill:none;stroke:url(#linearGradient5559);stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
           id="path142" />
      </g>
    </g>
  </g>
</svg>
